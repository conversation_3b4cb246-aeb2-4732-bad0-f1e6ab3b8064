<!DOCTYPE html>
<html>
<head>
    <title>WebSocket Test</title>
</head>
<body>
    <div>
        <input type="text" id="userId" placeholder="用户ID">
        <button onclick="connect()">连接</button>
    </div>
    <div>
        <select id="msgType">
            <option value="private">私聊</option>
            <option value="group">群聊</option>
        </select>
        <input type="text" id="targetId" placeholder="目标ID">
        <input type="text" id="message" placeholder="消息内容">
        <button onclick="sendMessage()">发送</button>
    </div>
    <div id="output"></div>

    <script>
        let ws;
        
        function connect() {
            const userId = document.getElementById('userId').value;
            ws = new WebSocket(`ws://localhost:18123/api/chat/ws/${userId}`);
            
            ws.onmessage = function(event) {
                const output = document.getElementById('output');
                output.innerHTML += `<p>收到: ${event.data}</p>`;
            };
            
            ws.onopen = function() {
                console.log('Connected');
            };
            
            ws.onerror = function(error) {
                console.error('WebSocket Error:', error);
            };
        }
        
        function sendMessage() {
            const type = document.getElementById('msgType').value;
            const targetId = parseInt(document.getElementById('targetId').value);
            const content = document.getElementById('message').value;
            
            const message = {
                type: type,
                content: content
            };
            
            if (type === 'private') {
                message.target_id = targetId;
            } else {
                message.group_id = targetId;
            }
            
            ws.send(JSON.stringify(message));
            
            const output = document.getElementById('output');
            output.innerHTML += `<p>发送: ${JSON.stringify(message)}</p>`;
        }
    </script>
</body>
</html> 