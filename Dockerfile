FROM registry.jxit.net.cn:5000/tiangolo/uvicorn-gunicorn-fastapi:python3.11

ENV TZ=Asia/Shanghai 

COPY ./requirements.txt /app/requirements.txt

RUN pip install --no-cache-dir --upgrade -r /app/requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple

RUN sed -i "s/deb.debian.org/mirrors.huaweicloud.com/g" /etc/apt/sources.list.d/debian.sources && \
    apt-get -y update && \
    apt-get -y install ffmpeg curl wget iproute2 psmisc tcpdump vim tzdata && \
    apt-get clean && \ 
    ln -fs /usr/share/zoneinfo/$TZ /etc/localtime && \
    dpkg-reconfigure -f noninteractive tzdata && \
    rm -rf /var/lib/apt/lists/*

COPY . /app
