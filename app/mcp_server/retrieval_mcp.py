from fastmcp import FastMCP
from typing import Annotated
from langchain_core.documents import Document
from app.utils.tools import search_database_file,search_faq
mcp = FastMCP("RAG Mcp Server")
mcp_app = mcp.http_app(path='/mcp')
@mcp.tool(
    name = "Information Retrieval Tool",
    description = "Useful when you need to get information from the database,chat history, or other relevant information."
)
async def retrieve_information(
    query: Annotated[str, "The query to retrieve information from the database,chat history, or other relevant information."],
    group_id: Annotated[str, "Group ID"],
    top_k: Annotated[int, "The number of results to return."] = 5
):
    database_res = await search_database_file(query,group_id)
    faq_res = await search_faq(query, group_id)
    # 混合排序
    all_docs = database_res + faq_res
    sorted_docs = sorted(all_docs, key=lambda x: x.metadata['score'] if hasattr(x,'metadata') else x["score"], reverse=True)
    result = []
    for doc in sorted_docs:
        if isinstance(doc, Document):
            if doc.metadata.get('score') == 1:
                # 满分片段无视top_k限制
                result.append({"content": doc.page_content})
            elif len(result) < top_k:
                result.append({"content": doc.page_content})
        else:
            if doc.get('score') == 1:
                # 满分片段无视top_k限制
                result.append(doc)
            elif len(result) < top_k:
                result.append(doc)
    return result

if __name__ == "__main__":
    mcp.run(transport="streamable-http")