import random
import string
import uuid
from datetime import datetime, timedelta

from fastapi import APIRouter, BackgroundTasks, Depends, UploadFile

from ..config import settings
from ..database import get_database
from ..models.user import User
from ..schemas.email import EmailContent
from ..schemas.group import GroupCreate
from ..schemas.phone import PhoneContent
from ..schemas.response import error, success
from ..schemas.user import (
    ResetPasswordForm,
    ResetPasswordSendCode,
    UserCreate,
    UserEmailLogin,
    UserLoginResponse,
    UserPhoneLogin,
    UserResponse,
    UserSendCodeResponse,
)
from ..utils.email_utils import send_verification_email
from ..utils.log import logger
from ..utils.phone_utils import send_verification_phone
from ..utils.security import (
    EncryptedAPIRoute,
    create_access_token,
    get_password_hash,
    verify_password,
)
from .file_route import upload_head
from .group_route import create_group
from ..utils.locales import get_translator

router = APIRouter(route_class=EncryptedAPIRoute)


@router.get("/confirm", description="前端配置代理确认接口")
async def confirm(_ = Depends(get_translator)):
    try:
        return success(message=_("通讯成功"))
    except Exception as e:
        return success(message=_("通讯成功"))


@router.post("/register/send-email-code", description="发送邮箱验证码")
async def send_verification_email_code(
    user_data: EmailContent, db=Depends(get_database), _ = Depends(get_translator)
):
    try:
        # 检查邮箱是否已注册
        user = await db.users.find_one({"email": user_data.email, "is_active": True})
        if user:
            res = error(code=409, message=_("邮箱已注册"))
            return res

        # 生成验证码
        code = "".join(random.choices(string.digits, k=6))

        # 创建临时用户记录存储验证码
        temp_user = User(
            username="",
            email=user_data.email,
            verification_code=code,
            hashed_password="",
            avatar=None,
        )

        await db.users.insert_one(temp_user.model_dump())

        # 发送验证码邮件
        await send_verification_email(user_data.email, code)
        return success(message=_("验证码已发送"))
    except Exception as e:
        logger.error(f"发送邮件验证码接口{user_data}出错: {str(e)}", exc_info=True)
        return error(code=500, message=_("服务端遇到未知异常：{}").format(str(e)))


@router.post("/send-phone-code", description="发送手机验证码")
async def send_verification_phone_code(
    user_data: PhoneContent, db=Depends(get_database), _ = Depends(get_translator)
):
    try:
        # return error(code=500, message="短信网关暂未开通")
        # 生成验证码
        code = "".join(random.choices(string.digits, k=6))
        user = await db.users.find_one({"phone": user_data.phone, "is_active": True})
        if user:
            # 已注册的用户写入验证码等待登录
            await db.users.update_one(
                {"phone": user_data.phone, "id": user["id"]},
                {"$set": {"verification_code": code}},
            )
        else:
            # 创建临时用户记录存储验证码
            new_user = User(
                username="",
                email=None,
                phone=user_data.phone,
                verification_code=code,
                hashed_password="",
                avatar=None,
            )
            user = new_user.model_dump()
            await db.users.insert_one(user)

        # 发送短信验证码
        await send_verification_phone(user_data.phone, code)
        return success(message=_("验证码已发送"), data=UserSendCodeResponse(**user))
    except Exception as e:
        logger.error(f"发送短信验证码接口{user_data}出错: {str(e)}", exc_info=True)
        return error(code=500, message=_("服务端遇到未知异常：{}").format(str(e)))


@router.post("/login-phone", description="用户通过手机验证码登录(如未注册则自动注册)")
async def phone_register(
    background_tasks: BackgroundTasks,
    user_data: UserPhoneLogin,
    db=Depends(get_database),
    _ = Depends(get_translator)
):
    try:
        # 检查手机号是否已注册
        user = await db.users.find_one({"id": user_data.id, "is_active": True})
        if not user:
            # 用户未注册时，进行注册
            temp_user = await db.users.find_one(
                {
                    "id": user_data.id,
                    "phone": user_data.phone,
                    "verification_code": user_data.verification_code,
                    "is_active": False,
                }
            )

            if not temp_user:
                return error(code=422, message=_("验证码错误"))

            # 手机注册默认使用手机号+uuid前4位作为用户名
            username = f"{user_data.phone}_{temp_user['id'][:4]}"
            update_data = {
                "$set": {
                    "username": username,
                    "hashed_password": "",
                    "phone": user_data.phone,
                    "is_active": True,
                    "verification_code": None,
                    "friends": [user_data.id],
                }
            }

            await db.users.update_one({"_id": temp_user["_id"]}, update_data)
            updated_user = await db.users.find_one({"_id": temp_user["_id"]})

            # 创建用户默认群聊及知识库
            await create_group(
                GroupCreate(
                    name=settings.DEFAULT_GROUP_NAME,
                    database_ids=[],
                    member_ids=[user_data.id],
                    owner_id=user_data.id,
                    agent_setting={
                        "abbr": [{"origin": "", "abbr": ""}],
                        "model": "",
                        "quality_check": True,
                        "role": "",
                        "integrate": False,
                    },
                ),
                current_user=User(**updated_user),
                db=db,
                _=_
            )
            # 上传默认头像
            # 通过调头像上传接口函数上传static/default.png
            await upload_head(
                background_tasks=background_tasks,
                file=UploadFile(
                    filename="default_avatar.png",
                    file=open("static/default_avatar.png", "rb"),
                ),
                current_user=User(**updated_user),
                db=db,
                _=_
            )
            user = updated_user
            if user_data.group_id:
                # 群跳转注册直接拉入群
                await db.groups.update_one(
                    {"id": user_data.group_id},
                    {"$push": {"member_ids": user_data.id}},
                )
        else:
            # 用户已注册
            user = await db.users.find_one(
                {
                    "id": user_data.id,
                    "phone": user_data.phone,
                    "verification_code": user_data.verification_code,
                    "is_active": True,
                }
            )

            if not user:
                return error(code=422, message=_("验证码错误"))

            await db.users.update_one(
                {"id": user_data.id}, {"$set": {"verification_code": None}}
            )

        # 生成访问令牌
        access_token = create_access_token(data={"uuid": str(user["id"])})
        user["access_token"] = access_token
        user["token_type"] = "bearer"

        return success(data=UserLoginResponse(**user))

    except Exception as e:
        logger.error(f"注册接口{user_data}出错: {str(e)}", exc_info=True)
        return error(code=500, message=_("服务端遇到未知异常：{}").format(str(e)))


@router.post("/register", description="邮件验证并完成注册")
async def email_register(
    background_tasks: BackgroundTasks, user_data: UserCreate, db=Depends(get_database), _ = Depends(get_translator)
):
    try:
        # 再次校验用户名
        existing_username = await db.users.find_one(
            {"username": user_data.username, "is_active": True}
        )
        if existing_username and existing_username["email"] != user_data.email:
            return error(code=409, message=_("用户名已被使用"))
        if user_data.phone not in ["",None]:
            # 填写手机号时对手机号进行去重校验
            existing_phone = await db.users.find_one(
                {"phone": user_data.phone, "is_active": True}
            )
            if existing_phone:
                return error(code=409, message=_("手机号已注册"))
        # 验证验证码
        temp_user = await db.users.find_one(
            {
                "email": user_data.email,
                "verification_code": user_data.verification_code,
                "is_active": False,
            }
        )

        if not temp_user:
            return error(code=422, message=_("验证码错误"))

        # 更新用户信息
        user_id = str(uuid.uuid4())
        update_data = {
            "$set": {
                "id": user_id,
                "username": user_data.username,
                "hashed_password": get_password_hash(user_data.password),
                "phone": user_data.phone,
                "is_active": True,
                "verification_code": None,
                "friends": [user_id],
            }
        }

        await db.users.update_one({"_id": temp_user["_id"]}, update_data)
        updated_user = await db.users.find_one({"_id": temp_user["_id"]})

        # 创建用户默认群聊及知识库
        group_data = await create_group(
            GroupCreate(
                name=settings.DEFAULT_GROUP_NAME,
                database_ids=[],
                member_ids=[user_id],
                owner_id=user_id,
                agent_setting={
                    "abbr": [{"origin": "", "abbr": ""}],
                    "model": "",
                    "quality_check": True,
                    "role": "",
                    "integrate": False,
                },
            ),
            current_user=User(**updated_user),
            db=db,
            _=_
        )
        # 上传默认头像
        # 通过调头像上传接口函数上传static/default.png
        await upload_head(
            background_tasks=background_tasks,
            file=UploadFile(
                filename="default_avatar.png",
                file=open("static/default_avatar.png", "rb"),
            ),
            current_user=User(**updated_user),
            db=db,
            _=_
        )
        if user_data.group_id:
            # 群跳转注册直接拉入群
            await db.groups.update_one(
                {"id": user_data.group_id},
                {"$push": {"member_ids": user_id}},
            )
        return success(data=UserResponse(**updated_user), message=_("注册成功"))
    except Exception as e:
        logger.error(f"注册接口{user_data}出错: {str(e)}", exc_info=True)
        return error(code=500, message=_("服务端遇到未知异常：{}").format(str(e)))


@router.post("/login", description="用户通过邮箱账号密码登录")
async def login(user_data: UserEmailLogin, db=Depends(get_database), _ = Depends(get_translator)):
    try:
        # 查找用户
        user = await db.users.find_one({"email": user_data.email, "is_active": True})
        if not user:
            return error(code=401, message=_("用户不存在"))
        if not verify_password(user_data.password, user["hashed_password"]):
            return error(code=401, message=_("邮箱或密码错误"))

        if not user["is_active"]:
            return error(code=401, message=_("账号未激活"))

        # 生成访问令牌
        access_token = create_access_token(data={"uuid": str(user["id"])})
        user["access_token"] = access_token
        user["token_type"] = "bearer"
        return success(data=UserLoginResponse(**user).model_dump())
    except Exception as e:
        logger.error(f"登录接口{user_data}出错: {str(e)}", exc_info=True)
        return error(code=500, message=_("服务端遇到未知异常：{}").format(str(e)))


@router.post("/password/send-code", description="重置密码-发送验证码")
async def send_password_reset_code(
    user_data: ResetPasswordSendCode, db=Depends(get_database), _ = Depends(get_translator)
):
    try:
        user = await db.users.find_one({"email": user_data.email, "is_active": True})
        if not user:
            return error(code=404, message=_("用户不存在"))
        # 生成6位数字验证码
        code = "".join(random.choices(string.digits, k=6))

        # 更新用户验证码和过期时间（5分钟有效）
        await db.users.update_one(
            {"email": user_data.email, "is_active": True},
            {
                "$set": {
                    "password_reset_code": code,
                    "reset_code_expiry": datetime.now() + timedelta(minutes=5),
                }
            },
        )

        # 发送验证码邮件（复用现有邮件发送逻辑）
        await send_verification_email(user_data.email, code)
        return success(message=_("验证码已发送"))
    except Exception as e:
        logger.error(f"发送密码重置验证码出错: {str(e)}", exc_info=True)
        return error(code=500, message=_("服务端遇到未知异常：{}").format(str(e)))


@router.post("/password/reset", description="用户重置密码")
async def password_reset(reset_data: ResetPasswordForm, db=Depends(get_database), _ = Depends(get_translator)):
    try:
        user = await db.users.find_one({"email": reset_data.email, "is_active": True})
        if not user:
            return error(code=404, message=_("用户不存在"))

        user = await db.users.find_one(
            {
                "email": reset_data.email,
                "is_active": True,
                "password_reset_code": reset_data.verification_code,
                "reset_code_expiry": {"$gt": datetime.now()},
            }
        )

        if not user:
            return error(code=422, message=_("验证码错误或已过期"))

        # 更新密码并清理验证码字段
        await db.users.update_one(
            {"email": reset_data.email, "is_active": True},
            {
                "$set": {
                    "hashed_password": get_password_hash(reset_data.new_password),
                    "password_reset_code": None,
                    "reset_code_expiry": None,
                }
            },
        )

        return success(message=_("密码重置成功"))
    except Exception as e:
        logger.error(f"{reset_data.email}用户重置验证码出错: {str(e)}", exc_info=True)
        return error(code=500, message=_("服务端遇到未知异常：{}").format(str(e)))


# @router.post("/password/modify", description="修改用户密码")
# async def reset_password(
#     password_data: dict,
#     current_user: User = Depends(get_current_user),
#     db=Depends(get_database),
#     token: str = Depends(oauth2_scheme)
# ):
#     try:
#         # 验证请求参数
#         if "new_password" not in password_data or "verification_code" not in password_data:
#             return error(code=400, message="缺少必要参数")

#         # 验证验证码
#         user = await db.users.find_one({
#             "id": current_user.id,
#             "password_reset_code": password_data["verification_code"],
#             "reset_code_expiry": {"$gt": datetime.now()}
#         })

#         if not user:
#             return error(code=422, message="验证码错误或已过期")

#         # 更新密码并清理验证码字段
#         await db.users.update_one(
#             {"id": current_user.id},
#             {"$set": {
#                 "hashed_password": get_password_hash(password_data["new_password"]),
#                 "password_reset_code": None,
#                 "reset_code_expiry": None
#             }}
#         )

#         # 使当前token失效
#         payload = jwt.decode(token, settings.SECRET_KEY,
#                              algorithms=[security.ALGORITHM])
#         expired_at = datetime.fromtimestamp(payload["exp"])
#         blacklist_token = TokenBlacklist(
#             token=token, user_id=current_user.id, expired_at=expired_at
#         )
#         await db.token_blacklist.insert_one(blacklist_token.model_dump())

#         return success(message="密码修改成功")
#     except Exception as e:
#         logger.error(f"密码修改接口出错: {str(e)}", exc_info=True)
#         return error(code=500, message=f"服务端遇到未知异常：{str(e)}")
