import re
import json
from datetime import datetime

from fastapi import APIRouter, Depends
from jose import jwt

from ..config import settings
from ..database import get_database
from ..models.token_blacklist import TokenBlacklist
from ..models.user import User
from ..schemas.response import PaginationModel, error, success
from ..schemas.user import (
    SearchFriend,
    UserFriendHandle,
    UserFriendRequest,
    UserFriendRequestResponse,
    UserQuery,
    UserResponse,
    UserSearch,
    UserUpdate,
)
from ..utils import security
from ..utils.auth import get_current_user, oauth2_scheme
from ..utils.log import logger
from ..utils.websocket_manager import connection_dic
from ..utils.locales import get_translator

router = APIRouter()


@router.post("/search", description="搜索用户信息")
async def search_user(
    search_data: UserSearch,
    current_user: User = Depends(get_current_user),
    db=Depends(get_database),
    _ = Depends(get_translator),
):
    try:
        # 构建查询条件
        query = {}
        if search_data.id:
            query["id"] = search_data.id
        if search_data.username:
            query["username"] = {
                "$regex": search_data.username,
                "$options": "i",
            }  # i 表示不区分大小写
        if search_data.phone:
            query["phone"] = search_data.phone
        if search_data.email:
            query["email"] = search_data.email
        if query == {}:
            return error(code=404, message=_("未查询到用户信息"))
        query["is_active"] = True
        # 执行查询
        users = await db.users.find(query).to_list(length=None)

        if not users:
            return error(code=404, message=_("未查询到用户信息"))

        for user in users:
            if user["id"] in current_user.friends:
                user["is_friend"] = True
            else:
                user["is_friend"] = False

        return success(data=UserResponse(**users[0]))
    except Exception as e:
        logger.error(f"查询用户信息接口出错: {str(e)}", exc_info=True)
        return error(code=500, message=_('服务端遇到未知异常：{}').format(str(e)))


@router.post("/query", description="精确搜索用户信息")
async def query_user(
    query_data: UserQuery,
    current_user: User = Depends(get_current_user),
    db=Depends(get_database),
    _ = Depends(get_translator),
):
    try:
        users = await db.users.find(
            {
                "$or": [
                    # 模糊匹配搜索用户名、手机号、邮箱
                    {"username": {"$eq": query_data.query_str}},
                    {"phone": {"$eq": query_data.query_str}},
                    {"email": {"$eq": query_data.query_str}},
                ],
                "is_active": True,
            }
        ).to_list(length=None)
        for user in users:
            if user["id"] in current_user.friends:
                user["is_friend"] = True
            else:
                user["is_friend"] = False
        return success(
            data=(
                []
                if len(users) == 0
                else list({item["id"]: UserResponse(**item) for item in users}.values())
            )
        )
    except Exception as e:
        logger.error(f"查询用户信息接口出错: {str(e)}", exc_info=True)
        return error(code=500, message=_('服务端遇到未知异常：{}').format(str(e)))


# 修改用户信息


@router.put("/{user_id}", description="修改用户信息")
async def update_user(
    user_id: str,
    user_data: UserUpdate,
    current_user: User = Depends(get_current_user),
    db=Depends(get_database),
    _ = Depends(get_translator),
):
    try:
        if current_user.id != user_id:
            logger.warning(f"用户{current_user.id}尝试修改{user_id}的信息")
            return error(code=403, message=_("无权修改信息"))
        user = await db.users.find_one({"id": user_id, "is_active": True})
        if not user:
            return error(code=404, message=_("未查询到用户信息"))

        # 检查用户名是否重复
        existing_username = await db.users.find_one(
            {"username": user_data.username, "is_active": True}
        )
        if existing_username and existing_username["id"] != user_id:
            return error(code=409, message=_("用户名已被使用"))

        if user_data.username == settings.DEFAULT_GROUP_NAME:
            return error(code=409, message=_("用户名已被使用"))
        # 修改用户名
        modify_data = {"username": user_data.username}
        if user_data.phone != current_user.phone:
            exist_phone = await db.users.find_one(
                {"phone": user_data.phone, "is_active": True}
            )
            if exist_phone:
                return error(code=409, message=_("手机号已被使用"))
            modify_data["phone"] = user_data.phone
        # 更新用户信息
        await db.users.update_one({"id": user_id}, {"$set": modify_data})

        return success(message=_("更新成功"))
    except Exception as e:
        logger.error(f"更新用户信息接口出错: {str(e)}", exc_info=True)
        return error(code=500, message=_('服务端遇到未知异常：{}').format(str(e)))


# 删除好友
@router.delete("/friend/{friend_id}", description="删除好友")
async def delete_friend(
    friend_id: str,
    current_user: User = Depends(get_current_user),
    db=Depends(get_database),
    _ = Depends(get_translator),
):
    try:
        if friend_id == current_user.id:
            return error(code=403, message=_("不能删除自己"))
        # 检查目标用户是否存在
        friend = await db.users.find_one(
            {
                "id": friend_id,
                "is_active": True,
            }
        )
        if not friend:
            return error(code=404, message=_("目标用户不存在"))

        # 检查是否已经是好友
        if friend_id not in current_user.friends:
            return error(code=403, message=_("该用户不是您的好友"))

        await db.users.update_one(
            {"id": current_user.id}, {"$pull": {"friends": friend_id}}
        )
        # TODO:直接互删？
        await db.users.update_one(
            {"id": friend_id}, {"$pull": {"friends": current_user.id}}
        )
        return success(message=_("删除成功"))

    except Exception as e:
        logger.error(f"删除好友接口出错: {str(e)}", exc_info=True)
        return error(code=500, message=_('服务端遇到未知异常：{}').format(str(e)))


@router.get("/friend/request/{friend_id}", description="发送好友请求")
async def send_friend_request(
    friend_id: str,
    current_user: User = Depends(get_current_user),
    db=Depends(get_database),
    _ = Depends(get_translator),
):
    try:
        if friend_id == current_user.id:
            return error(code=403, message=_("不能申请添加自己"))
        send_time = datetime.strftime(datetime.now(), "%Y-%m-%d %H:%M:%S")
        # 检查目标用户是否存在
        friend = await db.users.find_one({"id": friend_id, "is_active": True})
        if not friend:
            return error(code=404, message=_("目标用户不存在"))

        # 检查是否已经是好友
        if friend_id in current_user.friends:
            return error(code=409, message=_("该用户已经是您的好友"))

        # 检查是否已经发送过请求
        for request in friend["friend_requests"]:
            if (
                request["request_type"] == "from"
                and request["user_id"] == current_user.id
                and request["status"] == "pending"
            ):
                return error(code=409, message=_("您已经发送过好友请求"))

        # 添加好友请求
        from_friend_request = UserFriendRequest(
            request_type="from",
            user_id=current_user.id,
            username=current_user.username,
            avatar=current_user.avatar,
            status="pending",  # pending, accepted, rejected, sended
            created_at=send_time,
            handled_at=None,
        )

        to_friend_request = UserFriendRequest(
            request_type="to",
            user_id=friend_id,
            username=friend["username"],
            avatar=friend["avatar"],
            status="sended",
            created_at=send_time,
            handled_at=None,
        )

        # 接收方新增好友请求记录
        await db.users.update_one(
            {"id": friend_id},
            {"$push": {"friend_requests": from_friend_request.model_dump()}},
        )

        # 发起方新增好友请求记录
        await db.users.update_one(
            {"id": current_user.id},
            {"$push": {"friend_requests": to_friend_request.model_dump()}},
        )
        await connection_dic.broadcast([friend_id],json.dumps({"type":"refresh_friend_request_count"}))
        return success(message=_("好友请求已发送"))

    except Exception as e:
        logger.error(f"发送好友请求接口出错: {str(e)}", exc_info=True)
        return error(code=500, message=_('服务端遇到未知异常：{}').format(str(e)))


@router.post("/friend/request_list/list", description="获取好友请求列表")
async def get_friend_request_list(
    user_search: SearchFriend,
    current_user: User = Depends(get_current_user),
    db=Depends(get_database),
    _ = Depends(get_translator),
):
    try:
        if user_search.search is None:
            if user_search.page_size > 50:
                return error(code=422, message=_("每页数量不能超过50"))
            # 获取用户数据
            user_data = await db.users.find_one(
                {
                    "id": current_user.id,
                    "is_active": True,
                }
            )

            # 计算分页
            start = (user_search.page - 1) * user_search.page_size
            end = start + user_search.page_size

            # 对好友请求按时间倒序排序
            friend_requests = sorted(
                user_data["friend_requests"],
                key=lambda x: x["created_at"],
                reverse=True,
            )[start:end]

            return success(
                data=PaginationModel(
                    total=len(user_data["friend_requests"]),
                    page=user_search.page,
                    page_size=user_search.page_size,
                    items=[
                        UserFriendRequestResponse(**request)
                        for request in friend_requests
                    ],
                )
            )
        else:
            escaped_name = re.escape(user_search.search)
            regex_pattern = f".*{escaped_name}.*"

            if user_search.page_size > 50:
                return error(code=422, message=_("每页数量不能超过50"))
            # 获取用户数据
            user_data = await db.users.find_one(
                {
                    "id": current_user.id,
                    "is_active": True,
                }
            )

            # 计算分页
            start = (user_search.page - 1) * user_search.page_size
            end = start + user_search.page_size

            # 对符合要求的好友请求按时间倒序排序
            friend_requests = sorted(
                [
                    request
                    for request in user_data["friend_requests"]
                    if re.search(regex_pattern, request["username"], re.IGNORECASE)
                ],
                key=lambda x: x["created_at"],
                reverse=True,
            )[start:end]

            return success(
                data=PaginationModel(
                    total=len(user_data["friend_requests"]),
                    page=user_search.page,
                    page_size=user_search.page_size,
                    items=[
                        UserFriendRequestResponse(**request)
                        for request in friend_requests
                    ],
                )
            )
    except Exception as e:
        logger.error(f"获取好友请求列表接口出错: {str(e)}", exc_info=True)
        return error(code=500, message=_('服务端遇到未知异常：{}').format(str(e)))


@router.post("/friend/handle", description="好友申请审核")
async def handle_friend_request(
    request_data: UserFriendHandle,
    current_user: User = Depends(get_current_user),
    db=Depends(get_database),
    _ = Depends(get_translator),
):
    try:
        handle_time = datetime.strftime(datetime.now(), "%Y-%m-%d %H:%M:%S")
        if request_data.action not in ["accept", "reject"]:
            return error(code=400, message=_("无效的操作类型"))

        # 查找好友请求
        request = None
        for req in current_user.friend_requests:
            if req["status"] == "pending" and req["user_id"] == request_data.friend_id:
                request = req
                break

        if not request:
            return error(code=404, message=_("未找到该好友请求"))

        if request_data.action == "accept":
            # 互相添加好友
            await db.users.update_one(
                {"id": current_user.id},
                {
                    "$push": {"friends": request_data.friend_id},
                    # 更新好友请求状态
                    "$set": {
                        "friend_requests.$[elem].status": request_data.action,
                        "friend_requests.$[elem].handled_at": handle_time,
                    },
                },
                array_filters=[
                    {
                        "elem.user_id": request_data.friend_id,
                        "elem.request_type": "from",
                        "elem.status": "pending",
                    }
                ],
            )

            await db.users.update_one(
                {"id": request_data.friend_id},
                {
                    "$push": {"friends": current_user.id},
                    "$set": {
                        "friend_requests.$[elem].status": request_data.action,
                        "friend_requests.$[elem].handled_at": handle_time,
                    },
                },
                array_filters=[
                    {
                        "elem.user_id": current_user.id,
                        "elem.request_type": "to",
                        "elem.status": "sended",
                    }
                ],
            )
            result = _("已接受好友请求")
        else:
            # 拒绝好友请求，更新请求状态
            await db.users.update_one(
                {"id": current_user.id},
                {
                    "$set": {
                        "friend_requests.$[elem].status": request_data.action,
                        "friend_requests.$[elem].handled_at": handle_time,
                    }
                },
                array_filters=[
                    {
                        "elem.user_id": request_data.friend_id,
                        "elem.request_type": "from",
                        "elem.status": "pending",
                    }
                ],
            )

            await db.users.update_one(
                {"id": request_data.friend_id},
                {
                    "$set": {
                        "friend_requests.$[elem].status": request_data.action,
                        "friend_requests.$[elem].handled_at": handle_time,
                    }
                },
                array_filters=[
                    {
                        "elem.user_id": current_user.id,
                        "elem.requests_type": "to",
                        "elem.status": "sended",
                    }
                ],
            )
            result = _("已拒绝好友请求")

        await connection_dic.broadcast([current_user.id],json.dumps({"type":"refresh_friend_request_count"}))
        return success(message=result)

    except Exception as e:
        logger.error(f"处理好友请求接口出错: {str(e)}", exc_info=True)
        return error(code=500, message=_('服务端遇到未知异常：{}').format(str(e)))


@router.get("/friends", description="查询好友列表")
async def get_friends(
    search: str = None,
    current_user: User = Depends(get_current_user),
    db=Depends(get_database),
    _ = Depends(get_translator),
):
    try:
        friends = []
        if search == "[object Object]":
            search = None
        if not search:
            # 在搜索为空时，返回所有好友
            for friend_id in list(set(current_user.friends)):
                friend = await db.users.find_one(
                    {
                        "id": friend_id,
                        "is_active": True,
                    }
                )
                if friend:
                    if friend_id in current_user.friends:
                        friend["is_friend"] = True
                    else:
                        friend["is_friend"] = False
                    friends.append(
                        UserResponse(**friend).model_dump(
                            exclude=["user_setting", "user_service"]
                        )
                    )
        else:
            friends = await db.users.find(
                {
                    "$or": [
                        # 模糊匹配搜索用户名、手机号、邮箱
                        {"username": {"$regex": search, "$options": "i"}},
                        {"phone": {"$regex": search, "$options": "i"}},
                        {"email": {"$regex": search, "$options": "i"}},
                    ],
                    "is_active": True,
                }
            ).to_list(length=None)
            for friend in friends:
                if friend["id"] in current_user.friends:
                    friend["is_friend"] = True
                else:
                    friend["is_friend"] = False
            friends = [
                UserResponse(**friend).model_dump(
                    exclude=["user_setting", "user_service"]
                )
                for friend in friends
                if friend["id"] in current_user.friends
            ]

        return success(data=friends)

    except Exception as e:
        logger.error(f"获取好友列表接口出错: {str(e)}", exc_info=True)
        return error(code=500, message=_('服务端遇到未知异常：{}').format(str(e)))


@router.delete("/logout", description="注销用户")
async def send_verification_code(
    current_user: User = Depends(get_current_user), db=Depends(get_database), _ = Depends(get_translator)
):
    try:

        user = await db.users.find_one({"id": current_user.id, "is_active": True})
        if not user:
            return error(code=404, message=_("用户不存在或已注销"))
        await db.users.update_one(
            {"id": current_user.id}, {"$set": {"is_active": False}}
        )
        return success(message=_("注销成功"))
    except Exception as e:
        logger.error(f"注销接口出错: {str(e)}", exc_info=True)
        return error(code=500, message=_('服务端遇到未知异常：{}').format(str(e)))


@router.post("/invalidate-token", description="使当前token失效")
async def invalidate_token(
    current_user: User = Depends(get_current_user),
    token: str = Depends(oauth2_scheme),
    db=Depends(get_database),
    _ = Depends(get_translator),
):
    try:
        # 解析token获取过期时间
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[security.ALGORITHM]
        )
        expired_at = datetime.fromtimestamp(payload["exp"])

        # 将token加入黑名单
        blacklist_token = TokenBlacklist(
            token=token, user_id=current_user.id, expired_at=expired_at
        )
        await db.token_blacklist.insert_one(blacklist_token.model_dump())

        return success(message=_("token已失效"))
    except Exception as e:
        logger.error(f"使token失效接口出错: {str(e)}", exc_info=True)
        return error(code=500, message=_('服务端遇到未知异常：{}').format(str(e)))


# 获取未审核的好友请求数量
@router.get("/friend_requests/count", description="获取未审核的好友请求数量")
async def get_friend_requests_count(
    current_user: User = Depends(get_current_user), db=Depends(get_database), _ = Depends(get_translator)
):
    try:
        # 获取好友请求数量
        user = await db.users.find_one(
            {
                "id": current_user.id,
                "is_active": True,
            }
        )
        if not user:
            return error(code=404, message=_("用户不存在"))

        return success(
            data={
                "count": len(
                    [
                        request
                        for request in user["friend_requests"]
                        if request["status"] == "pending"
                    ]
                )
            }
        )
    except Exception as e:
        logger.error(f"获取好友请求数量接口出错: {str(e)}", exc_info=True)
        return error(code=500, message=_('服务端遇到未知异常：{}').format(str(e)))
