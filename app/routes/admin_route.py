import asyncio

import pandas as pd
from fastapi import APIRouter, Depends, UploadFile

from ..database import get_database
from ..models.user import User
from ..utils.admin import AdminServer
from ..utils.auth import get_current_user
from ..utils.locales import get_translator

router = APIRouter()


@router.post("/upload_excel")
async def upload_excel(
    file: UploadFile, current_user: User = Depends(get_current_user), _ = Depends(get_translator)
):
    try:
        # 读取 Excel 文件
        df = pd.read_excel(file.file)
        user_data = df[[_("姓名"), _("邮箱"), _("手机号"), _("密码")]].to_dict(orient="records")

        # 初始化 AdminServer
        admin_server = AdminServer(current_user)

        # 连接数据库
        manage_db = await get_database()

        # 批量创建用户
        created_users = []
        for data in user_data:
            user_info, success = await admin_server.create_user(
                username=data[_("姓名")],
                phone=str(data[_("手机号")]),
                email=data[_("邮箱")],
                password=str(data[_("密码")]),
            )
            if success:
                created_users.append(user_info["id"])

        # 互相加好友（新增好友互加逻辑）
        async def add_mutual_friends(user_a, user_b):
            await admin_server.add_friend(user_a, user_b)
            await admin_server.add_friend(user_b, user_a)

        tasks = []
        for i in range(len(created_users)):
            for j in range(i + 1, len(created_users)):
                tasks.append(add_mutual_friends(created_users[i], created_users[j]))

        await asyncio.gather(*tasks)

        return {"message": _("成功创建{count}用户并建立好友关系").format(count=len(created_users))}
    except Exception as e:
        return {"message": _("处理文件时出错: {error}").format(error=str(e))}
    finally:
        file.file.close()
