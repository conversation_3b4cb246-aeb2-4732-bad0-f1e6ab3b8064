import uuid
import os

from fastapi import APIRouter, Depends
from fastapi.responses import FileResponse

from ..database import get_chat_database, get_database
from ..models.user import User
from ..schemas.response import error, success
from ..schemas.tts import getSoundFileByMessageId
from ..utils.auth import get_current_user
from ..utils.file_handler import save_speech_file, download_file
from ..utils.log import logger
from ..utils.locales import get_translator

router = APIRouter()


@router.post("/generate", description="根据文本生成语音文件")
async def generate_sound_file(
    generate: getSoundFileByMessageId,
    current_user: User = Depends(get_current_user),
    manage_db=Depends(get_database),
    chat_db=Depends(get_chat_database),
    _ = Depends(get_translator),
):
    try:
        # 检查用户是否存在
        user_data = await manage_db.users.find_one(
            {
                "id": current_user.id,
                "is_active": True,
            }
        )
        if not user_data:
            return error(code=404, message=_("用户不存在或未激活"))

        # 检查用户是否为群组成员
        group = await manage_db.groups.find_one(
            {"id": generate.group_id, "is_dissolved": False}
        )
        if not group:
            return error(code=404, message=_("群组不存在或已解散"))
        if current_user.id not in group["member_ids"]:
            return error(code=403, message=_("您不在该群组中"))

        # 根据群ID获取群对话记录集合
        group_collection = getattr(chat_db, generate.group_id)

        # 从群对话记录中查找对应的消息
        message_record = await group_collection.find_one({"id": generate.message_id})
        if not message_record:
            return error(code=404, message=_("未找到对应的消息记录"))

        # 当前消息的sound_file_id不为空，直接返回
        if message_record.get("sound_file_id"):
            return success(code=200, message=_("语音文件已存在"), data=message_record["sound_file_id"])

        content = message_record["content"]
        doc_uuid = str(uuid.uuid4())
        path = await save_speech_file(content, user_data, doc_uuid, manage_db)

        await group_collection.update_one(
            {"id": generate.message_id},
            {"$set": {"sound_file_id": doc_uuid}},
        )
        # file = await manage_db.files.find_one({"id": doc_uuid})
        # if file:
        #     # 判断一下file["local_file_path"]路径是否存在
        #     # if os.path.exists(file["local_file_path"]):
        #     #     path = file["local_file_path"]
        #     # else:
        #     #     path = await download_file(file["cloud_file_path"])
        #     return FileResponse(
        #         path=path,
        #         filename=file["file_name"],
        #         media_type=file["file_type"],
        #     )

        return success(code=200, message=_("语音文件生成成功"), data=doc_uuid)

    except Exception as e:
        logger.error(f"生成语音文件失败: {str(e)}", exc_info=True)
        return error(code=500, message=_("生成语音文件失败"))
