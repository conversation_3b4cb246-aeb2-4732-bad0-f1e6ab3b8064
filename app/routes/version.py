from fastapi import APIRouter, Depends
from ..database import get_database
from ..utils.security import EncryptedAPIRoute
from ..schemas.response import success
from ..schemas.version import VersionBase
from ..config import settings

router = APIRouter(route_class=EncryptedAPIRoute)

@router.get("/app_version/{type}")
async def get_app_version(type:str,db=Depends(get_database)):
    version = await db.version.find_one({"type":type})
    if version:
        return success(data={"app_version":version['version'],"app_url":version['url']})
    else:
        return success(data={"app_version":"","app_url":""})

@router.post("/app_version")
async def update_app_version(
    version_data: VersionBase,
    db=Depends(get_database)
):
    version = await db.version.find_one({"type":version_data.type})
    if not version:
        await db.version.insert_one({"type":version_data.type,"version":version_data.version,"url":version_data.url})
    else:
        await db.version.find_one_and_update({"type":version_data.type},{"$set":{"version":version_data.version,"url":version_data.url}})
    return success()

@router.get("/app_name")
async def get_app_name():
    name=settings.DEFAULT_PRODUCT_NAME
    return success(data={"app_version": name})

@router.get("/app_lang_list")
async def get_app_lang_list():
    lang_list_str =settings.DEFAULT_PRODUCT_LANG_LIST
    return success(data={"app_lang_list": [lang.strip() for lang in lang_list_str.split(',')]})
