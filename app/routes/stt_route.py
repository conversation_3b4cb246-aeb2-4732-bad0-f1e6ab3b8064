import ast
import asyncio

from fastapi import APIRouter, BackgroundTasks, Depends, File, Form, UploadFile

from ..database import get_chat_database, get_database
from ..models.user import User
from ..schemas.response import error, success
from ..schemas.stt import getTransferByMessageId
from ..utils.asr import VoiceTranscriber
from ..utils.auth import get_current_user
from ..utils.log import logger
from ..utils.locales import get_translator
from ..utils.stt import recognize_audio_file

router = APIRouter()


@router.post("/recognize", description="使用vosk模型进行语音识别")
async def recognize_voice(
    input_audio_file: UploadFile = File(...),
    language: str = Form(default="cn", description="识别语言，支持'cn'或'en'"),
    _ = Depends(get_translator),
) -> dict:
    try:
        content = await asyncio.to_thread(input_audio_file.file.read)
        transcriber = VoiceTranscriber()
        recognition_result = await asyncio.to_thread(
            transcriber.transcribe_audio, content
        )

        if not recognition_result:
            return {"message": _("未能识别任何内容，请检查音频文件。"), "result": None}

        return {"message": _("语音识别完成"), "result": recognition_result}

    except Exception as e:
        return {"message": _("识别过程中发生错误: {err}").format(err=str(e)), "result": None}


@router.post("/get_transfer", description="根据消息ID获取识别内容")
async def get_transfer_by_message_id(
    background_tasks: BackgroundTasks,
    transfer: getTransferByMessageId,
    chat_db=Depends(get_chat_database),
    manage_db=Depends(get_database),
    current_user: User = Depends(get_current_user),
    _ = Depends(get_translator),
) -> dict:
    try:
        logger.critical(f"用户{current_user.id}获取语音识别结果{transfer}")
        # 检查用户是否为群组成员
        group = await manage_db.groups.find_one(
            {"id": transfer.group_id, "is_dissolved": False}
        )

        if not group:
            # return {"code": 404, "message": "群组不存在或已解散", "data": None}
            return error(code=404, message=_("群组不存在或已解散"))
        if current_user.id not in group["member_ids"]:
            # return {"code": 403, "message": "您不在该群组中", "data": None}
            return error(code=403, message=_("您不在该群组中"))
        # 根据群ID获取群对话记录集合
        group_collection = getattr(chat_db, transfer.group_id)

        # 从群对话记录中查找对应的消息
        message_record = await group_collection.find_one({"id": transfer.message_id})
        if not message_record:
            # return {"code": 404, "message": "未找到对应的消息记录", "data": None}
            return error(code=404, message=_("未找到对应的消息记录"))
        content = message_record["content"]
        if (
            isinstance(content, str)
            and content.startswith("{")
            and content.endswith("}")
        ):
            parsed_content = ast.literal_eval(content)
            # 获取录音文件id
            audio_file_id = parsed_content["id"]
        else:
            audio_file_id = content

        transfer_status = message_record.get("transfer_status", None)
        duration = message_record.get("duration", None)
        transfer_content = message_record.get("transfer", None)
        if transfer_content:
            return success(code=200, message=_("识别内容获取成功"), data=transfer_content)
        # 检查转文字状态
        if not transfer_status or transfer_status == "fail":
            # 未转换过或者转换失败，调用转换函数
            if duration > 60:
                # 长录音重新后台识别,提前返回
                background_tasks.add_task(
                    recognize_audio_file,
                    audio_file_id,
                    transfer.message_id,
                    transfer.group_id,
                    current_user,
                    long=True,
                )
                return_msg = (
                    _("重新识别中...") if transfer_status == "fail" else _("识别中...")
                )
            else:
                # 段录音及时识别返回结果
                return_msg = await recognize_audio_file(
                    audio_file_id,
                    transfer.message_id,
                    transfer.group_id,
                    current_user,
                    long=False,
                )
            return success(code=200, message=return_msg, data=return_msg)
        else:
            # 已经触发过转换(transfer_status为parsing或success)
            # 判断录音类别(长语音/短语音)
            if duration and transfer_status == "parsing":
                return_msg = _("识别中...")
                return success(code=200, message=return_msg, data=return_msg)
            elif duration > 60 and transfer_status == "success":
                # 长录音每个群只限识别一次,识别结果存放的txt文档id储存在transfer_doc_id字段
                transfer_doc_id = message_record.get("transfer_doc_id", None)
                if not transfer_doc_id:
                    # 没有转换文档id
                    return_msg = _("识别异常，后台管理员排查中")
                    logger.warning(f"{transfer}长录音存在识别成功却没有文档id的情况")
                    return success(code=200, message=return_msg, data=return_msg)
                # 判断群是否已发送过语音转文字文件
                # 模糊查询content字段中包含 id:"文档id"的行记录
                transfer_file = await group_collection.find_one(
                    {"content": {"$regex": '"id": "{}"'.format(transfer_doc_id)}}
                )
                # escaped_name = re.escape("id:'{}'".format(transfer_doc_id))
                # regex_pattern = f".*{escaped_name}.*"
                # transfer_file = await group_collection.find_one(
                #     regex_pattern
                # )
                if transfer_file:
                    content = transfer_file["content"]
                    if (
                        isinstance(content, str)
                        and content.startswith("{")
                        and content.endswith("}")
                    ):
                        parsed_content = ast.literal_eval(content)
                        return_msg = _("已识别成功,请查看识别结果文档{filename}").format(filename=parsed_content['filename'])
                        return success(code=200, message=return_msg, data=return_msg)
                    else:
                        return_msg = _("已识别成功")
                        return success(code=200, message=return_msg, data=return_msg)
                else:
                    # 有id却没有文档发送记录？
                    return_msg = _("识别异常，后台管理员排查中")
                    logger.warning(f"{transfer}长录音存在识别成功但没有文件记录的情况")
                    return success(code=200, message=return_msg, data=return_msg)

            elif (duration < 60 or duration == 60) and transfer_status == "success":
                # 短录音，返回transfer字段
                return_msg = message_record.get("transfer", None)
                if not return_msg:
                    return_msg = await recognize_audio_file(
                        audio_file_id,
                        transfer.message_id,
                        transfer.group_id,
                        current_user,
                        long=False,
                    )
                return success(code=200, message=_("识别内容获取成功"), data=return_msg)

    except Exception as e:
        # 修改此处的返回值
        return error(code=500, message=_("获取识别内容时发生错误: {err}").format(err=str(e)))
