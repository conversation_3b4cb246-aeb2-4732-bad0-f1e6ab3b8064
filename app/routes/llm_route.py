from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException

from ..database import get_database
from ..models.user import User
from ..models.user import CustomModel
from ..schemas.response import error, success
from ..schemas.user import CustomLLMCreate, CustomLLMResponse, CustomLLMUpate
from ..utils.auth import get_current_user
from ..utils.llm import SupportedModels  # 导入 SupportedModels 枚举
from ..utils.log import logger
from ..utils.locales import get_translator

router = APIRouter()


@router.get("/llm_list", description="获取系统默认及自定义模型列表")
async def llm_list(current_user: User = Depends(get_current_user), _ = Depends(get_translator)):
    try:
        # 获取 SupportedModels 枚举中的所有模型名称
        preset_models = [_(str(model.value)) for model in SupportedModels if not model.name in ['GEMINI_PRO']]

        # 获取用户自定义模型名称列表（保留重复项）
        custom_models = [
            m.name for m in getattr(current_user.user_setting, "llm_models", []) if m.is_deleted is False
        ]
        # 直接合并两个列表
        combined_models = preset_models + custom_models

        return success(data={"models": combined_models})
    except Exception as e:
        return error(message=str(e))

@router.get("/group_llm_list/{group_id}", description="获取系统默认、自定义模型及群主共享列表")
async def group_llm_list(group_id: str,current_user: User = Depends(get_current_user), _ = Depends(get_translator),db=Depends(get_database)):
    try:
        group_owner_llm_list = []
        group_data = await db.groups.find_one({"id": group_id})
        if group_data.get("owner_id") != current_user.id:
            # 获取群主共享模型列表
            group_owner_data = await db.users.find_one({"id": group_data.get("owner_id")})
            group_owner_llm_list = [llm.get("name")+_(" (共享)") for llm in group_owner_data.get("user_setting", {}).get("llm_models", []) if llm['is_deleted'] is False and llm.get('is_shared',False) is True]

        # 获取 SupportedModels 枚举中的所有模型名称
        preset_models = [_(str(model.value)) for model in SupportedModels if not model.name in ['GEMINI_PRO']]

        # 获取用户自定义模型名称列表（保留重复项）
        custom_models = [
            m.name+_(" (自定义)") for m in getattr(current_user.user_setting, "llm_models", []) if m.is_deleted is False
        ]
        # 直接合并两个列表
        combined_models = preset_models + custom_models + group_owner_llm_list

        return success(data={"models": combined_models})
    except Exception as e:
        logger.error(f"get group llm list error:{str(e)}")
        return error(message=str(e))

@router.post("/custom_llm", description="添加自定义模型")
async def manage_custom_llm(
    data: CustomLLMCreate, 
    current_user: User = Depends(get_current_user),
    manage_db=Depends(get_database),
    _ = Depends(get_translator),
):
    try:
        # 获取当前用户设置
        user_setting = current_user.user_setting
        llm_models = getattr(user_setting, "llm_models", [])
        for model in llm_models:
            if model.name == data.name and model.is_deleted is False:
                return error(message=_("模型别名已存在，请使用其他别名"))
        # 检查模型名称是否在 SupportedModels 中
        if data.name in [_(str(model.value)) for model in SupportedModels]:
            return error(message=_("模型别名不能与系统默认模型名称相同"))

        model = CustomModel(name=data.name, 
                            provider=data.provider, 
                            url=data.url, 
                            key=data.key,
                            model_name=data.model_name, 
                            max_token=data.max_token,
                            is_shared=data.is_shared)

        # 更新数据库
        await manage_db.users.update_one(
            {"id": current_user.id},
            {"$push": {"user_setting.llm_models": model.model_dump()}},
        )

        return success(message=_("模型配置保存成功"))

    except Exception as e:
        logger.error(f"保存自定义模型失败: {str(e)}")
        return error(message=_("保存模型配置失败，请检查参数格式"))

@router.put("/custom_llm", description="更新自定义模型")
async def manage_custom_llm(
    data: CustomLLMUpate, 
    current_user: User = Depends(get_current_user),
    manage_db=Depends(get_database),
    _ = Depends(get_translator),
):
    try:
        # 获取当前用户设置
        user_setting = current_user.user_setting
        llm_models = getattr(user_setting, "llm_models", [])

        # 查找是否已存在同名配置
        for model in llm_models:
            if model.name == data.name and model.is_deleted is False and model.id != data.id:
                return error(message=_("模型别名已存在，请使用其他别名"))

        # 检查模型名称是否在 SupportedModels 中
        if data.name in [_(str(model.value)) for model in SupportedModels]:
            return error(message=_("模型别名不能与系统默认模型名称相同"))

        # 更新数据库
        result = await manage_db.users.update_one(
            {"id": current_user.id,
             "user_setting.llm_models.id": data.id
             },
            {"$set": {"user_setting.llm_models.$.name": data.name,
                      "user_setting.llm_models.$.provider": data.provider,
                     "user_setting.llm_models.$.url": data.url.rstrip("/"),
                     "user_setting.llm_models.$.key": data.key,
                     "user_setting.llm_models.$.model_name": data.model_name,
                     "user_setting.llm_models.$.max_token": data.max_token,
                     "user_setting.llm_models.$.updated_at": datetime.now(),
                     "user_setting.llm_models.$.is_shared": data.is_shared,
                     }
            },   
        )

        if result.modified_count == 0:
            return error(message=_("模型不存在"))

        return success(message=_("模型配置保存成功"))

    except Exception as e:
        logger.error(f"保存自定义模型失败: {str(e)}")
        return error(message=_("保存模型配置失败，请检查参数格式"))

@router.delete("/custom_llm", description="删除自定义模型")
async def delete_custom_llm(
    id: str, 
    current_user: User = Depends(get_current_user),
    manage_db=Depends(get_database),
    _ = Depends(get_translator),
):
    try:
        result = await manage_db.users.update_one(
            {"id": current_user.id,
             "user_setting.llm_models.id": id},
            {"$set": {"user_setting.llm_models.$.is_deleted": True,
                      "user_setting.llm_models.$.updated_at": datetime.now(),}},
        )

        if result.modified_count == 0:
            raise HTTPException(status_code=404, detail=_("模型不存在或已删除"))

        return success(message=_("模型删除成功"))
    except HTTPException as he:
        raise he
    except Exception as e:
        logger.error(f"删除自定义模型失败: {str(e)}")
        return error(message=_("删除模型失败"))


@router.get("/custom_llm", description="获取自定义模型列表")
async def get_custom_llm(current_user: User = Depends(get_current_user), _ = Depends(get_translator)):
    try:
        # 获取用户设置中的自定义模型列表
        custom_models = getattr(current_user.user_setting, "llm_models", [])

        # 转换为 CustomLLMResponse 格式
        response_models = [CustomLLMResponse(**model.model_dump()).model_dump() for model in custom_models if model.is_deleted is False]

        return success(data=response_models)

    except Exception as e:
        logger.error(f"获取自定义模型列表失败: {str(e)}")
        return error(message=_("获取模型列表失败"))
