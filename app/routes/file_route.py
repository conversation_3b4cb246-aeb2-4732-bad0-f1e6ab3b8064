import asyncio
import os
import uuid
from typing import Annotated, Optional

from fastapi import APIRouter, BackgroundTasks, Depends, File, Form, Request, UploadFile
from fastapi.responses import FileResponse, HTMLResponse
from fastapi.templating import Jinja2Templates

from ..config import settings
from ..database import get_chat_database, get_database
from ..models.user import User
from ..schemas.file import GroupToDataBase
from ..schemas.response import error, success
from ..utils.auth import get_current_user
from ..utils.database import delete_file_from_database, save_file_to_database
from ..utils.file_handler import (
    download_file,
    get_audio_duration,
    save_file,
    validate_file,
)
from ..utils.llm import SupportedModels, background_answer, broadcast_and_save_msg
from ..utils.log import logger
from ..utils.stt import process_audio_for_wakeword
from ..utils.vl import llm_process_picture
from ..utils.locales import get_translator

router = APIRouter()

# 设置模板目录
templates = Jinja2Templates(directory="templates")


@router.post("/database/upload", description="知识库上传文档")
async def upload_file_to_database(
    background_tasks: BackgroundTasks,
    file: Annotated[UploadFile, File()],
    database_id: Annotated[str, Form()],
    current_user: User = Depends(get_current_user),
    manage_db=Depends(get_database),
    _ = Depends(get_translator),
):
    try:
        logger.critical(f"用户{current_user.id}上传文档到数据库{database_id}接口")
        doc_uuid = str(uuid.uuid4())
        # 验证文件
        await validate_file(file, settings.SUPPORTED_UNSTRUCTURED_FILE_TYPE, 0)

        # 查询collection_name
        database = await manage_db.databases.find_one(
            {"id": database_id, "is_deleted": False}
        )

        if not database:
            return error(code=404, message=_("数据库不存在"))

        if current_user.id != database["owner_id"]:
            logger.warning(
                f"发现用户{current_user.id}非法上传文档到数据库{database_id}"
            )
            return error(code=403, message=_("您没有权限访问该数据库"))
        safe_filename = "".join(
            char for char in file.filename if char.isalnum() or char in "._- "
        )
        file.filename = safe_filename
        content = await file.read()
        file_header = {
            "filename": file.filename,
            "content_type": file.content_type,
            "size": file.size,
        }
        background_tasks.add_task(
            save_file_to_database,
            content,
            file_header,
            current_user,
            doc_uuid,
            database,
            _,
        )
        return success(message=_("后台上传中"))

    except ValueError as e:
        return error(code=400, message=str(e))
    except Exception as e:
        logger.error(
            f"用户{current_user.id}在知识库{database_id}上传文档接口出错:{str(e)}"
        )
        return error(code=500, message=_("服务端遇到未知异常：{}").format(str(e)))


@router.get(
    "/database/upload_to/{database_id}/{token}",
    response_class=HTMLResponse,
    description="网页上传文档到知识库",
)
async def upload_file_from_page(
    request: Request,
    token: str,
    database_id: str,
    db=Depends(get_database),
    _ = Depends(get_translator),
):
    try:
        user = await get_current_user(token)
        # 获取用户信息
        user_data = await db.users.find_one(
            {
                "id": user.id,
                "is_active": True,
            }
        )
        if not user_data:
            raise ValueError("非法用户")
        # 查询知识库信息
        database = await db.databases.find_one({"id": database_id, "is_deleted": False})

        if not database:
            raise ValueError("数据库不存在")

        if user.id not in database["member_ids"]:
            logger.warning(f"发现用户{user.id}非法上传文档到数据库{database_id}")
            raise ValueError("您没有权限访问该数据库")
        # 渲染模板
        return templates.TemplateResponse(
            "upload.html",
            {
                "request": request,
                "token": token,
                "database_id": database_id,
                "database_name": database["name"],
            },
        )

    except ValueError as e:
        return templates.TemplateResponse(
            "error.html", {"request": request, "error": str(e)}
        )
    except Exception as e:
        logger.error(
            f"用户{token}在知识库{database_id}上传文档接口出错:{str(e)}", exc_info=False
        )
        return templates.TemplateResponse(
            "error.html", {"request": request, "error": str(e)}
        )


@router.post("/group/upload/media", description="群上传多媒体数据")
async def group_upload_media(
    background_tasks: BackgroundTasks,
    file: Annotated[UploadFile, File(...)],
    group_id: Annotated[str, Form(...)],
    duration: Annotated[Optional[float], Form(...)],
    at: Annotated[Optional[str], Form(...)] = None,
    question: Annotated[Optional[str], Form(...)] = None,
    current_user: User = Depends(get_current_user),
    manage_db=Depends(get_database),
    chat_db=Depends(get_chat_database),
    _ = Depends(get_translator),
):
    try:
        logger.critical(
            f"群用户{current_user.id}上传多媒体:前端传入group_id:{group_id},duration:{duration},at:{at}"
        )
        # 验证文档大小及类型
        await validate_file(
            file,
            settings.ALLOWED_EXTENSIONS,
            0,
        )
        # 生成文档id
        doc_uuid = str(uuid.uuid4())
        # 获取群对话记录集合
        group_collection = getattr(chat_db, group_id)
        # 获取群组信息
        group = await manage_db.groups.find_one({"id": group_id, "is_dissolved": False})
        if not group:
            return error(code=404, message=_("群组不存在"))
        if current_user.id not in group["member_ids"]:
            logger.warning(f"发现用户{current_user.id}非法上传文档到群聊{group_id}")
            return error(code=403, message=_("您没有权限访问该群聊"))
        safe_filename = "".join(
            char for char in file.filename if char.isalnum() or char in "._- "
        )
        file.filename = safe_filename
        content = await file.read()
        file_header = {
            "filename": file.filename,
            "content_type": file.content_type,
            "size": file.size,
        }
        # 广播发送文档的消息
        message = await broadcast_and_save_msg(
            group_collection=group_collection,
            manage_db=manage_db,
            content=(
                doc_uuid
                if file_header["content_type"].startswith("audio/")
                else {"id": doc_uuid, "filename": file.filename}
            ),
            type=file.content_type,
            group_id=group_id,
            sender_id=current_user.id,
            cite_id=None,
            duration=duration,
            at_list=[] if at in ['','undefined',None] else [at]
        )
        background_tasks.add_task(
            save_file,
            content,
            current_user,
            doc_uuid,
            manage_db,
            file_header,
            None,
            group_id,
        )
        if at and at != "undefined":
            if file_header["content_type"].startswith("audio/"):
                # 当音频有@时的处理
                if (
                    at in [_(str(model.value)) for model in SupportedModels]
                    and duration < 60
                ):
                    # @的是模型时的处理
                    background_tasks.add_task(
                        process_audio_for_wakeword,
                        content=content,
                        group=group,
                        group_collection=group_collection,
                        message_id=message.id,
                        model=at,
                        user=current_user,
                        language="cn",
                        _=_
                    )
            elif file_header["content_type"].startswith("image/"):
                # 当图片有@时的处理
                background_tasks.add_task(
                    llm_process_picture,
                    picture_content=content,
                    picture_type=file_header["content_type"],
                    question=question if question else "描述一下这张图片",
                    group=group,
                    group_collection=group_collection,
                    message_id=message.id,
                    model=at,
                    user=current_user,
                )
            # elif at not in [str(model.value) for model in SupportedModels]:
            #     # TODO: @的是用户时的处理
            #     pass
        # elif duration and duration > 60:
        #     # 长录音上传马上触发识别任务,以便后续调转文字时更快响应
        #     background_tasks.add_task(
        #         recognize_audio_file, doc_uuid, group_id, current_user, long=True
        #     )
        return success(message=_("上传成功"))

    except ValueError as e:
        return error(code=400, message=str(e))
    except Exception as e:
        logger.error(f"用户{current_user.id}在群{group_id}上传图片接口出错:{str(e)}")
        return error(code=500, message=_("服务端遇到未知异常：{}").format(str(e)))


@router.post("/group/upload/file", description="群组上传文档")
async def upload_file_to_group(
    background_tasks: BackgroundTasks,
    file: Annotated[UploadFile, File(...)],
    group_id: Annotated[str, Form(...)],
    current_user: User = Depends(get_current_user),
    manage_db=Depends(get_database),
    chat_db=Depends(get_chat_database),
    _ = Depends(get_translator),
):
    try:
        logger.critical(f"群用户{current_user.id}上传文档:前端传入group_id:{group_id}")
        # 验证文档大小及类型
        await validate_file(
            file,
            settings.ALLOWED_EXTENSIONS,
            0,
        )
        # 生成文档id
        doc_uuid = str(uuid.uuid4())
        # 获取群对话记录集合
        group_collection = getattr(chat_db, group_id)
        # 获取群组信息
        group = await manage_db.groups.find_one({"id": group_id, "is_dissolved": False})
        if not group:
            return error(code=404, message=_("群组不存在"))
        if current_user.id not in group["member_ids"]:
            logger.warning(f"发现用户{current_user.id}非法上传文档到群聊{group_id}")
            return error(code=403, message=_("您没有权限访问该群聊"))

        safe_filename = "".join(
            char for char in file.filename if char.isalnum() or char in "._- "
        )
        file.filename = safe_filename
        # 保存文件
        content = await file.read()
        file_header = {
            "filename": file.filename,
            "content_type": file.content_type,
            "size": file.size,
        }
        # await save_file(content,current_user,doc_uuid,manage_db,file_header)
        file_path = await save_file(
            content, current_user, doc_uuid, manage_db, file_header, None, group_id
        )
        # 通过文件上传录音文件时,判断如果时长录音则提前进行识别
        if file_header["content_type"].startswith("audio/"):
            duration = get_audio_duration(file_path)
        else:
            duration = None
        # 广播发送文档的消息
        message = await broadcast_and_save_msg(
            group_collection=group_collection,
            manage_db=manage_db,
            content={"id": doc_uuid, "filename": file.filename},
            type=file.content_type,
            group_id=group_id,
            sender_id=current_user.id,
            cite_id=None,
            duration=duration,
        )

        # if duration and duration > 60:
        #     background_tasks.add_task(
        #         recognize_audio_file,
        #         doc_uuid,
        #         message.id,
        #         group_id,
        #         current_user,
        #         long=True,
        #     )

        return success(message=_("上传成功"))

    except ValueError as e:
        return error(code=400, message=str(e))
    except Exception as e:
        logger.error(f"用户{current_user.id}在群{group_id}上传文档接口出错:{str(e)}")
        return error(code=500, message=_("服务端遇到未知异常：{}").format(str(e)))


@router.post("/head/upload", description="上传头像")
async def upload_head(
    background_tasks: BackgroundTasks,
    file: Annotated[UploadFile, File(...)],
    current_user: User = Depends(get_current_user),
    db=Depends(get_database),
    _ = Depends(get_translator),
):
    try:
        logger.critical(f"用户{current_user.id}上传头像")
        doc_uuid = str(uuid.uuid4())
        # 验证文件
        await validate_file(
            file,
            [".png", ".jpg", ".jpeg"],
            settings.MAX_FILE_SIZE,
        )
        safe_filename = "".join(
            char for char in file.filename if char.isalnum() or char in "._- "
        )
        file.filename = safe_filename
        content = await file.read()
        file_header = {
            "filename": file.filename,
            "content_type": file.content_type,
            "size": file.size,
        }
        # 保存文件到本地及云端
        background_tasks.add_task(
            save_file,
            content,
            current_user,
            doc_uuid,
            db,
            file_header,
        )
        # await save_file(content,current_user,doc_uuid,db,file_header)

        # 更新用户头像参数
        await db.users.update_one(
            {"id": current_user.id},
            {"$set": {"avatar": doc_uuid}},
        )

        return success(data={"file_path": current_user.id}, message=_("上传成功"))
    except ValueError as e:
        return error(code=400, message=str(e))
    except Exception as e:
        logger.error(
            f"{current_user.id}上传头像{doc_uuid}接口出错: {str(e)}", exc_info=True
        )
        return error(code=500, message=_("服务端遇到未知异常：{}").format(str(e)))


@router.get("/download/{file_id}/{token}", description="下载文档")
async def download_file_route(
    file_id: str,
    token: str,
    # current_user: User = Depends(get_current_user),
    db=Depends(get_database),
    _ = Depends(get_translator),
):
    try:
        current_user = await get_current_user(token=token)
        logger.info(f"用户{current_user.id} 下载 {file_id}")
        # 查询文件记录
        file_record = await db.files.find_one({"id": file_id, "is_deleted": False})

        if not file_record:
            return error(code=404, message=_("文件不存在"))

        # 检查用户是否有权限访问该文件
        # database = await db.databases.find_one({
        #     "id": file_record["vectordb_id"],
        #     "is_deleted": False
        # })

        # if not database or current_user.id not in database["member_ids"]:
        #     logger.warning(f"发现用户{current_user.id}非法下载文档{file_id}")
        #     return error(code=200, message="您没有权限下载该文件")

        # 本地文件不存在，则从云端下载
        if not os.path.exists(file_record["local_file_path"]):
            local_file_path = await download_file(f"{file_record['cloud_file_path']}")
        else:
            local_file_path = file_record["local_file_path"]

        # 返回文件
        return FileResponse(
            path=local_file_path,
            filename=file_record["file_name"],
            media_type=file_record["file_type"],
        )

    except Exception as e:
        logger.error(f"下载文件{file_id}失败: {str(e)}")
        return error(code=500, message=f"下载文件失败：{str(e)}")


@router.get("/head/{user_id}", description="下载头像")
async def download_head(user_id: str, db=Depends(get_database), _ = Depends(get_translator)):
    try:
        # 从数据库查询文件记录
        user = await db.users.find_one(
            {
                "id": user_id,
                "is_active": True,
            }
        )
        if not user or not user["avatar"]:
            return FileResponse(
                path="static/default_avatar.png",
            )
        head_file = await db.files.find_one({"id": user["avatar"], "is_deleted": False})
        if not head_file:
            # 查询不到头像文档则项目默认头像
            return FileResponse(
                path="static/default_avatar.png",
            )
        # 本地文件不存在，则从云端下载
        if not os.path.exists(head_file["local_file_path"]):
            local_file_path = await download_file(f"{head_file['cloud_file_path']}")
        else:
            local_file_path = head_file["local_file_path"]
        # 返回文件
        return FileResponse(
            path=local_file_path,
        )

    except Exception as e:
        logger.error(f"{user_id}下载头像失败: {str(e)}")
        return error(code=500, message=f"下载头像失败：{str(e)}")


@router.delete("/database/delete/{file_id}", description="删除知识库文件")
async def delete_database_file(
    file_id: str,
    current_user: User = Depends(get_current_user),
    manage_db=Depends(get_database),
    _ = Depends(get_translator),
):
    try:
        logger.critical(f"用户{current_user.id}删除知识库文件{file_id}")
        # 查询文件记录
        file_record = await manage_db.files.find_one(
            {
                "id": file_id,
                "is_deleted": False,
            }
        )
        if not file_record:
            return error(code=404, message=_("文件不存在"))

        # 获取关联的数据库ID
        database_id = file_record.get("vectordb_id")
        if not database_id:
            return error(code=409, message=_("文件未关联到知识库"))

        # 查询数据库信息
        database = await manage_db.databases.find_one(
            {
                "id": database_id,
                "is_deleted": False,
            }
        )
        if not database:
            return error(code=404, message=_("关联的数据库不存在"))

        # 检查用户权限
        if (
            current_user.id not in database["member_ids"]
            and current_user.id != database["owner_id"]
        ):
            logger.warning(f"用户{current_user.id}尝试非法删除文件{file_id}")
            return error(code=403, message=_("无操作权限"))

        # 执行删除操作
        await delete_file_from_database(
            file_id,
            database,
            current_user,
            file_record.get("file_size", 0),
        )

        return success(message=_("文件删除成功"))

    except Exception as e:
        logger.error(f"删除文件{file_id}失败: {str(e)}")
        return error(code=500, message=f"删除失败: {str(e)}")


@router.post("/group/database/upload", description="把群文件上传到知识库库")
async def upload_group_file_to_database(
    background_tasks: BackgroundTasks,
    info: GroupToDataBase,
    manage_db=Depends(get_database),
    current_user: User = Depends(get_current_user),
    _ = Depends(get_translator),
):
    try:
        logger.critical(
            f"用户{current_user.id}把群文件{info.file_id}上传到知识库{info.database_id}"
        )
        # 查询原始文件记录
        file_record = await manage_db.files.find_one(
            {"id": info.file_id, "is_deleted": False}
        )

        if not file_record:
            return error(code=404, message=_("原始文件不存在"))

        # 验证群组权限
        group = await manage_db.groups.find_one(
            {"id": info.group_id, "is_dissolved": False, "member_ids": current_user.id}
        )
        if not group:
            return error(code=403, message=_("无群组访问权限"))

        # 验证目标数据库权限
        database = await manage_db.databases.find_one(
            {
                "id": info.database_id,
                "is_deleted": False,
                "$or": [{"owner_id": current_user.id}, {"member_ids": current_user.id}],
            }
        )
        if not database:
            return error(code=403, message=_("无数据库访问权限"))

        # 检查本地文件是否存在
        if not os.path.exists(file_record["local_file_path"]):
            local_path = await download_file(file_record["cloud_file_path"])
        else:
            local_path = file_record["local_file_path"]

        # 读取文件内容
        with open(local_path, "rb") as f:
            content = f.read()

        # 生成新文档UUID
        new_doc_uuid = str(uuid.uuid4())

        # 构建文件头信息
        file_header = {
            "filename": file_record["file_name"],
            "content_type": file_record["file_type"],
            "size": file_record["file_size"],
        }

        # 启动后台任务
        background_tasks.add_task(
            save_file_to_database,
            content,
            file_header,
            current_user,
            new_doc_uuid,
            database,
            _
        )

        return success(message=_("文件已加入上传队列"))

    except ValueError as e:
        return error(code=400, message=str(e))
    except Exception as e:
        logger.error(f"群文件转存失败: {str(e)}")
        return error(code=500, message=f"文件转存失败: {str(e)}")


@router.post("/group/multiple/upload", description="群批量上传文件")
async def upload_multiple_files_to_group(
    background_tasks: BackgroundTasks,
    files: Annotated[list[UploadFile], list[File()]],
    group_id: Annotated[str, Form(...)],
    at: Annotated[Optional[str], Form(...)] = None,
    question: Annotated[Optional[str], Form(...)] = None,
    current_user: User = Depends(get_current_user),
    manage_db=Depends(get_database),
    chat_db=Depends(get_chat_database),
    _ = Depends(get_translator),
):
    try:
        logger.critical(
            f"群用户{current_user.id}批量上传文件:前端传入group_id:{group_id}"
        )
        # 验证文档大小及类型并生成文档id
        doc_uuids = []
        file_headers = []
        for file in files:
            await validate_file(
                file,
                settings.ALLOWED_EXTENSIONS,
                0,
            )
            doc_uuids.append(str(uuid.uuid4()))
            safe_filename = "".join(
                char for char in file.filename if char.isalnum() or char in "._- "
            )
            file.filename = safe_filename
            file_headers.append(
                {
                    "filename": file.filename,
                    "content_type": file.content_type,
                    "size": file.size,
                }
            )
        # 获取群对话记录集合
        group_collection = getattr(chat_db, group_id)
        # 获取群组信息
        group = await manage_db.groups.find_one({"id": group_id, "is_dissolved": False})
        if not group:
            return error(code=404, message=_("群组不存在"))
        if current_user.id not in group["member_ids"]:
            logger.warning(f"发现用户{current_user.id}非法上传文档到群聊{group_id}")
            return error(code=403, message=_("您没有权限访问该群聊"))
        for i in range(len(files)):
            file = files[i]
            doc_uuid = doc_uuids[i]
            file_header = file_headers[i]
            content = await file.read()
            # 保存文件
            file_path = await save_file(
                content, current_user, doc_uuid, manage_db, file_header, None, group_id
            )
            # 通过文件上传录音文件时,判断如果时长录音则提前进行识别
            if file_header["content_type"].startswith("audio/"):
                duration = get_audio_duration(file_path)
            else:
                duration = None
            # 广播发送文档的消息
            await broadcast_and_save_msg(
                group_collection=group_collection,
                manage_db=manage_db,
                content=(
                    doc_uuid
                    if file_header["content_type"].startswith("audio/")
                    else {"id": doc_uuid, "filename": file.filename}
                ),
                type=file.content_type,
                group_id=group_id,
                sender_id=current_user.id,
                cite_id=None,
                duration=duration,
            )
        # 更新群组的最近文件列表
        recent_files = [
            {"id": doc_uuid, "type": header["content_type"]}
            for doc_uuid, header in zip(doc_uuids, file_headers)
        ]
        # await manage_db.groups.update_one(
        #     {"id": group_id},
        #     {"$push": {"recent_files": {"$each": recent_files}}},
        # )

        if question:
            question_message = await broadcast_and_save_msg(
                group_collection=group_collection,
                manage_db=manage_db,
                content=f"@{at} {question}",
                type="text",
                group_id=group_id,
                sender_id=current_user.id,
            )

        # 处理@
        if at and at in [_(str(model.value)) for model in SupportedModels]:
            group = await manage_db.groups.find_one(
                {"id": group_id}, {"is_dissolved": False}
            )
            asyncio.create_task(
                background_answer(
                    group_data=group,
                    question=question if question else "根据上下文生成回答",
                    llm_model=at,
                    group_collection=group_collection,
                    user_data=current_user,
                    cite_id=question_message.id if question else None,
                    recent_files=recent_files,
                    _=_
                )
            )

        return success(message=_("批量上传成功"))
    except Exception as e:
        logger.error(f"群批量上传文件失败: {str(e)}")
        return error(code=500, message=f"批量上传文件失败: {str(e)}")
