import asyncio
import json
from uuid import uuid4
from fastapi import APIRouter, Depends, WebSocket, WebSocketDisconnect

from ..database import get_chat_database, get_database
from ..models.user import User
from ..utils.auth import get_current_user
from ..utils.llm import background_answer, broadcast_and_save_msg
from ..utils.log import logger
from ..utils.websocket_manager import connection_dic
from ..utils.security import create_access_token
from ..schemas.message import StopMessage
from ..schemas.response import error, success
from ..utils.locales import get_translator_func,get_translator
from ..models.user import User
from pymongo import ReturnDocument
router = APIRouter()

# 面向临时用户的临时会话室socket端点
@router.websocket("/ws_tmp/{group_id}/{language}")
async def websocket_endpoint(
    websocket: WebSocket,
    group_id: str,
    language: str,
    manage_db=Depends(get_database),
    chat_db=Depends(get_chat_database),
):
    try:
        _ = get_translator_func(language)
        # 查找群组
        group_data = await manage_db.groups.find_one(
            {
                "id": group_id,
                "is_dissolved": False,
            }
        )
        if not group_data:
            # 群组不存在
            logger.warning(
                f"发现{user_id}({user_id})违规向群不存在的群{group_id}发送消息"
            )
            # 群组不存在或已解散则拒绝连接
            await websocket.close()
            return
        # 更新群访客数并获取最新的访客讯号
        group_data = await manage_db.groups.find_one_and_update(
            {"id": group_id},
            {"$inc": {"tmp_visitor": 1}},
            return_document=ReturnDocument.AFTER
        )
        # 生成临时用户数据
        user_id = str(uuid4())
        # 临时用户命名"群id后四位_访客_访客序号"
        user = User(id=user_id, username=f"{group_id[-4:]}_"+_("访客")+f"_{group_data['tmp_visitor']}",hashed_password="",avatar=None,is_active=True)
        user_data = user.model_dump()
        await connection_dic.connect(user,websocket)
        if group_data["is_dissolved"] is True:
            if group_data.get("type", "group") == "group":
                await websocket.send_text(
                    json.dumps({"type": "alert", "content": _( "群组已解散")})
                )
            if group_data.get("type", "group") == "private":
                await websocket.send_text(
                    json.dumps({"type": "alert", "content": _( "对方已删除聊天")})
                )
            # 群组不存在或已解散则拒绝连接s
            await websocket.close()
            return
        # 将临时用户信息写入user表
        await manage_db.users.insert_one(user_data)
        # 生成临时token
        token = create_access_token(data={"uuid": str(user.id)})
        # 回传临时token和临时用户信息
        await websocket.send_text(
                json.dumps({"type": "token", "content": {"token":token,"user_data":user.model_dump(exclude=['created_at'])}})
            )
        logger.info(f"{user_id} connected")
        # 将临时用户拉入群聊
        await manage_db.groups.update_one(
            {"id": group_id},
            {"$push": {"member_ids": user_id}},
        )
        group_collection = getattr(chat_db, group_id)
        # 每当有新用户进入聊天室时，都用群主身份广播一次欢迎语
        await websocket.send_text(
            json.dumps({"type": "welcome", "content": group_data.get("web_agent_setting",{}).get("welcome_message",_("欢迎光临聊天室"))})
        )
        while True:
            try:
                data = await websocket.receive_json()
                manage_db = await get_database()
                chat_db = await get_chat_database()
                # 心跳确认
                if data["type"] == "ping":
                    await websocket.send_text(json.dumps({"type": "pong"}))
                    continue
                logger.critical(f"{user.id} 发送socket数据: {data}")
                # 获取当前消息语言
                lang = data.get("language", "zh-CN") if data else "zh-CN"
                _ = get_translator_func(lang)
                # 查询群成员
                group_data = await manage_db.groups.find_one({"id": group_id})
                if not group_data:
                    # 群组不存在
                    logger.warning(
                        f"发现{user.username}({user.id})违规向群不存在的群{group_id}发送消息"
                    )
                    await websocket.send_text(
                        json.dumps({"type": "alert", "content": _( "群组不存在")})
                    )
                    continue
                if group_data["is_dissolved"] is True:
                    if group_data.get("type", "group") == "group":
                        await websocket.send_text(
                            json.dumps({"type": "alert", "content": _( "群组已解散")})
                        )
                    if group_data.get("type", "group") == "private":
                        await websocket.send_text(
                            json.dumps({"type": "alert", "content": _( "对方已删除聊天")})
                        )
                    continue
                
                group_collection = getattr(chat_db, group_id)

                """
                data 格式
                1.普通文字消息
                {"type":"text","content":消息内容,"group_id":消息所属群id,"cite":引用消息id}
                    1.1 文字内容@LLM或RAG
                    {"type":"text","content":消息内容,"group_id":消息所属群id,model:"bot/DeepSeek-V3/...等"}

                """
                if data["type"] == "text":
                    # 广播用户消息
                    message = await broadcast_and_save_msg(
                        group_collection=group_collection,
                        manage_db=manage_db,
                        content=data["content"],
                        type=data["type"],
                        group_id=group_id,
                        sender_id=user.id,
                        cite_id=None if "cite" not in data else data["cite"],
                        at_list=[] if "at_list" not in data else data["at_list"],
                    )
                    # 向bot提问的逻辑
                    # 在消息处理部分修改：
                    if "model" in data and data["model"] != "":
                        # 去除@模型文本
                        query = data["content"].strip().replace(f"@{data['model']}", "")
                        if "at_list" in data and len(data["at_list"]) != 0:
                            # 同时@群员和模型时，不走问答
                            continue
                        # 启动流式任务
                        asyncio.create_task(
                            background_answer(
                                group_data=group_data,
                                question=query,
                                llm_model=data["model"],
                                group_collection=group_collection,
                                cite_id=message.id,  # 使用消息ID作为引用
                                user_data=user_data,
                                web_search=(
                                    False
                                    if "web_search" not in data
                                    else data["web_search"]
                                ),
                                _=_,
                                lang=lang,
                            )
                        )
                # 用户撤回消息
                elif data["type"] == "revoke":
                    await broadcast_and_save_msg(
                        group_collection=group_collection,
                        manage_db=manage_db,
                        content=data["message_ids"],
                        type=data["type"],
                        group_id=data["group_id"],
                        sender_id=user.id,
                        cite_id=None,
                        save_message=False,
                    )
                    await group_collection.update_one(
                        {"id": data["message_ids"]}, {"$set": {"is_revoke": True}}
                    )
                    message = await group_collection.find_one(
                        {"id": data["message_ids"]}
                    )
                    message.pop("_id")
                    message["content"] = _( "撤回了一条消息")
                    message["type"] = data["type"]
                    message["is_revoke"] = False
                    # 在message_ids后插入一条撤回消息的记录
                    await group_collection.insert_one(message)
                    # 更新群最新消息为撤回消息
                    if group_data["last_message"]["id"] == data["message_ids"]:
                        await manage_db.groups.update_one(
                            {"id": group_data["id"]},
                            {"$set": {"last_message": message}},
                        )
                    # 更新引用撤回消息的引用内容
                    await group_collection.update_many(
                        {"cite.id": data["message_ids"]},
                        {"$set": {"cite.content": _( "消息已撤回")}},
                    )
                    continue

                elif data["type"] == "delete":
                    await broadcast_and_save_msg(
                        group_collection=group_collection,
                        manage_db=manage_db,
                        content=data["message_ids"],
                        type=data["type"],
                        group_id=data["group_id"],
                        sender_id=user.id,
                        cite_id=None,
                        save_message=False,
                    )
                    await group_collection.update_one(
                        {"id": data["message_ids"]}, {"$set": {"is_revoke": True}}
                    )
                    message = await group_collection.find_one(
                        {"id": data["message_ids"]}
                    )
                    message.pop("_id")
                    message["content"] = _( "已删除")
                    message["type"] = data["type"]
                    message["is_revoke"] = False
                    # 在message_ids后插入一条撤回消息的记录
                    await group_collection.insert_one(message)
                    # 更新群最新消息为撤回消息
                    if group_data["last_message"]["id"] == data["message_ids"]:
                        await manage_db.groups.update_one(
                            {"id": group_data["id"]},
                            {"$set": {"last_message": message}},
                        )
                    await group_collection.update_many(
                        {"cite.id": data["message_ids"]},
                        {"$set": {"cite.content": _( "已删除")}},
                    )
                    continue
                elif data["type"] == "new":
                    # 聊聊新话题
                    # 如果group_collection最后一条消息类型不是new，才发送新话题消息
                    last_message = await group_collection.find_one(
                        {}, sort=[("created_at", -1)]
                    )
                    if last_message and last_message["type"] == "new":
                        continue
                    await broadcast_and_save_msg(
                        group_collection=group_collection,
                        manage_db=manage_db,
                        content=_("以下是新话题"),
                        type=data["type"],
                        group_id=data["group_id"],
                        sender_id=user.id,
                        cite_id=None,
                    )
                    # 清除群的recent_files
                    await manage_db.groups.update_one(
                        {"id": data["group_id"]}, {"$set": {"recent_files": []}}
                    )
                    continue
            except WebSocketDisconnect:
                # 将临时用户移出群聊
                await manage_db.groups.update_one(
                    {"id": group_id},
                    {"$pull": {"member_ids": user_id}},
                )
                # 将临时用户从user表中移除
                await manage_db.users.update_one({"id": user_id}, {"$set": {"is_active": "false"}})
                await connection_dic.disconnect(user.id, websocket)
                logger.info(f"{user.id} disconnected")
                break
            except Exception as e:
                # 将临时用户移出群聊
                await manage_db.groups.update_one(
                    {"id": group_id},
                    {"$pull": {"member_ids": user_id}},
                )
                # 将临时用户从user表中移除
                await manage_db.users.update_one({"id": user_id}, {"$set": {"is_active": "false"}})
                await connection_dic.disconnect(user.id, websocket)
                logger.error(
                    f"{user.id} WebSocket 业务数据{data}出错: {str(e)}", exc_info=True
                )
                break
    except Exception as e:
        # 将临时用户移出群聊
        await manage_db.groups.update_one(
            {"id": group_id},
            {"$pull": {"member_ids": user_id}},
        )
        # 将临时用户从user表中移除
        await manage_db.users.update_one({"id": user_id}, {"$set": {"is_active": "false"}})
        await connection_dic.disconnect(user.id, websocket)
        logger.error(f"{user.id} WebSocket 服务出错: {str(e)}", exc_info=True)

@router.websocket("/ws/{token}")
async def websocket_endpoint(
    websocket: WebSocket,
    token,
    manage_db=Depends(get_database),
    chat_db=Depends(get_chat_database),
    _ = Depends(get_translator),
):
    # await websocket.accept()
    # 将token写入头部时的获取校验逻辑(前端暂时无法实现将token写入头部)
    # token = websocket.headers.get("Authorization").split(" ")[1]
    # 获取用户
    user = await get_current_user(token)
    # 获取用户信息
    user_data = await manage_db.users.find_one(
        {
            "id": user.id,
            "is_active": True,
        }
    )
    if not user_data:
        await websocket.close()
        return

    await connection_dic.connect(user, websocket)
    logger.info(f"{user.id} connected")

    try:
        while True:
            try:
                data = await websocket.receive_json()
                manage_db = await get_database()
                chat_db = await get_chat_database()
                # 心跳确认
                if data["type"] == "ping":
                    await websocket.send_text(json.dumps({"type": "pong"}))
                    continue
                logger.critical(f"{user.id} 发送socket数据: {data}")
                # 获取当前消息语言
                lang = data.get("language", "zh-CN") if data else "zh-CN"
                _ = get_translator_func(lang)
                # @普通用户
                group_id = data["group_id"]
                # 查询群成员
                group_data = await manage_db.groups.find_one({"id": group_id})
                if not group_data:
                    # 群组不存在
                    logger.warning(
                        f"发现{user.username}({user.id})违规向群不存在的群{group_id}发送消息"
                    )
                    await websocket.send_text(
                        json.dumps({"type": "alert", "content": _( "群组不存在")})
                    )
                    continue
                if group_data["is_dissolved"] is True:
                    if group_data.get("type", "group") == "group":
                        await websocket.send_text(
                            json.dumps({"type": "alert", "content": _( "群组已解散")})
                        )
                    if group_data.get("type", "group") == "private":
                        await websocket.send_text(
                            json.dumps({"type": "alert", "content": _( "对方已删除聊天")})
                        )
                    continue
                if user.id not in group_data["member_ids"]:
                    # 不在群组中的用户不能向该群组发送消息
                    logger.warning(
                        f"发现{user.username}({user.id})违规向群{group_data['name']}({group_id})发送消息"
                    )
                    await websocket.send_text(
                        json.dumps({"type": "alert", "content": _( "你不在该群组中")})
                    )
                    continue
                group_collection = getattr(chat_db, group_id)

                """
                data 格式
                1.普通文字消息
                {"type":"text","content":消息内容,"group_id":消息所属群id,"cite":引用消息id}
                    1.1 文字内容@LLM或RAG
                    {"type":"text","content":消息内容,"group_id":消息所属群id,model:"bot/DeepSeek-V3/...等"}

                """
                if data["type"] == "text":
                    # 广播用户消息
                    message = await broadcast_and_save_msg(
                        group_collection=group_collection,
                        manage_db=manage_db,
                        content=data["content"],
                        type=data["type"],
                        group_id=group_id,
                        sender_id=user.id,
                        cite_id=None if "cite" not in data else data["cite"],
                        at_list=[] if "at_list" not in data else data["at_list"],
                    )
                    # 向bot提问的逻辑
                    # 在消息处理部分修改：
                    if "model" in data and data["model"] != "":
                        # 去除@模型文本
                        query = data["content"].strip().replace(f"@{data['model']}", "")
                        if "at_list" in data and len(data["at_list"]) != 0:
                            # 同时@群员和模型时，不走问答
                            continue
                        # 启动流式任务
                        asyncio.create_task(
                            background_answer(
                                group_data=group_data,
                                question=query,
                                llm_model=data["model"],
                                group_collection=group_collection,
                                cite_id=message.id,  # 使用消息ID作为引用
                                user_data=user_data,
                                web_search=(
                                    False
                                    if "web_search" not in data
                                    else data["web_search"]
                                ),
                                _=_,
                                lang=lang,
                            )
                        )
                # 用户撤回消息
                elif data["type"] == "revoke":
                    await broadcast_and_save_msg(
                        group_collection=group_collection,
                        manage_db=manage_db,
                        content=data["message_ids"],
                        type=data["type"],
                        group_id=data["group_id"],
                        sender_id=user.id,
                        cite_id=None,
                        save_message=False,
                    )
                    await group_collection.update_one(
                        {"id": data["message_ids"]}, {"$set": {"is_revoke": True}}
                    )
                    message = await group_collection.find_one(
                        {"id": data["message_ids"]}
                    )
                    message.pop("_id")
                    message["content"] = _( "撤回了一条消息")
                    message["type"] = data["type"]
                    message["is_revoke"] = False
                    # 在message_ids后插入一条撤回消息的记录
                    await group_collection.insert_one(message)
                    # 更新群最新消息为撤回消息
                    if group_data["last_message"]["id"] == data["message_ids"]:
                        await manage_db.groups.update_one(
                            {"id": group_data["id"]},
                            {"$set": {"last_message": message}},
                        )
                    # 更新引用撤回消息的引用内容
                    await group_collection.update_many(
                        {"cite.id": data["message_ids"]},
                        {"$set": {"cite.content": _( "消息已撤回")}},
                    )
                    continue

                elif data["type"] == "delete":
                    await broadcast_and_save_msg(
                        group_collection=group_collection,
                        manage_db=manage_db,
                        content=data["message_ids"],
                        type=data["type"],
                        group_id=data["group_id"],
                        sender_id=user.id,
                        cite_id=None,
                        save_message=False,
                    )
                    await group_collection.update_one(
                        {"id": data["message_ids"]}, {"$set": {"is_revoke": True}}
                    )
                    message = await group_collection.find_one(
                        {"id": data["message_ids"]}
                    )
                    message.pop("_id")
                    message["content"] = _( "已删除")
                    message["type"] = data["type"]
                    message["is_revoke"] = False
                    # 在message_ids后插入一条撤回消息的记录
                    await group_collection.insert_one(message)
                    # 更新群最新消息为撤回消息
                    if group_data["last_message"]["id"] == data["message_ids"]:
                        await manage_db.groups.update_one(
                            {"id": group_data["id"]},
                            {"$set": {"last_message": message}},
                        )
                    await group_collection.update_many(
                        {"cite.id": data["message_ids"]},
                        {"$set": {"cite.content": _( "已删除")}},
                    )
                    continue
                elif data["type"] == "new":
                    # 聊聊新话题
                    # 如果group_collection最后一条消息类型不是new，才发送新话题消息
                    last_message = await group_collection.find_one(
                        {}, sort=[("created_at", -1)]
                    )
                    if last_message and last_message["type"] == "new":
                        continue
                    await broadcast_and_save_msg(
                        group_collection=group_collection,
                        manage_db=manage_db,
                        content=_("以下是新话题"),
                        type=data["type"],
                        group_id=data["group_id"],
                        sender_id=user.id,
                        cite_id=None,
                    )
                    # 清除群的recent_files
                    await manage_db.groups.update_one(
                        {"id": data["group_id"]}, {"$set": {"recent_files": []}}
                    )
                    continue
            except WebSocketDisconnect:
                await connection_dic.disconnect(user.id, websocket)
                logger.info(f"{user.id} disconnected")
                break
            except Exception as e:
                logger.error(
                    f"{user.id} WebSocket 业务数据{data}出错: {str(e)}", exc_info=True
                )
                break
    except Exception as e:
        logger.error(f"{user.id} WebSocket 服务出错: {str(e)}", exc_info=True)


@router.put("/stop", description="停止消息输出")
async def stop_output(
    stop_data: StopMessage,
    current_user: User = Depends(get_current_user),
    chat_db=Depends(get_chat_database),
    _ = Depends(get_translator),
):
    try:
        chat_data = await getattr(chat_db, stop_data.group_id).find_one(
            {"id": stop_data.message_id}
        )
        if chat_data is None:
            return error(code=404, message=_("消息不存在"))
        if chat_data["cite"]["sender_id"] != current_user.id:
            return error(code=403, message=_("无权限停止该消息输出"))
        await getattr(chat_db, stop_data.group_id).update_one(
            {"id": stop_data.message_id},
            {"$set": {"stop": True}},
        )
        return success(message=_("已停止输出"))
    except Exception as e:
        logger.error(
            f"{current_user.id} 暂停消息输出{stop_data}出错: {str(e)}", exc_info=True
        )
        return error(code=500, message=_("停止消息输出失败"))
