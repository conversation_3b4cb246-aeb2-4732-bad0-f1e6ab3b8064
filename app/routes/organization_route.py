from fastapi import APIRouter, Depends

from ..database import get_database
from ..models.department import Department
from ..models.organization import Organization
from ..models.user import User
from ..schemas.common import PaginationRequest
from ..schemas.department import DepartmentCreate
from ..schemas.organization import OrganizationCreate
from ..schemas.response import PaginationModel, error, success
from ..utils.auth import get_current_user
from ..utils.log import logger
from ..utils.locales import get_translator

router = APIRouter()


# Organization endpoints
@router.post("/organizations")
async def create_organization(
    org_data: OrganizationCreate,
    current_user: User = Depends(get_current_user),
    db=Depends(get_database),
    _ = Depends(get_translator),
):
    try:
        if not current_user.is_admin:
            return error(code=403, message=_("只有管理员可以创建组织"))

        # 检查组织代码是否已存在
        existing_org = await db.organizations.find_one({"code": org_data.code})
        if existing_org:
            return error(code=409, message=_("组织代码已存在"))

        new_org = Organization(name=org_data.name, code=org_data.code)

        result = await db.organizations.insert_one(new_org.dict())
        new_org.id = str(result.inserted_id)

        return success(data=new_org)
    except Exception as e:
        logger.error(f"创建组织出错: {str(e)}", exc_info=True)
        return error(code=500, message=_("服务端遇到未知异常：{}".format(str(e))))


@router.post("/organizations", description="获取组织列表")
async def list_organizations(
    pagination: PaginationRequest,
    current_user: User = Depends(get_current_user),
    db=Depends(get_database),
    _ = Depends(get_translator),
):
    try:
        skip = (pagination.page - 1) * pagination.page_size

        # 获取总数
        total = await db.organizations.count_documents({})

        # 获取分页数据
        orgs = (
            await db.organizations.find()
            .sort("created_at", -1)
            .skip(skip)
            .limit(pagination.page_size)
            .to_list(None)
        )

        return success(
            data=PaginationModel(
                total=total,
                page=pagination.page,
                page_size=pagination.page_size,
                items=[Organization(**org) for org in orgs],
            )
        )
    except Exception as e:
        logger.error(f"获取组织列表出错: {str(e)}", exc_info=True)
        return error(code=500, message=_("服务端遇到未知异常：{}".format(str(e))))


@router.get("/organizations/{org_id}")
async def get_organization(
    org_id: str,
    current_user: User = Depends(get_current_user),
    db=Depends(get_database),
    _ = Depends(get_translator),
):
    try:
        org = await db.organizations.find_one({"id": org_id})
        if not org:
            return error(code=404, message=_("组织不存在"))
        return success(data=Organization(**org))
    except Exception as e:
        logger.error(f"获取组织详情出错: {str(e)}", exc_info=True)
        return error(code=500, message=_("服务端遇到未知异常：{}".format(str(e))))


@router.delete("/organizations/{org_id}")
async def delete_organization(
    org_id: str,
    current_user: User = Depends(get_current_user),
    db=Depends(get_database),
    _ = Depends(get_translator),
):
    try:
        if not current_user.is_admin:
            return error(code=403, message=_("只有管理员可以删除组织"))
        org = await db.organizations.find_one({"id": org_id})
        if not org:
            return error(code=404, message=_("组织不存在"))

        await db.organizations.delete_one({"id": org_id})
        return success(message=_("组织已删除"))
    except Exception as e:
        logger.error(f"删除组织出错: {str(e)}", exc_info=True)
        return error(code=500, message=_("服务端遇到未知异常：{}".format(str(e))))


# Department endpoints
@router.post("/departments")
async def create_department(
    dept_data: DepartmentCreate,
    current_user: User = Depends(get_current_user),
    db=Depends(get_database),
    _ = Depends(get_translator),
):
    try:
        if not current_user.is_admin:
            return error(code=403, message=_("只有管理员可以创建部门"))

        # 检查组织是否存在
        org = await db.organizations.find_one({"id": dept_data.organization_id})
        if not org:
            return error(code=404, message=_("组织不存在"))

        # 如果有父部门,检查父部门是否存在
        if dept_data.parent_id:
            parent_dept = await db.departments.find_one({"id": dept_data.parent_id})
            if not parent_dept:
                return error(code=404, message=_("父部门不存在"))

        new_dept = Department(
            name=dept_data.name,
            organization_id=dept_data.organization_id,
            parent_id=dept_data.parent_id,
        )

        result = await db.departments.insert_one(new_dept.dict())
        new_dept.id = str(result.inserted_id)

        # 更新组织的部门列表
        await db.organizations.update_one(
            {"id": dept_data.organization_id},
            {"$push": {"department_ids": new_dept.id}},
        )

        # 如果有父部门,更新父部门的子部门列表
        if dept_data.parent_id:
            await db.departments.update_one(
                {"id": dept_data.parent_id}, {"$push": {"child_ids": new_dept.id}}
            )

        return success(data=new_dept)
    except Exception as e:
        logger.error(f"创建部门出错: {str(e)}", exc_info=True)
        return error(code=500, message=_("服务端遇到未知异常：{}".format(str(e))))


@router.post("/organizations/{org_id}/departments", description="获取部门列表")
async def list_departments(
    org_id: str,
    pagination: PaginationRequest,
    current_user: User = Depends(get_current_user),
    db=Depends(get_database),
    _ = Depends(get_translator),
):
    try:
        org = await db.organizations.find_one({"id": org_id})
        if not org:
            return error(code=404, message=_("组织不存在"))

        skip = (pagination.page - 1) * pagination.page_size

        # 获取总数
        total = await db.departments.count_documents({"organization_id": org_id})

        # 获取分页数据
        departments = (
            await db.departments.find({"organization_id": org_id})
            .sort("created_at", -1)
            .skip(skip)
            .limit(pagination.page_size)
            .to_list(None)
        )

        return success(
            data=PaginationModel(
                total=total,
                page=pagination.page,
                page_size=pagination.page_size,
                items=[Department(**dept) for dept in departments],
            )
        )
    except Exception as e:
        logger.error(f"获取部门列表出错: {str(e)}", exc_info=True)
        return error(code=500, message=_("服务端遇到未知异常：{}".format(str(e))))


@router.delete("/departments/{dept_id}")
async def delete_department(
    dept_id: str,
    current_user: User = Depends(get_current_user),
    db=Depends(get_database),
    _ = Depends(get_translator),
):
    try:
        if not current_user.is_admin:
            return error(code=403, message=_("只有管理员可以删除部门"))
        dept = await db.departments.find_one({"id": dept_id})
        if not dept:
            return error(code=404, message=_("部门不存在"))

        # 检查是否有子部门
        child_depts = await db.departments.find_one({"parent_id": dept_id})
        if child_depts:
            return error(code=409, message=_("请先删除子部门"))

        # 检查是否有用户
        users = await db.users.find_one({"department_id": dept_id, "is_active": True})
        if users:
            return error(code=409, message=_("请先移除部门下的用户"))

        # 从组织的部门列表中移除
        await db.organizations.update_one(
            {"id": dept["organization_id"]}, {"$pull": {"department_ids": dept_id}}
        )

        # 如果有父部门,从父部门的子部门列表中移除
        if dept["parent_id"]:
            await db.departments.update_one(
                {"id": dept["parent_id"]}, {"$pull": {"child_ids": dept_id}}
            )

        await db.departments.delete_one({"id": dept_id})
        return success(message=_("部门已删除"))
    except Exception as e:
        logger.error(f"删除部门出错: {str(e)}", exc_info=True)
        return error(code=500, message=_("服务端遇到未知异常：{}".format(str(e))))
