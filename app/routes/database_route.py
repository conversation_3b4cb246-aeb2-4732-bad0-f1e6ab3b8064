import asyncio
import uuid

from fastapi import APIRouter, Depends

from ..database import get_database
from ..models.database import Database
from ..models.file import File
from ..models.user import User
from ..schemas.common import PaginationRequest
from ..schemas.database import (
    DatabaseCreate,
    DatabaseGroupManage,
    DatabaseListFile,
    DatabaseQueryType,
    DatabaseResponse,
    DatabaseSearch,
    DatabaseUpdate,
)
from ..schemas.response import PaginationModel, error, success
from ..utils.auth import get_current_user
from ..utils.database import delete_file_from_database
from ..utils.log import logger
from ..utils.milvusdb import (
    create_collection,
    delete_collection,
    search_documents_in_milvus,
)
from ..utils.remote_model import system_graphbase_manager
from ..utils.search import database_file_search, message_search
from ..utils.locales import get_translator

router = APIRouter()


@router.post("/create", description="创建知识库")
async def create_database(
    create_data: DatabaseCreate,
    current_user: User = Depends(get_current_user),
    db=Depends(get_database),
    _ = Depends(get_translator),
):
    try:
        # 查询用户信息
        user = await db.users.find_one({"id": current_user.id})
        # 当前用户id默认放进create_data的member_ids中（要防止重复）
        if current_user.id not in create_data.member_ids:
            create_data.member_ids.append(current_user.id)
        # 与群绑定创建
        if create_data.group_id not in ["", None]:
            logger.critical(
                f"用户{current_user.id}创建知识库{create_data.name}与群组{create_data.group_id}绑定"
            )
            group = await db.groups.find_one(
                {"id": create_data.group_id, "is_dissolved": False}
            )
            if group:
                database_name = f"_{create_data.group_id.replace('-','')}_file"
                await create_collection(database_name)

                # 创建群默认文档知识库
                file_db = Database(
                    name=_("{}群文档知识库").format(group['name']),
                    v_name=database_name,
                    default_group_id=create_data.group_id,
                    owner_id=current_user.id,
                    owner_name=user["username"],
                    member_ids=[current_user.id],
                    description=_("{}群文档知识库").format(group['name']),
                    type="file",
                    group_ids=[create_data.group_id],
                )
                file_db_data = file_db.model_dump()
                await db.databases.insert_one(file_db_data)
                await db.groups.update_one(
                    {"id": create_data.group_id},
                    {"$push": {"database_ids": file_db.id}},
                )

                # 创建群默认收藏知识库
                database_name = f"_{create_data.group_id.replace('-','')}_col"
                await create_collection(database_name)
                col_db = Database(
                    name=_("{}群收藏知识库").format(group['name']),
                    v_name=database_name,
                    default_group_id=create_data.group_id,
                    owner_id=current_user.id,
                    owner_name=user["username"],
                    member_ids=[current_user.id],  # 创建者自动成为成员
                    description=_("{}群收藏知识库").format(group['name']),
                    type="col",
                    group_ids=[create_data.group_id],
                )
                col_db_data = col_db.model_dump()
                await db.databases.insert_one(col_db_data)
                await db.groups.update_one(
                    {"id": create_data.group_id}, {"$push": {"database_ids": col_db.id}}
                )

                # 创建群默认对话记录知识库
                database_name = f"_{create_data.group_id.replace('-','')}_chat"
                await create_collection(database_name)
                chat_db = Database(
                    name=_("{}群对话记录库").format(group['name']),
                    v_name=database_name,
                    default_group_id=create_data.group_id,
                    owner_id=current_user.id,
                    owner_name=user["username"],
                    member_ids=[current_user.id],
                    description=_("{}群对话记录库").format(group['name']),
                    type="chat",
                    group_ids=[create_data.group_id],
                )
                chat_db_data = chat_db.model_dump()
                await db.databases.insert_one(chat_db_data)
                await db.groups.update_one(
                    {"id": create_data.group_id},
                    {"$push": {"database_ids": chat_db.id}},
                )

            else:
                return success(data=None, message=_("没有查询到绑定群组"))
        else:
            logger.debug(f"用户{current_user.id}创建知识库{create_data.name}与个人绑定")
            # 个人独立创建
            database_id = str(uuid.uuid4())
            database_name = (
                f"_{current_user.id.replace('-','')}_{database_id.replace('-','')}"
            )
            file_db = Database(
                id=database_id,
                name=create_data.name,
                v_name=database_name,
                default_group_id=None,
                owner_id=current_user.id,
                owner_name=user["username"],
                member_ids=create_data.member_ids,  # 创建者自动成为成员
                description=create_data.description,
                type="file",
                group_ids=create_data.group_ids,
                show_details=create_data.show_details,
            )
            file_db_data = file_db.model_dump()
            await db.databases.insert_one(file_db_data)
            # 将知识库绑定到群组
            for group_id in create_data.group_ids:
                await db.groups.update_one(
                    {"id": group_id}, {"$push": {"database_ids": file_db.id}}
                )

        return success(
            data=DatabaseResponse(**file_db_data).model_dump(), message=_("创建成功")
        )
    except Exception as e:
        logger.error(f"创建向量数据库接口出错: {str(e)}", exc_info=True)
        return error(code=500, message=_("服务端遇到未知异常：{}").format(str(e)))


@router.post("/list", description="查询知识库列表")
async def list_databases(
    query_data: DatabaseQueryType,
    current_user: User = Depends(get_current_user),
    db=Depends(get_database),
    _ = Depends(get_translator),
):
    try:
        logger.debug(f"用户{current_user.id}查询知识库列表{query_data}")
        if query_data.page_size > 50:
            return error(code=422, message=_("每页数量不能超过50"))
        skip = (query_data.page - 1) * query_data.page_size

        # 构建查询条件：查询用户拥有的或被授权的知识库
        if query_data.is_owner:
            # 查询用户拥有知识库
            query = {
                "owner_id": current_user.id,
                "is_deleted": False,
            }
        elif query_data.is_owner is False:
            # 查询被授权的知识库
            query = {
                "$and": [
                    {"member_ids": current_user.id},  # 用户拥有的知识库
                    {"owner_id": {"$ne": current_user.id}},  # 被授权的知识库
                ],
                "is_deleted": False,
            }
        else:
            # 查询用户拥有的或被授权的知识库
            query = {
                "$or": [
                    {"owner_id": current_user.id},  # 用户拥有的知识库
                    {"member_ids": current_user.id},  # 被授权的知识库
                ],
                "is_deleted": False,
            }
        if query_data.type:
            query["type"] = query_data.type

        # 获取总数
        total = await db.databases.count_documents(query)

        # 获取分页数据
        databases = (
            await db.databases.find(query)
            .sort("created_at", -1)
            .skip(skip)
            .limit(query_data.page_size)
            .to_list(None)
        )

        # 去除非好友和非群成员的成员和群组id
        for database in databases:
            if database["owner_id"] != current_user.id:
                for member_id in database["member_ids"]:
                    if (
                        member_id != current_user.id
                        and member_id not in current_user.friends
                    ):
                        database["member_ids"].remove(member_id)
                for group_id in database["group_ids"]:
                    group = await db.groups.find_one(
                        {"id": group_id, "is_dissolved": False}
                    )
                    if group:
                        if current_user.id in group["member_ids"]:
                            continue
                    database["group_ids"].remove(group_id)

        return success(
            data=PaginationModel(
                total=total,
                page=query_data.page,
                page_size=query_data.page_size,
                items=[
                    DatabaseResponse(**database).model_dump() for database in databases
                ],
            )
        )
    except Exception as e:
        logger.error(f"获取数据库列表接口出错: {str(e)}", exc_info=True)
        return error(code=500, message=_("服务端遇到未知异常：{}").format(str(e)))


@router.post("/chat_list", description="查询群聊知识库列表")
async def list_chat_databases(
    pagination: PaginationRequest,
    current_user: User = Depends(get_current_user),
    db=Depends(get_database),
    _ = Depends(get_translator),
):
    try:
        if pagination.page_size > 50:
            return error(code=422, message=_("每页数量不能超过50"))
        skip = (pagination.page - 1) * pagination.page_size

        # 构建查询条件：查询用户拥有的或被授权的知识库
        query = {
            "$or": [
                {"owner_id": current_user.id},  # 用户拥有的知识库
                {"member_ids": current_user.id},  # 被授权的知识库
            ],
            "type": "chat",  # 只查询文档知识库
            "is_deleted": False,
        }

        # 获取总数
        total = await db.databases.count_documents(query)

        # 获取分页数据
        databases = (
            await db.databases.find(query)
            .sort("created_at", -1)
            .skip(skip)
            .limit(pagination.page_size)
            .to_list(None)
        )

        # 去除非好友和非群成员的成员和群组id
        for database in databases:
            if database["owner_id"] != current_user.id:
                for member_id in database["member_ids"]:
                    if (
                        member_id != current_user.id
                        and member_id not in current_user.friends
                    ):
                        database["member_ids"].remove(member_id)
                for group_id in database["group_ids"]:
                    group = await db.groups.find_one(
                        {"id": group_id, "is_dissolved": False}
                    )
                    if group:
                        if current_user.id in group["member_ids"]:
                            continue
                    database["group_ids"].remove(group_id)

        return success(
            data=PaginationModel(
                total=total,
                page=pagination.page,
                page_size=pagination.page_size,
                items=[
                    DatabaseResponse(**database).model_dump() for database in databases
                ],
            )
        )
    except Exception as e:
        logger.error(f"获取数据库列表接口出错: {str(e)}", exc_info=True)
        return error(code=500, message=_("服务端遇到未知异常：{}").format(str(e)))


@router.post("/list_owned", description="查询自有权知识库列表")
async def list_owned_databases(
    query_data: DatabaseQueryType,
    current_user: User = Depends(get_current_user),
    db=Depends(get_database),
    _ = Depends(get_translator),
):
    try:
        logger.debug(f"用户{current_user.id}查询自有知识库列表{query_data}")
        if query_data.page_size > 50:
            return error(code=422, message=_("每页数量不能超过50"))
        skip = (query_data.page - 1) * query_data.page_size
        query = {"owner_id": current_user.id, "is_deleted": False}
        if query_data.type:
            query["type"] = query_data.type
        # 获取总数
        total = await db.databases.count_documents(query)

        # 获取分页数据
        databases = (
            await db.databases.find(query)
            .sort("created_at", -1)
            .skip(skip)
            .limit(query_data.page_size)
            .to_list(None)
        )

        # 去除非好友和非群成员的成员和群组id
        for database in databases:
            if database["owner_id"] != current_user.id:
                for member_id in database["member_ids"]:
                    if (
                        member_id != current_user.id
                        and member_id not in current_user.friends
                    ):
                        database["member_ids"].remove(member_id)
                for group_id in database["group_ids"]:
                    group = await db.groups.find_one(
                        {"id": group_id, "is_dissolved": False}
                    )
                    if group:
                        if current_user.id in group["member_ids"]:
                            continue
                    database["group_ids"].remove(group_id)

        return success(
            data=PaginationModel(
                total=total,
                page=query_data.page,
                page_size=query_data.page_size,
                items=[
                    DatabaseResponse(**database).model_dump() for database in databases
                ],
            )
        )
    except Exception as e:
        logger.error(f"获取自有数据库列表接口出错: {str(e)}", exc_info=True)
        return error(code=500, message=_("服务端遇到未知异常：{}").format(str(e)))


@router.post("/list_authed", description="查询被授权知识库列表")
async def list_authed_databases(
    query_data: DatabaseQueryType,
    current_user: User = Depends(get_current_user),
    db=Depends(get_database),
    _ = Depends(get_translator),
):
    try:
        logger.debug(f"用户{current_user.id}查询被授权知识库列表{query_data}")
        if query_data.page_size > 50:
            return error(code=422, message=_("每页数量不能超过50"))
        skip = (query_data.page - 1) * query_data.page_size

        # 构建查询条件：查询用户拥有的或被授权的知识库（member_ids包含用户id且owner_id不等于用户id）
        query = {
            "member_ids": current_user.id,
            "owner_id": {"$ne": current_user.id},
            "is_deleted": False,
        }
        if query_data.type:
            query["type"] = query_data.type
        # 获取总数
        total = await db.databases.count_documents(query)

        # 获取分页数据
        databases = (
            await db.databases.find(query)
            .sort("created_at", -1)
            .skip(skip)
            .limit(query_data.page_size)
            .to_list(None)
        )

        # 去除非好友和非群成员的成员和群组id
        for database in databases:
            if database["owner_id"] != current_user.id:
                for member_id in database["member_ids"]:
                    if (
                        member_id != current_user.id
                        and member_id not in current_user.friends
                    ):
                        database["member_ids"].remove(member_id)
                for group_id in database["group_ids"]:
                    group = await db.groups.find_one(
                        {"id": group_id, "is_dissolved": False}
                    )
                    if group:
                        if current_user.id in group["member_ids"]:
                            continue
                    database["group_ids"].remove(group_id)

        return success(
            data=PaginationModel(
                total=total,
                page=query_data.page,
                page_size=query_data.page_size,
                items=[
                    DatabaseResponse(**database).model_dump() for database in databases
                ],
            )
        )
    except Exception as e:
        logger.error(f"获取被授权数据库列表接口出错: {str(e)}", exc_info=True)
        return error(code=500, message=_("服务端遇到未知异常：{}").format(str(e)))


@router.post("/files", description="查询知识库文档列表")
async def list_files(
    search_data: DatabaseListFile,
    current_user: User = Depends(get_current_user),
    db=Depends(get_database),
    _ = Depends(get_translator),
):
    try:
        logger.debug(f"用户{current_user.id}查询知识库文档列表{search_data}")
        if search_data.page_size > 50:
            return error(code=422, message=_("每页数量不能超过50"))

        database = await db.databases.find_one(
            {"id": search_data.database_id, "is_deleted": False}
        )

        if not database:
            return error(code=404, message=_("知识库不存在"))

        if (
            current_user.id not in database["member_ids"]
            and current_user.id != database["owner_id"]
        ):
            logger.warning(
                f"发现用户{current_user.id}非法访问数据库{search_data.database_id}"
            )
            return error(code=403, message=_("权限不足"))

        if search_data.file_name is None:
            skip = (search_data.page - 1) * search_data.page_size
            # 获取总数
            total = await db.files.count_documents(
                {"vectordb_id": search_data.database_id, "is_deleted": False}
            )
            # 获取分页数据
            files = (
                await db.files.find(
                    {
                        "vectordb_id": search_data.database_id,
                        "is_deleted": False,
                    }
                )
                .sort("created_at", -1)
                .skip(skip)
                .limit(search_data.page_size)
                .to_list(None)
            )
        else:
            # 获取全部file的list
            all_files = await database_file_search(
                file_name=search_data.file_name, database_id=search_data.database_id
            )
            # 获取总数
            total = len(all_files)
            # 开始分页
            start_index = (search_data.page - 1) * search_data.page_size
            end_index = start_index + search_data.page_size
            # 获取分页后的list
            files = all_files[start_index:end_index]

        response_data = PaginationModel(
            total=total,
            page=search_data.page,
            page_size=search_data.page_size,
            items=[
                File(**file).model_dump(exclude=["local_file_path", "cloud_file_path"])
                for file in files
            ],
        )
        return success(data=response_data)
    except Exception as e:
        logger.error(f"获取文件列表接口出错: {str(e)}", exc_info=True)
        return error(code=500, message=_("服务端遇到未知异常：{}").format(str(e)))


@router.get("/{database_id}", description="查询知识库详情")
async def search_database(
    database_id: str,
    current_user: User = Depends(get_current_user),
    db=Depends(get_database),
    _ = Depends(get_translator),
):
    try:
        logger.debug(f"用户{current_user.id}查询知识库{database_id}详情")
        database = await db.databases.find_one(
            {
                "id": database_id,
                "is_deleted": False,
                "$or": [{"owner_id": current_user.id}, {"member_ids": current_user.id}],
            }
        )

        if not database:
            return error(code=404, message=_("知识库不存在"))

        # 查看知识库详情时，展示与自己相关的成员和拥有的群
        member_data = await db.users.find(
            {"id": {"$in": database["member_ids"]}, "is_active": True}
        ).to_list(None)
        for member in member_data:
            # 不是好友不展示
            if member["id"] not in current_user.friends:
                database["member_ids"].remove(member["id"])
        group_data = await db.groups.find(
            {"id": {"$in": database["group_ids"]}, "is_dissolved": False}
        ).to_list(None)
        for group in group_data:
            # 不是群主不展示
            if current_user.id != group["owner_id"]:
                database["group_ids"].remove(group["id"])
        return success(data=Database(**database).model_dump())
    except Exception as e:
        logger.error(f"获取数据库接口出错: {str(e)}", exc_info=True)
        return error(code=500, message=_("服务端遇到未知异常：{}").format(str(e)))


# 更新知识库信息
@router.put("/{database_id}", description="更新知识库信息")
async def update_database(
    database_id: str,
    database_data: DatabaseUpdate,
    current_user: User = Depends(get_current_user),
    db=Depends(get_database),
    _ = Depends(get_translator),
):
    try:
        logger.debug(
            f"用户({current_user.id})更新知识库({database_id})信息{database_data}"
        )
        # 查询知识库
        database = await db.databases.find_one({"id": database_id, "is_deleted": False})
        if not database:
            return error(code=404, message=_("知识库不存在"))

        # 检查权限
        if (
            current_user.id != database["owner_id"]
            and current_user.id not in database["member_ids"]
        ):
            logger.warning(
                f"发现用户 {current_user.id} 越权使用知识库 {database['id']}"
            )
            return error(code=403, message=_("无权修改此知识库"))

        # 当前用户id默认放进database_data的member_ids中（要防止重复）
        if database["owner_id"] not in database_data.member_ids:
            database_data.member_ids.append(database["owner_id"])

        # 知识库解除授权时，解绑知识库被绑定的用户所有群组绑定的群
        # 确定解除授权用户名单
        delete_user_list = set(database["member_ids"]) - set(database_data.member_ids)
        if delete_user_list:
            for user_id in delete_user_list:
                # 查询用户所有群组
                user_groups = await db.groups.find(
                    {"owner_id": user_id, "is_dissolved": False}
                ).to_list(None)
                # 解绑知识库被绑定的用户所有群组绑定的群
                for group in user_groups:
                    if database_id in group["database_ids"]:
                        await db.groups.update_one(
                            {"id": group["id"]},
                            {"$pull": {"database_ids": database_id}},
                        )
                        # 解绑知识库绑定的群组
                        await db.databases.update_one(
                            {"id": database_id},
                            {"$pull": {"group_ids": group["id"]}},
                        )
        # 更新数据
        update_data = {
            k: v for k, v in database_data.model_dump().items() if v is not None
        }
        update_data["member_ids"].append(current_user.id)
        await db.databases.update_one({"id": database_id}, {"$set": update_data})

        # 返回更新后的数据
        updated_robot = await db.databases.find_one(
            {
                "id": database_id,
                "is_deleted": False,
            }
        )
        return success(
            data=DatabaseResponse(**updated_robot).model_dump(), message=_("更新成功")
        )
    except Exception as e:
        logger.error(f"知识库{database_id}更新失败: {str(e)}", exc_info=True)
        return error(code=500, message=_("服务端遇到未知异常：{}").format(str(e)))


@router.delete("/{database_id}", description="删除知识库")
async def delete_database(
    database_id: str,
    current_user: User = Depends(get_current_user),
    db=Depends(get_database),
    _ = Depends(get_translator),
):
    try:
        logger.debug(f"用户({current_user.id})请求删除知识库({database_id})")
        database = await db.databases.find_one({"id": database_id, "is_deleted": False})

        if not database:
            return error(code=404, message=_("数据库不存在"))

        if current_user.id != database["owner_id"]:
            logger.warning(f"发现用户{current_user.id}非法删除数据库{database_id}")
            return error(code=403, message=_("您无权限删除此共享知识库"))

        # 解绑知识库绑定的群
        for group_id in database["group_ids"]:
            await db.groups.update_one(
                {"id": group_id}, {"$pull": {"database_ids": database_id}}
            )

        # # 删除文件
        # files = await db.files.find(
        #     {"vectordb_id": database_id, "is_deleted": False}
        # ).to_list(None)

        # semaphore = asyncio.Semaphore(20)  # 并发限制

        # async def limited_delete_file(file):
        #     async with semaphore:
        #         return await delete_file_from_database(
        #             file_id=file["id"],
        #             database=database,
        #             current_user=current_user,
        #             file_size=file["file_size"],
        #             delete_database=False,
        #         )

        # # 创建并行删除任务
        # delete_tasks = [limited_delete_file(file) for file in files]
        # # 并行执行所有删除任务
        # await asyncio.gather(*delete_tasks)

        # await delete_collection(database["v_name"])

        # # 删除neo4j数据库
        # if await system_graphbase_manager.check_graph_database_exists(database_id):
        #     await system_graphbase_manager.delete_graph_database(database_id)

        await db.databases.update_one(
            {"id": database_id}, {"$set": {"is_deleted": True}}
        )

        return success(message=_("删除成功"))
    except Exception as e:
        logger.error(f"删除数据库接口出错: {str(e)}", exc_info=True)
        return error(code=500, message=_("服务端遇到未知异常：{}").format(str(e)))


@router.post("/add_group", description="绑定群组到知识库")
async def add_group_to_database(
    add_data: DatabaseGroupManage,
    current_user: User = Depends(get_current_user),
    db=Depends(get_database),
    _ = Depends(get_translator),
):
    try:
        logger.debug(f"用户{current_user.id}绑定群组到知识库{add_data}")
        # 查询知识库
        database = await db.databases.find_one(
            {"id": add_data.database_id, "is_deleted": False}
        )
        if not database:
            return error(code=404, message=_("知识库不存在"))

        # 检查权限
        # if current_user.id != database["owner_id"]:
        #     logger.warning(
        #         f"发现用户 {current_user.id} 越权使用知识库 {database['id']}"
        #     )
        #     return error(code=403, message="无权修改此知识库")

        # 更新数据
        group_data = await db.groups.find(
            {"id": {"$in": add_data.group_ids}, "is_dissolved": False}
        ).to_list(None)
        if any(group["owner_id"] != current_user.id for group in group_data):
            logger.warning(
                f"发现用户 {current_user.id} 越权绑定知识库 {database['name']}到非自有群"
            )
            return error(
                code=403, message=f"无权绑定到{database['name']}知识库到非自有群"
            )
        await db.databases.update_one(
            {"id": add_data.database_id, "is_deleted": False},
            {"$push": {"group_ids": {"$each": add_data.group_ids}}},
        )
        for group_id in add_data.group_ids:
            await db.groups.update_one(
                {"id": group_id}, {"$push": {"database_ids": add_data.database_id}}
            )

        return success(message=_("绑定成功"))
    except Exception as e:
        logger.error(
            f"知识库{add_data.database_id}绑定群组{add_data.group_ids}失败: {str(e)}",
            exc_info=True,
        )
        return error(code=500, message=f"绑定知识库失败")


@router.post("/delete_group", description="解绑知识库群组")
async def delete_group_from_database(
    add_data: DatabaseGroupManage,
    current_user: User = Depends(get_current_user),
    db=Depends(get_database),
    _ = Depends(get_translator),
):
    try:
        logger.debug(f"用户{current_user.id}解绑知识库群组{add_data}")
        # 查询知识库
        database = await db.databases.find_one(
            {"id": add_data.database_id, "is_deleted": False}
        )
        if not database:
            return error(code=404, message=_("知识库不存在"))

        # 检查权限
        # if current_user.id != database["owner_id"]:
        #     logger.warning(
        #         f"发现用户 {current_user.id} 越权使用知识库 {database['id']}"
        #     )
        #     return error(code=403, message="无权修改此知识库")

        group_data = await db.groups.find(
            {"id": {"$in": add_data.group_ids}, "is_dissolved": False}
        ).to_list(None)
        if any(group["owner_id"] != current_user.id for group in group_data):
            logger.warning(
                f"发现用户 {current_user.id} 越权解绑非自有群{database['name']}知识库 "
            )
            return error(code=403, message=f"无权解绑非自有群{database['name']}知识库")
        # 更新数据
        await db.databases.update_one(
            {"id": add_data.database_id, "is_deleted": False},
            {"$pull": {"group_ids": {"$in": add_data.group_ids}}},
        )
        for group_id in add_data.group_ids:
            await db.groups.update_one(
                {"id": group_id}, {"$pull": {"database_ids": add_data.database_id}}
            )

        return success(message=_("解绑成功"))
    except Exception as e:
        logger.error(
            f"知识库{add_data.database_id}解绑群组{add_data.group_ids}失败: {str(e)}",
            exc_info=True,
        )
        return error(code=500, message=_("解绑失败"))


@router.post("/file_search", description="搜索知识库文档")
async def search_database_file(
    search_data: DatabaseSearch,
    current_user: User = Depends(get_current_user),
    manage_db=Depends(get_database),
    _ = Depends(get_translator),
):
    """文档搜索接口
    Args:
        search_data: 包含搜索内容和分页参数的请求体
        current_user: 通过JWT token解析的当前用户对象
        manage_db: MongoDB数据库依赖
    """
    try:
        logger.debug(f"用户{current_user.id}搜索知识库文档{search_data}")
        # 分页参数校验
        if search_data.page_size > 50:
            return error(code=422, message=_("每页数量不能超过50"))

        # 执行Milvus向量数据库搜索
        all_texts = await search_documents_in_milvus(search_data.content, current_user)
        total = len(all_texts)

        # 分页处理
        start_index = (search_data.page - 1) * search_data.page_size
        end_index = start_index + search_data.page_size
        texts = all_texts[start_index:end_index]

        texts_with_files = texts.copy()
        for text_with_file in texts_with_files:
            # 根据文档ID查询文件元数据
            file = await manage_db.files.find_one(
                {"id": text_with_file["doc_uuid"], "is_deleted": False}
            )
            if file:
                # 将MongoDB文档转换为File模型对象
                text_with_file["file"] = File(**file).model_dump(
                    exclude=["local_file_path", "cloud_file_path"]
                )
            else:
                text_with_file["file"] = None  # 处理文档不存在的情况

        # 构建分页响应
        response_data = PaginationModel(
            total=total,
            page=search_data.page,
            page_size=search_data.page_size,
            items=texts_with_files,
        ).dict()

        return success(data=response_data)

    except Exception as e:
        # 异常处理与日志记录
        logger.error(f"搜索文件出错: {str(e)}", exc_info=True)
        return error(code=500, message=_("服务端遇到未知异常：{}").format(str(e)))


@router.post("/chat_search", description="搜索知识库对话")
async def search_database_chat(
    search_data: DatabaseSearch,
    current_user: User = Depends(get_current_user),
    _ = Depends(get_translator),
):
    try:
        logger.debug(f"用户{current_user.id}搜索知识库对话{search_data}")
        # 分页参数校验
        if search_data.page_size > 50:
            return error(code=422, message=_("每页数量不能超过50"))

        # 执行Milvus向量数据库搜索
        all_texts = await message_search(search_data.content, current_user)
        total = len(all_texts)

        # 分页处理
        start_index = (search_data.page - 1) * search_data.page_size
        end_index = start_index + search_data.page_size
        texts = all_texts[start_index:end_index]

        # 构建分页响应
        response_data = PaginationModel(
            total=total,
            page=search_data.page,
            page_size=search_data.page_size,
            items=texts,
        ).dict()

        return success(data=response_data)

    except Exception as e:
        # 异常处理与日志记录
        logger.error(f"搜索文件出错: {str(e)}", exc_info=True)
        return error(code=500, message=_("服务端遇到未知异常：{}").format(str(e)))
