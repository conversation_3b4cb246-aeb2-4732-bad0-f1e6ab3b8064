from fastapi import APIRouter, BackgroundTasks, Depends

from ..database import get_chat_database, get_database
from ..models.collection import Collection
from ..models.user import User
from ..schemas.collection import (
    CollectionCreate,
    CollectionResponse,
    GetCollectionInfo,
    GetCollectionList,
    SearchCollection,
)
from ..schemas.message import MessageData
from ..schemas.response import PaginationModel, error, success
from ..utils.auth import get_current_user
from ..utils.collection import save_collection_to_database
from ..utils.log import logger
from ..utils.milvusdb import (
    delete_items_by_doc_uuid,
    search_collection_in_milvus,
)
from ..utils.locales import get_translator

router = APIRouter()


@router.post("/add", description="添加对话记录到收藏")
async def add_collection(
    background_tasks: BackgroundTasks,
    collection: CollectionCreate,
    current_user: User = Depends(get_current_user),
    manage_db=Depends(get_database),
    chat_db=Depends(get_chat_database),
    _=Depends(get_translator),
):
    try:
        # 如果收藏内容为空，不允许收藏
        if not isinstance(collection.content, list) or len(collection.content) == 0:
            return error(code=422, message=_("收藏内容不能为空"))
        # 确保 user_collections 集合存在
        group = await manage_db.groups.find_one(
            {"id": collection.group_id, "is_dissolved": False}
        )
        if not group:
            return error(code=404, message=_("群组不存在"))
        if group["owner_id"] != current_user.id:
            logger.warning(
                f"发现{current_user.id} 越权对{collection.group_id}群添加收藏"
            )
            return error(code=403, message=_("只有群主才能添加对话记录到收藏"))

        # 通过群id获取数据库id
        database_v_name = f"_{collection.group_id.replace('-', '')}_col"
        database = await manage_db.databases.find_one(
            {
                "v_name": database_v_name,
                "is_deleted": False,
            }
        )
        if database is None:
            logger.error(f"未找到v_name为 {database_v_name} 的数据库")
            return error(code=404, message=_("未找到v_name为{}的数据库".format(database_v_name)))
        database_id = database.get("id")

        group_collection = getattr(chat_db, collection.group_id)
        # 将消息内容保存到txt文件
        message_ids = collection.content
        messages = []
        # 如果收藏的内容不是文字内容，则显示“非文字收藏”
        abstract = _("非文字收藏")
        for message_id in message_ids:
            message = await group_collection.find_one({"id": message_id})
            if message["type"] == "text":
                # 文本消息拿前十个字作为摘要
                abstract = f'{message.get("content")[:10]}...'
            elif message.get("transfer", None):
                # 非文本消息如果有转文字内容就拿前十个字作为摘要
                abstract = f'{message.get("transfer")[:10]}...'
            if message:
                messages.append(message.get("content", ""))
        # 创建收藏记录，使用UUID生成id
        new_collection = Collection(
            content=collection.content,
            database_id=database_id,
            group_id=collection.group_id,
            is_deleted=False,
            user_id=current_user.id,
            abstract=abstract,
        )

        await manage_db.user_collections.insert_one(
            new_collection.model_dump()
        )  # 将收藏记录插入数据库
        background_tasks.add_task(
            save_collection_to_database, new_collection.id, messages, database_v_name
        )
        return success(message=_("收藏成功"))
    except Exception as e:
        logger.error(f"添加收藏接口出错: {str(e)}", exc_info=True)
        return error(code=500, message=_("服务端遇到未知异常：{}".format(str(e))))


@router.delete("/remove/{collection_id}", description="删除收藏")  # 修改为 delete 请求
async def remove_collection(
    collection_id,
    current_user: User = Depends(get_current_user),
    manage_db=Depends(get_database),  # 引入 manage_db 用于获取 database_v_name
    _=Depends(get_translator),
):
    try:
        # 获取对应的收藏记录
        collection = await manage_db.user_collections.find_one(
            {
                "id": collection_id,
                "is_deleted": False,
            }
        )
        if collection is None:
            return error(code=404, message=_("未找到对应的收藏记录"))

        # 检查用户是否为收藏记录的所有者
        group_id = collection.get("group_id")
        gourp = await manage_db.groups.find_one({"id": group_id, "is_dissolved": False})
        if current_user.id != gourp.get("owner_id"):
            return error(code=403, message=_("您没有权限删除此收藏记录"))

        # 通过群id获取数据库id
        database_v_name = f"_{collection['group_id'].replace('-', '')}_col"

        # 调用 milvusdb.py 中的函数删除向量数据库中的数据
        await delete_items_by_doc_uuid(database_v_name, collection_id)

        manage_db.user_collections.update_one(
            {"user_id": current_user.id, "id": collection_id},
            {"$set": {"is_deleted": True}},
        )
        return success(message=_("删除成功"))
    except Exception as e:
        logger.error(f"删除收藏接口出错: {str(e)}", exc_info=True)
        return error(code=500, message=_("服务端遇到未知异常：{}".format(str(e))))


@router.post("/get_collection_list", description="根据群id返回收藏记录")
async def get_collection_list(
    get_list: GetCollectionList,
    manage_db=Depends(get_database),
    current_user: User = Depends(get_current_user),
    _=Depends(get_translator),
):
    try:
        if get_list.page_size > 50:
            return error(code=422, message=_("每页数量不能超过50"))

        skip = (get_list.page - 1) * get_list.page_size

        # 检查用户是否为群组成员
        group = await manage_db.groups.find_one(
            {"id": get_list.group_id, "is_dissolved": False}
        )

        if not group:
            return error(code=404, message=_("群组不存在或已解散"))

        # if current_user.id not in group["member_ids"]:
        #     return error(code=403, message="您不在该群组中")

        # 获取总数
        total = await manage_db.user_collections.count_documents(
            {"group_id": get_list.group_id, "is_deleted": False}
        )

        # 根据群id从manage_db.user_collections中获取所有group_id为get_list.group_id的记录
        collections = (
            await manage_db.user_collections.find(
                {"group_id": get_list.group_id, "is_deleted": False}
            )
            .skip(skip)
            .sort("created_at", -1)
            .limit(get_list.page_size)
            .to_list(None)
        )

        return success(
            data=PaginationModel(
                total=total,
                page=get_list.page,
                page_size=get_list.page_size,
                items=[
                    CollectionResponse(**collection).model_dump()
                    for collection in collections
                ],
            )
        )

    except Exception as e:
        logger.error(f"获取收藏记录时发生错误: {str(e)}", exc_info=True)
        return error(code=500, message=_("服务端遇到未知异常：{}".format(str(e))))


@router.post("/info", description="根据收藏记录id获取收藏内容")
async def get_collection_info(
    info: GetCollectionInfo,
    manage_db=Depends(get_database),
    chat_db=Depends(get_chat_database),
    _=Depends(get_translator),
):
    try:
        # 根据收藏记录id查找对应的收藏项
        collection = await manage_db.user_collections.find_one(
            {
                "id": info.collection_id,
                "is_deleted": False,
            }
        )
        if collection is None:
            return error(code=404, message=_("未找到对应的收藏记录"))

        # 取出收藏项中的content（消息id列表）
        message_ids = collection.get("content", [])

        # 获取对话记录集合
        group_collection = getattr(chat_db, info.group_id)

        # 查找消息id对应的消息内容
        messages = []
        for message_id in message_ids:
            message = await group_collection.find_one({"id": message_id})
            if message:
                # message.pop('_id', None)
                messages.append(message)
        # 按时间顺序排列
        messages.sort(key=lambda x: x.get("created_at", 0))

        return success(
            data=[MessageData(**message).model_dump() for message in messages]
        )
    except Exception as e:
        logger.error(f"获取收藏内容接口出错: {str(e)}", exc_info=True)
        return error(code=500, message=_("服务端遇到未知异常：{}".format(str(e))))


@router.post("/search", description="根据关键词搜索收藏记录")
async def search_collection(
    search: SearchCollection,
    current_user: User = Depends(get_current_user),
    manage_db=Depends(get_database),
    _=Depends(get_translator),
):
    try:
        if search.page_size > 50:
            return error(code=422, message=_("每页数量不能超过50"))

        all_collection_ids = await search_collection_in_milvus(
            search.content, current_user
        )

        total = len(all_collection_ids)

        start_index = (search.page - 1) * search.page_size
        end_index = start_index + search.page_size
        collection_ids = all_collection_ids[start_index:end_index]

        # 根据collection_ids找到对应的收藏记录
        collections = await manage_db.user_collections.find(
            {"id": {"$in": collection_ids}, "is_deleted": False}
        ).to_list(None)
        # 查询收藏相关群组
        groups = await manage_db.groups.find(
            {"id": {"$in": [collection["group_id"] for collection in collections]}}
        ).to_list(None)
        # 构造群id与群名映射
        group_dic = {group["id"]: group["name"] for group in groups}
        # 拼接群名
        for collection in collections:
            collection["group_name"] = group_dic.get(collection["group_id"], None)

        response_data = PaginationModel(
            start_index=start_index,
            total=total,
            page=search.page,
            page_size=search.page_size,
            items=[
                CollectionResponse(**collection).model_dump()
                for collection in collections
            ],
        ).dict()

        return success(data=response_data)

    except Exception as e:
        logger.error(f"搜索收藏记录时发生错误: {str(e)}", exc_info=True)
        return error(code=500, message=_("服务端遇到未知异常：{}".format(str(e))))
