import json
from datetime import datetime
from fastapi import APIRouter, Depends, Request
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates

from ..config import settings
from ..database import get_chat_database, get_database
from ..models.group import Group, GroupAgentSetting, GroupForward, Webhook
from ..models.user import User
from ..schemas.database import DatabaseCreate, DatabaseResponse
from ..schemas.group import (
    ForwardMessage,
    GroupCreate,
    GroupCreateResponse,
    GroupDatabaseManage,
    GroupDetailResponse,
    GroupForwardMessage,
    GroupInquiry,
    GroupMemberManage,
    GroupMessageSearch,
    GroupReadUpdate,
    GroupResponse,
    MessageSearch,
    ResponseEvaluate,
    FAQSearch
)
from ..schemas.message import MessageData, MessageResponse
from ..schemas.response import PaginationModel, error, success
from ..schemas.user import UserResponse
from ..utils.auth import get_current_user
from ..utils.log import logger
from ..utils.search import (
    group_message_search,
    message_group_search,
    name_group_search,
    user_group_search,
)
from .database_route import create_database
from ..utils.websocket_manager import connection_dic
from ..utils.locales import get_translator

router = APIRouter()

# 设置模板目录
templates = Jinja2Templates(directory="templates")


@router.post("/create", description="创建群聊")
async def create_group(
    group_data: GroupCreate,
    current_user: User = Depends(get_current_user),
    db=Depends(get_database),
    _ = Depends(get_translator),
):
    try:
        if current_user.id not in group_data.member_ids:
            # 防止前端漏传创建者到成员列表
            group_data.member_ids.append(current_user.id)
        logger.debug(f"{current_user.id} 创建群{group_data}")
        if len(group_data.member_ids) == 2 and group_data.type == "private":
            exist_private_group = await db.groups.find_one(
                {
                    "$and": [
                        {"member_ids": group_data.member_ids[0]},
                        {"member_ids": group_data.member_ids[1]},
                    ],
                    "type": "private",
                    "is_dissolved": False,
                }
            )
            if exist_private_group:
                exist_private_group["member_ids"].remove(current_user.id)
                # 用户已注销时也正常打开聊天框
                user = await db.users.find_one(
                    {"id": exist_private_group["member_ids"][0]}
                )
                exist_private_group["name"] = user["username"]
                return success(
                    code=308,
                    data=GroupCreateResponse(**exist_private_group).model_dump(),
                )
        # 用户可以创建(拥有同名群组，除了默认的群)(避免群主移交时出现重名的情况的麻烦)
        ai_group = await db.groups.find_one(
            {"name": group_data.name, "owner_id": current_user.id}
        )
        if group_data.name.strip() == settings.DEFAULT_GROUP_NAME and ai_group:
            return error(code=409, message=_("不能创建与默认群聊同名群聊"))
        # # 创建新群组
        new_group = Group(
            name="" if group_data.type == "private" else group_data.name,
            owner_id=current_user.id,
            database_ids=group_data.database_ids,
            member_ids=group_data.member_ids,  # 添加群主为群组成员
            agent_setting=group_data.agent_setting,
            type=group_data.type,
        )
        new_group_data = new_group.model_dump()
        # 一人建群时使用群组id作为群名
        if group_data.name.strip() == "" and len(group_data.member_ids) == 1:
            new_group_data['name'] = new_group_data['id']
        # 创建群聊时默认写入最新消息，以便在列表查询时将新建群聊排在前面
        new_group_data['last_message'] = {
                "created_at": new_group.created_at,
                "id": "",
                "type": "",
                "content": "",
                "transfer": "",
                "duration": None,
                "cite": {},
                "sender_id": "",
                "sender_username": "",
                "sender_avatar": "",
                "group_id": "",
            }
        # 为群组设置默认AI服务
        if new_group_data['agent_setting']['model'] == "":
            new_group_data['agent_setting']['model'] = _("DeepSeek（收费）")
        await db.groups.insert_one(new_group_data)
        if not group_data.type == "private":
            db_data = await create_database(
                create_data=DatabaseCreate(
                    name=f"{group_data.name}群文档知识库",
                    group_id=new_group.id,
                    description=f"{group_data.name}群文档知识库",
                    member_ids=group_data.member_ids,
                    group_ids=[new_group.id],
                ),
                current_user=current_user,
                db=db,
                _=_,
            )
            if not db_data["data"]:
                raise Exception("创建失败")
        else:
            new_group_data['member_ids'].remove(current_user.id)
            # 用户已注销时也正常打开聊天框
            user = await db.users.find_one({"id": new_group_data['member_ids'][0]})
            new_group_data['name'] = user["username"]
        new_group_data["last_message"] = MessageResponse(**new_group_data["last_message"]).model_dump()
        return success(data=GroupCreateResponse(**new_group_data).model_dump(), message=_("创建成功"))
    except Exception as e:
        logger.error(
            f"{current_user.id} 创建群组{group_data}出错: {str(e)}", exc_info=True
        )
        return error(code=500, message=_("创建群组出错"))


# @router.post("/{group_uuid}/join",description="申请加入群聊")
# async def join_group(
#     group_uuid: str,
#     current_user: User = Depends(get_current_user),
#     db = Depends(get_database)
# ):
#     try:
#         # 查找群组
#         group = await db.groups.find_one({
#             "id": group_uuid,
#             "is_dissolved": False
#         })

#         if not group:
#             return error(code=404, message="群组不存在或已解散")

#         # 检查用户是否已经在群组中
#         if current_user.id in group["member_ids"]:
#             return error(code=400, message="您已经在群组中")

#         # 添加用户到群组
#         await db.groups.update_one(
#             {"id": group_uuid},
#             {"$push": {"member_ids": current_user.id}}
#         )

#         return success(data={}, message="加入群组成功")
#     except Exception as e:
#         logger.error(f"{current_user.id} 加入群组出错: {str(e)}",exc_info=True)
#         return error(message="加入群组出错")


@router.post("/add_user_to_group", description="添加用户到群聊")
async def add_member_to_group(
    post_data: GroupMemberManage,
    current_user: User = Depends(get_current_user),
    db=Depends(get_database),
    _ = Depends(get_translator),
):
    try:
        # 查找群组
        group = await db.groups.find_one(
            {
                "id": post_data.group_id,
                "is_dissolved": False,
            }
        )

        for user_id in post_data.user_ids:
            # 检查添加的用户是否存在
            user = await db.users.find_one({"id": user_id, "is_active": True})
            if user_id in group["member_ids"]:
                return error(
                    code=409, message=_("用户{}已在群聊中，不能重复添加").format(user['username'])
                )
            if not user:
                return error(code=404, message=_("用户{}不存在或已注销")).format(user_id)

        if not group:
            return error(code=404, message=_("群组不存在或已解散"))

        # 检查是否是群主
        if group["owner_id"] != current_user.id:
            return error(code=403, message=_("非群主不能添加用户到群聊"))

        # 检查用户是否在群组中
        if current_user.id not in group["member_ids"]:
            return error(code=403, message=_("您不在该群组中"))

        # 将用户列表添加到群组列表
        await db.groups.update_one(
            {"id": post_data.group_id},
            {"$push": {"member_ids": {"$each": post_data.user_ids}}},
        )

        return success(message=_("添加成功"))
    except Exception as e:
        logger.error(
            f"{current_user.id} 添加用户到群组{post_data}出错: {str(e)}", exc_info=True
        )
        return error(code=500, message=_("添加用户到群组出错"))


@router.post("/dele_user_from_group", description="将用户移出群聊")
async def del_member_to_group(
    post_data: GroupMemberManage,
    current_user: User = Depends(get_current_user),
    db=Depends(get_database),
    _ = Depends(get_translator),
):
    try:
        # 查找群组
        group = await db.groups.find_one(
            {"id": post_data.group_id, "is_dissolved": False}
        )

        if not group:
            return error(code=404, message=_("群组不存在或已解散"))

        # 检查是否是群主
        if group["owner_id"] != current_user.id:
            logger.warning(
                f"{current_user.id} 非群主尝试将用户移出群组{post_data.group_id}"
            )
            return error(code=403, message=_("非群主不能将用户移出群聊"))

        # 检查用户是否在群组中
        if current_user.id not in group["member_ids"]:
            logger.warning(
                f"{current_user.id} 非群主(成员)尝试将用户移出群组{post_data.group_id}"
            )
            return error(code=403, message=_("您不在该群组中"))

        if current_user.id in post_data.user_ids:
            return error(code=403, message=_("不能将群主移出群聊"))

        # 从群组中移除用户列表
        await db.groups.update_one(
            {"id": post_data.group_id},
            {"$pull": {"member_ids": {"$in": post_data.user_ids}}},
        )

        return success(message=_("移除成功"))
    except Exception as e:
        logger.error(
            f"{current_user.id} 将用户移除出群组{post_data}出错: {str(e)}",
            exc_info=True,
        )
        return error(code=500, message=_("将用户移除出群组出错"))


@router.post("/{group_id}/leave", description="用户主动退出群聊")
async def leave_group(
    group_id: str,
    current_user: User = Depends(get_current_user),
    db=Depends(get_database),
    _ = Depends(get_translator),
):
    try:
        logger.debug(f"用户({current_user.id})主动退出群聊({group_id})")
        # 查找群组
        group = await db.groups.find_one(
            {
                "id": group_id,
                "is_dissolved": False,
            }
        )

        if not group:
            return error(code=404, message=_("群组不存在或已解散"))

        # 检查是否是群主
        if group["owner_id"] == current_user.id:
            return error(code=403, message=_("群主不能退出群组"))

        # 检查用户是否在群组中
        if current_user.id not in group["member_ids"]:
            return error(code=403, message=_("您不在该群组中"))

        # 将用户从群组中移除
        await db.groups.update_one(
            {"id": group_id},
            {"$pull": {"member_ids": current_user.id}},
        )

        return success(message=_("退出群组成功"))
    except Exception as e:
        logger.error(
            f"{current_user.id} 退出群组{group_id}出错: {str(e)}", exc_info=True
        )
        return error(code=500, message=_("退出群组出错"))


@router.delete("/{group_uuid}", description="解散群聊")
async def dissolve_group(
    group_uuid: str,
    current_user: User = Depends(get_current_user),
    db=Depends(get_database),
    _ = Depends(get_translator),
):
    try:
        logger.debug(f"用户({current_user.id})解散群聊({group_uuid})")
        # 查找群组
        group = await db.groups.find_one(
            {
                "id": group_uuid,
                "is_dissolved": False,
            }
        )

        if not group:
            return error(code=404, message=_("群组不存在或已解散"))

        if group["name"] == settings.DEFAULT_GROUP_NAME:
            return error(code=403, message=_("不允许解散默认群聊"))

        # 检查是否是群主
        if group["owner_id"] != current_user.id:
            if group.get("type", "group") == "group":
                return error(code=403, message=_("你没有权限解散该群组"))

        # 解绑群绑定的知识库
        for database_id in group["database_ids"]:
            await db.databases.update_one(
                {"id": database_id},
                {"$pull": {"group_ids": group["id"]}},
            )

        # 标记群组为已解散
        await db.groups.update_one(
            {"id": group_uuid},
            {"$set": {"is_dissolved": True}},
        )
        if group.get("type", "group") == "private":
            return success(message=_("该聊天已删除"))
        else:
            return success(message=_("解散群组成功"))
    except Exception as e:
        logger.error(
            f"{current_user.id} 解散群组{group_uuid}出错: {str(e)}", exc_info=True
        )
        return error(code=500, message=_("解散群组出错"))


@router.post("/list", description="查询群列表")
async def get_groups(
    inquiry: GroupInquiry,
    current_user: User = Depends(get_current_user),
    manage_db=Depends(get_database),
    chat_db=Depends(get_chat_database),
    _ = Depends(get_translator),
):
    try:
        # 计算所有对话未读
        total_unread = 0
        query = {
                "member_ids": current_user.id,
                "is_dissolved": False,
            }
        all_groups = await manage_db.groups.find(query).to_list(None)
        for group in all_groups:
            chat_database = getattr(chat_db, group["id"])
            group_unread = await chat_database.count_documents(
                {
                    "group_id": group["id"],
                    "is_revoke": False,
                    "read_list": {"$ne": current_user.id},
                }
            )
            total_unread += group_unread

        if inquiry.name in ["", None]:
            # if pagination.page_size > 50:
            #     return error(code=200, message="每页数量不能超过50")
            page_size = (
                inquiry.page_size - 1 if inquiry.page == 1 else inquiry.page_size
            )
            skip = (inquiry.page - 1) * page_size

            query = {
                "member_ids": current_user.id,
                "is_dissolved": False,
            }
            if inquiry.list_share:
                query["type"] = {"$ne": "private"}
            if inquiry.is_owner:
                query["owner_id"] = current_user.id
            # 获取总数
            total = await manage_db.groups.count_documents(query)

            query["name"] = {"$ne": settings.DEFAULT_GROUP_NAME}
            # 获取分页数据，按照last_message字段(字典对象)中的created_at字段进行排序
            if page_size > 0:
                groups = (
                    await manage_db.groups.find(query)
                    .sort([("last_message.created_at", -1)])
                    .skip(skip)
                    .limit(page_size)
                    .to_list(None)
                )
            else:
                groups = []

            # 在查询第一页时默认将群名为“我与AI”的群聊置顶
            if inquiry.page == 1:
                ai_group = await manage_db.groups.find_one(
                    {"name": settings.DEFAULT_GROUP_NAME, "owner_id": current_user.id}
                )
                if ai_group:
                    ai_group["unread_count"] = await getattr(
                        chat_db, ai_group["id"]
                    ).count_documents(
                        {
                            "group_id": ai_group["id"],
                            "is_revoke": False,
                            "read_list": {"$ne": current_user.id},
                        }
                    )
                    ai_group['name'] = _(ai_group['name'])
                    groups.insert(0, ai_group)

        else:
            name_search_groups = await name_group_search(
                inquiry.name,
                current_user,
            )
            message_search_groups = await message_group_search(
                inquiry.name,
                current_user,
            )
            user_search_groups = await user_group_search(
                inquiry.name,
                current_user,
            )

            # 合并三个搜索结果并去重
            all_groups = name_search_groups + message_search_groups + user_search_groups
            unique_groups = []
            added_group_ids = set()
            for group in all_groups:
                group_id = group["id"]
                if group_id not in added_group_ids:
                    unique_groups.append(group)
                    added_group_ids.add(group_id)

            # 新增is_owner过滤逻辑
            if getattr(inquiry, "is_owner", False):
                unique_groups = [
                    g for g in unique_groups if g["owner_id"] == current_user.id
                ]

            # 按照search.page和search.page_size进行分页
            start_index = (inquiry.page - 1) * inquiry.page_size
            end_index = start_index + inquiry.page_size
            groups = unique_groups[start_index:end_index]
            total = len(unique_groups)

        # 统计当前用户群未读消息数
        for group in groups:
            chat_database = getattr(chat_db, group["id"])
            group_unread = await chat_database.count_documents(
                {
                    "group_id": group["id"],
                    "is_revoke": False,
                    "read_list": {"$ne": current_user.id},
                }
            )
            group["unread_count"] = group_unread
            group["last_message"] = (
                MessageData(**group["last_message"]) if group["last_message"] else None
            )
            # 私聊对话处理
            if "type" in group and group["type"] == "private":
                group["member_ids"].remove(current_user.id)
                # 用户已注销时也正常打开聊天框
                user = await manage_db.users.find_one({"id": group["member_ids"][0]})
                group["name"] = user["username"]
            # 为每个群加上被@标识
            unread_at_message = (
                await chat_database.find(
                    {
                        "at_list": current_user.id,
                        "is_revoke": False,
                        "read_list": {"$not": {"$elemMatch": {"$eq": current_user.id}}},
                    }
                )
                .sort("created_at", 1)
                .to_list(None)
            )
            if len(unread_at_message) > 0:
                group["was_at"] = True

        # 构建响应数据
        response_data = PaginationModel(
            total=total,
            page=inquiry.page,
            page_size=inquiry.page_size,
            items=[GroupResponse(**group).model_dump() for group in groups],
        ).dict()
        response_data["total_unread"] = total_unread
        return success(data=response_data)

    except Exception as e:
        logger.error(
            f"{current_user.id} 获取群组列表{inquiry}出错: {str(e)}", exc_info=True
        )
        return error(code=500, message=_("获取群组列表出错"))


# 记录已读消息成员
@router.get("/all_read/{group_id}", description="一键已读某群消息")
async def read_all_message(
    group_id: str,
    current_user: User = Depends(get_current_user),
    db=Depends(get_database),
    chat_db=Depends(get_chat_database),
    _ = Depends(get_translator),
):
    try:
        group = await db.groups.find_one(
            {
                "id": group_id,
                "is_dissolved": False,
            }
        )
        if not group:
            return error(code=404, message=_("群组不存在"))
        if current_user.id not in group["member_ids"]:
            logger.warning(f"{current_user.id} 偷窥{group_id}群信息")
            return error(code=403, message=_("您不在该群组中"))
        chat_collection = getattr(chat_db, group_id)

        # 更新消息已读成员
        result = await chat_collection.update_many(
            {}, {"$addToSet": {"read_list": current_user.id}}
        )
        await connection_dic.broadcast([current_user.id],json.dumps({"type":"refresh_group_list","group_id":group_id}))
        return success(message=_("记录成功"))
    except Exception as e:
        logger.error(
            f"{current_user.id} 一键已读群{group_id}消息出错: {str(e)}", exc_info=True
        )
        return error(code=500, message=_("一键已读出错"))


# 记录已读消息成员
@router.post("/read", description="记录已读消息成员")
async def read_message(
    update_data: GroupReadUpdate,
    current_user: User = Depends(get_current_user),
    db=Depends(get_database),
    chat_db=Depends(get_chat_database),
    _ = Depends(get_translator),
):
    try:
        group = await db.groups.find_one(
            {
                "id": update_data.group_id,
                "is_dissolved": False,
            }
        )
        if not group:
            return error(code=404, message=_("群组不存在"))
        if current_user.id not in group["member_ids"]:
            logger.warning(f"{current_user.id} 偷窥{update_data.group_id}群信息")
            return error(code=403, message=_("您不在该群组中"))
        chat_collection = getattr(chat_db, update_data.group_id)

        # 更新消息已读成员
        result = await chat_collection.update_many(
            {"id": {"$in": update_data.message_ids}},
            {"$addToSet": {"read_list": current_user.id}},
        )

        if result.modified_count == 0:
            logger.warning(f"未找到需要更新的消息: {update_data.message_ids}")
            return error(code=404, message=_("消息已标记为已读或不存在"))

        return success(message=_("记录成功"))
    except Exception as e:
        logger.error(
            f"{update_data.group_id} 记录已读消息成员{current_user.id}出错: {str(e)}",
            exc_info=True,
        )
        return error(code=500, message=_("记录已读消息成员出错"))


@router.get("/{group_uuid}", description="查询某个群的信息")
async def get_group_by_uuid(
    group_uuid: str,
    current_user: User = Depends(get_current_user),
    db=Depends(get_database),
    chat_db=Depends(get_chat_database),
    _ = Depends(get_translator),
):
    try:
        group = await db.groups.find_one(
            {
                "id": group_uuid,
            }
        )
        if not group:
            return error(code=404, message=_("群组不存在"))
        if group["is_dissolved"] is True:
            if group.get("type", "group") == "group":
                return error(code=404, message=_("群组已解散"))
            if group.get("type", "group") == "private":
                return error(code=404, message=_("对方已删除聊天"))
        if current_user.id not in group["member_ids"]:
            logger.warning(f"{current_user.id} 偷窥{group_uuid}群信息")
            return error(code=403, message=_("您不在该群组中"))
        # 查询未读消息数
        chat_database = getattr(chat_db, group_uuid)
        total_unread = await chat_database.count_documents(
            {
                "group_id": group_uuid,
                "is_revoke": False,
                "read_list": {"$ne": current_user.id},
            }
        )

        members = []
        for member_id in group["member_ids"]:
            if "type" in group and group["type"] == "private":
                if member_id == current_user.id:
                    continue
                user = await db.users.find_one({"id": member_id})
                if user:
                    members.append(
                        UserResponse(**user).model_dump(
                            exclude=["user_setting", "user_service"]
                        )
                    )
                    group["name"] = user["username"]
            else:
                user = await db.users.find_one({"id": member_id, "is_active": True})
                if not user:
                    # 临时用户
                    user = {"id":member_id,"username":member_id,"created_at":datetime.now()}
                if member_id in current_user.friends:
                    user["is_friend"] = True
                else:
                    user["is_friend"] = False
                if user:
                    members.append(
                        UserResponse(**user).model_dump(
                            exclude=["user_setting", "user_service"]
                        )
                    )
        databases = []
        for database_id in group["database_ids"]:
            database = await db.databases.find_one(
                {"id": database_id, "is_deleted": False}
            )
            if database:
                if database["owner_id"] != current_user.id:
                    for member_id in database["member_ids"]:
                        if (
                            member_id != current_user.id
                            and member_id not in current_user.friends
                        ):
                            database["member_ids"].remove(member_id)
                    for group_id in database["group_ids"]:
                        group_data = await db.groups.find_one(
                            {"id": group_id, "is_dissolved": False}
                        )
                        if group_data:
                            if current_user.id in group_data["member_ids"]:
                                continue
                        database["group_ids"].remove(group_id)
                databases.append(DatabaseResponse(**database))

        group["last_message"] = (
            MessageData(**group["last_message"]) if group["last_message"] else None
        )
        group["members"] = members
        group["databases"] = databases
        group["agent_setting"] = GroupAgentSetting(**group["agent_setting"])
        group["web_agent_setting"] = Webhook(**group["web_agent_setting"]) if "web_agent_setting" in group else Webhook()
        group_data = GroupDetailResponse(**group).model_dump()
        group_data["total_unread"] = total_unread
        group_data["agent_setting"]["model"] = _(group_data["agent_setting"]["model"])
        return success(data=group_data)
    except Exception as e:
        logger.error(
            f"{current_user.id} 通过uuid获取群组{group_uuid}出错: {str(e)}",
            exc_info=True,
        )
        return error(code=500, message=_("获取群组出错"))


# 修改群信息(更改群名，变更群主，更新绑定机器人)
@router.put("/{group_uuid}", description="修改群信息")
async def update_group(
    group_uuid: str,
    group_data: GroupCreate,
    current_user: User = Depends(get_current_user),
    db=Depends(get_database),
    _ = Depends(get_translator),
):
    try:
        logger.debug(f"用户({current_user.id}） 修改群({group_uuid})信息{group_data}")
        if group_data.name.strip() == "":
            return error(code=400, message=_("群组名称不能为空"))
        group = await db.groups.find_one(
            {
                "id": group_uuid,
                "is_dissolved": False,
            }
        )
        if not group:
            logger.warning(f"群{group_uuid}不存在或已解散")
            return error(code=404, message=_("群组不存在"))

        if group["owner_id"] != current_user.id:
            logger.warning(f"发现非群主{current_user.id}越权修改群{group_uuid}信息")
            return error(code=403, message=_("只有群主能修改群信息"))

        # 不允许修改默认群组名称
        if (
            group["name"] == settings.DEFAULT_GROUP_NAME
            and group_data.name != settings.DEFAULT_GROUP_NAME
        ):
            return error(code=403, message=_("不允许修改默认群聊名称"))

        # 不允许将其他群组名称改为默认群组名称
        if (
            group["name"] != settings.DEFAULT_GROUP_NAME
            and group_data.name == settings.DEFAULT_GROUP_NAME
        ):
            return error(code=403, message=_("不允许修改为默认群聊名称"))

        if (
            group["name"] == settings.DEFAULT_GROUP_NAME
            and len(group_data.member_ids) > 1
        ):
            return error(code=403, message=_("默认群聊不允许添加其他用户"))

        if (
            group["name"] == settings.DEFAULT_GROUP_NAME
            and group_data.owner_id != current_user.id
        ):
            return error(code=403, message=_("默认群聊不允许移交"))

        if len(group_data.member_ids) == 0:
            return error(code=403, message=_("群聊成员不能为空"))

        # 更改群名一并更改默认知识库名称
        key_name_dict = {
            "群收藏知识库": "col",
            "群对话记录库": "chat",
            "群文档知识库": "file",
        }
        if group_data.name != group["name"]:
            for key, prefix in key_name_dict.items():
                database_name = f"_{group_uuid.replace('-','')}_{prefix}"
                new_name = f"{group_data.name} " + _(key)
                await db.databases.update_one(
                    {"v_name": database_name},
                    {"$set": {"name": new_name, "description": new_name}},
                )
        # 比较更新前后知识库id变化进行绑定或解绑
        database_diff = list(set(group["database_ids"]) ^ set(group_data.database_ids))
        for database_id in database_diff:
            if database_id not in group["database_ids"]:
                # 绑定知识库
                await db.databases.update_one(
                    {"id": database_id},
                    {"$push": {"group_ids": group_uuid}},
                )
            else:
                # 解绑知识库
                await db.databases.update_one(
                    {"id": database_id},
                    {"$pull": {"group_ids": group_uuid}},
                )

        await db.groups.update_one(
            {"id": group_uuid}, {"$set": group_data.model_dump()}
        )

        return success(message=_("修改成功"))

    except Exception as e:
        logger.error(
            f"{current_user.id} 修改群{group_uuid}信息{group_data}出错: {str(e)}",
            exc_info=True,
        )
        return error(code=500, message=_("修改群信息出错"))


@router.post("/add_db", description="绑定知识库到群组")
async def add_db_to_group(
    add_data: GroupDatabaseManage,
    current_user: User = Depends(get_current_user),
    db=Depends(get_database),
    _ = Depends(get_translator),
):
    try:
        group = await db.groups.find_one(
            {
                "id": add_data.group_id,
                "is_dissolved": False,
            }
        )
        if not group:
            logger.warning(f"群{add_data.group_id}不存在或已解散")
            return error(code=404, message=_("群组不存在"))

        if group["owner_id"] != current_user.id:
            logger.warning(
                f"发现非群主{current_user.id}越权修改群{add_data.group_id}信息"
            )
            return error(code=403, message=_("只有群主能修改群信息"))

        await db.groups.update_one(
            {"id": add_data.group_id},
            {"$push": {"database_ids": {"$each": add_data.database_ids}}},
        )
        for database_id in add_data.database_ids:
            await db.databases.update_one(
                {"id": database_id}, {"$push": {"group_ids": add_data.group_id}}
            )

        return success(message=_("绑定成功"))

    except Exception as e:
        logger.error(
            f"{current_user.id} 绑定群{add_data.group_id}知识库{add_data.database_ids}出错: {str(e)}",
            exc_info=True,
        )
        return error(code=500, message=_("绑定群组知识库出错"))


@router.post("/delete_db", description="解绑群组知识库")
async def delete_db_from_group(
    add_data: GroupDatabaseManage,
    current_user: User = Depends(get_current_user),
    db=Depends(get_database),
    _ = Depends(get_translator),
):
    try:
        group = await db.groups.find_one(
            {
                "id": add_data.group_id,
                "is_dissolved": False,
            }
        )
        if not group:
            logger.warning(f"群{add_data.group_id}不存在或已解散")
            return error(code=404, message=_("群组不存在"))

        if group["owner_id"] != current_user.id:
            logger.warning(
                f"发现非群主{current_user.id}越权修改群{add_data.group_id}信息"
            )
            return error(code=403, message=_("只有群主能修改群信息"))

        await db.groups.update_one(
            {"id": add_data.group_id},
            {"$pull": {"database_ids": {"$in": add_data.database_ids}}},
        )
        for database_id in add_data.database_ids:
            await db.databases.update_one(
                {"id": database_id}, {"$pull": {"group_ids": add_data.group_id}}
            )

        return success(message=_("解绑成功"))

    except Exception as e:
        logger.error(
            f"{current_user.id} 解绑群{add_data.group_id}知识库{add_data.database_ids}出错: {str(e)}",
            exc_info=True,
        )
        return error(code=500, message=_("解绑群知识库出错"))


@router.post("/messages", description="查询某个群的对话记录")
async def get_group_messages(
    search_data: GroupMessageSearch,
    current_user: User = Depends(get_current_user),
    manage_db=Depends(get_database),
    chat_db=Depends(get_chat_database),
    _ = Depends(get_translator),
):
    try:
        group = await manage_db.groups.find_one(
            {"id": search_data.id, "is_dissolved": False}
        )
        if not group:
            return error(code=404, message=_("群组不存在"))
        if group["is_dissolved"] is True:
            if group.get("type", "group") == "group":
                return error(code=404, message=_("群组已解散"))
            if group.get("type", "group") == "private":
                return error(code=404, message=_("对方已删除聊天"))
        if current_user.id not in group['member_ids']:
            logger.warning(f"{current_user.id}非法查询群{group['id']}聊天记录")
            return error(code=403, message=_("您不在该群组中"))
        for member_id in group["member_ids"]:
            if "type" in group and group["type"] == "private":
                if member_id == current_user.id:
                    continue
                user = await manage_db.users.find_one({"id": member_id})
                if user:
                    group_name = user["username"]
            else:
                group_name = group.get("name")

        if search_data.page_size > 50:
            return error(code=422, message=_("每页数量不能超过50"))
        chat_collection = getattr(chat_db, search_data.id)
        query = {"group_id": search_data.id, "is_revoke": False}
        if search_data.start_index:
            query["created_at"] = {"$lte": search_data.start_index}
        # 获取总数
        total = await chat_collection.count_documents(query)

        # 修改skip的计算方式，从后往前计算
        skip = max(0, total - (search_data.page * search_data.page_size))
        # 实际获取的条数可能小于page_size（当是第一页且总条数不足时）
        actual_limit = min(
            search_data.page_size,
            total - (search_data.page - 1) * search_data.page_size,
        )

        # 修改判断逻辑：只有当页码超出实际页数时才返回空列表
        if (search_data.page - 1) * search_data.page_size >= total:
            messages = []
        else:
            # 查找群组对话记录，按时间正序排序，从后往前跳过
            messages = (
                await chat_collection.find(query)
                .sort("created_at", 1)
                .skip(skip)
                .limit(actual_limit)
                .to_list(None)
            )

        # 按时间倒序排序
        messages.reverse()
        start_index = (
            search_data.start_index
            if search_data.start_index
            else messages[0]["created_at"] if len(messages) > 0 else None
        )

        # 标记已读/未读状态
        for message in messages:
            if "read_list" in message and current_user.id in message["read_list"]:
                message["is_read"] = True
            else:
                message["is_read"] = False

        data = PaginationModel(
            start_index=start_index,
            total=total,
            page=search_data.page,
            page_size=search_data.page_size,
            items=[MessageResponse(**message).model_dump() for message in messages],
        ).dict()

        data["group_name"] = group_name

        return success(data=data)
    except Exception as e:
        logger.error(
            f"{current_user.id} 通过uuid获取对话记录{search_data}出错: {str(e)}",
            exc_info=True,
        )
        return error(code=500, message=_("获取对话记录失败"))


@router.post("/messages/forward", description="转存对话记录")
async def forward_messages(
    message_data: GroupForwardMessage,
    current_user: User = Depends(get_current_user),
    db=Depends(get_database),
    _ = Depends(get_translator),
):
    try:
        logger.debug(
            f"用户({current_user.id})分享{len(message_data.message_ids)}条群({message_data.group_id})聊天记录"
        )
        if not message_data.message_ids:
            return error(code=400, message=_("请选择要转存的对话记录"))
        group_forward = GroupForward(
            group_id=message_data.group_id,
            message_ids=message_data.message_ids,
            forward_user_id=current_user.id,
        )
        await db.group_forwards.insert_one(group_forward.model_dump())
        return success(
            data={"forward_url": f"/group/messages/get_forward/page/{group_forward.id}"}
        )
    except Exception as e:
        logger.error(
            f"{current_user.id}转存对话记录{message_data}出错: {str(e)}", exc_info=True
        )
        return error(code=500, message=_("转存对话记录出错"))


@router.get("/messages/get_forward/{forward_id}", description="获取转存的对话记录")
async def get_forwarded_message(
    forward_id: str,
    manage_db=Depends(get_database),
    chat_db=Depends(get_chat_database),
    _ = Depends(get_translator),
):
    try:
        # 从 MongoDB 中获取转存的记录
        document = await manage_db.group_forwards.find_one({"id": forward_id})
        if not document:
            return error(code=404, message=_("分享记录不存在"))
        # 查询转存的对话记录详情
        chat_collection = getattr(chat_db, document["group_id"])
        messages = await chat_collection.find(
            {"id": {"$in": document["message_ids"]}}
        ).to_list(None)
        forward = ForwardMessage(
            id=document["id"],
            group_id=document["group_id"],
            messages=[MessageData(**message) for message in messages],
            forward_user_id=document["forward_user_id"],
            created_at=document["created_at"],
        )
        return success(data=forward.model_dump())
    except Exception as e:
        logger.error(f"获取转存对话记录{forward_id}出错: {str(e)}", exc_info=True)
        return error(code=500, message=_("获取转存对话记录出错"))


@router.get(
    "/messages/get_forward/page/{forward_id}",
    response_class=HTMLResponse,
    description="渲染分享的对话记录页面",
)
async def get_forwarded_message_page(
    forward_id: str,
    request: Request,
    manage_db=Depends(get_database),
    chat_db=Depends(get_chat_database),
    _ = Depends(get_translator),
):
    try:
        # 从 MongoDB 中获取转存的记录
        document = await manage_db.group_forwards.find_one({"id": forward_id})
        if not document:
            return HTMLResponse(content=_("分享记录不存在"), status_code=404)

        # 查询转存的对话记录详情
        chat_collection = getattr(chat_db, document["group_id"])
        messages = await chat_collection.find(
            {"id": {"$in": document["message_ids"]}, "is_revoke": False}
        ).to_list(None)

        # 获取转发用户信息
        forward_user = await manage_db.users.find_one(
            {"id": document["forward_user_id"], "is_active": True}
        )

        for message in messages:
            try:
                message["content"] = json.loads(message["content"])
                message["cite"]["content"] = json.dumps(message["cite"]["content"])
            except Exception as e:
                pass

        # 渲染模板
        return templates.TemplateResponse(
            "forward_messages.html",
            {
                "request": request,
                "messages": messages,
                "forward_user_id": forward_user["id"],
                "forward_user": forward_user["username"],
                "forward_user_avatar": forward_user["avatar"],
                "forward_time": document["created_at"].strftime("%Y-%m-%d %H:%M:%S"),
            },
        )
    except Exception as e:
        logger.error(f"渲染转存对话记录{forward_id}出错: {str(e)}", exc_info=True)
        return HTMLResponse(content=_("获取对话记录失败"), status_code=500)


# @router.get("/name/{group_name}")
# async def get_group_by_name(
#     group_name: str,
#     current_user: User = Depends(get_current_user),
#     db = Depends(get_database)
# ):
#     try:
#         group = await db.groups.find_one({
#             "name": group_name,
#             "$or": [
#                 {"owner_id": current_user.id},
#                 {"member_ids": current_user.id}
#             ]
#         })

#         if not group:
#             return error(code=404, message="群组不存在")

#         return success(data=Group(**group))
#     except Exception as e:
#         logger.error(f"{current_user.id} 通过群名获取群组出错: {str(e)}",exc_info=True)
#         return error(code=500, message="获取群组出错")


# @router.post("/search", description="根据名称模糊搜索群组")
# async def search_groups(
#     search: GroupInquiry,
#     current_user: User = Depends(get_current_user),
#     manage_db=Depends(get_database),
#     chat=Depends(get_chat_database),
# ):
#     try:
#         # 构建查询条件
#         escaped_name = re.escape(search.name)
#         regex_pattern = f".*{escaped_name}.*"
#         query = {
#             "member_ids": current_user.id,
#             "name": {"$regex": regex_pattern, "$options": "i"},
#             "is_dissolved": False,
#         }

#         # 获取总数
#         total = await manage_db.groups.count_documents(query)

#         # 当前页数
#         skip = (search.page - 1) * search.page_size

#         # 查找群组
#         groups = (
#             await manage_db.groups.find(query)
#             .skip(skip)
#             .limit(search.page_size)
#             .to_list(None)
#         )

#         for group in groups:
#             # TODO：查询该成员的未读消息数
#             chat_database = getattr(chat, group["id"])
#             group["unread_count"] = await chat_database.count_documents(
#                 {
#                     "group_id": group["id"],
#                     "is_revoke": False,
#                     "read_list": {"$ne": current_user.id},
#                 }
#             )
#             group["last_message"] = (
#                 MessageData(**group["last_message"]) if group["last_message"] else None
#             )

#         # 构建响应数据
#         response_data = PaginationModel(
#             total=total,
#             page=search.page,
#             page_size=search.page_size,
#             items=[GroupResponse(**group).model_dump() for group in groups],
#         )

#         return success(data=response_data)
#     except Exception as e:
#         logger.error(f"{current_user.id} 搜索群组出错: {str(e)}", exc_info=True)
#         return error(code=500, message="搜索群组失败")


@router.post("/evaluate", description="用户对 AI 回答进行评估")
async def evaluate_ai_response(
    evaluate_data: ResponseEvaluate,
    current_user: User = Depends(get_current_user),
    chat_db=Depends(get_chat_database),
    _ = Depends(get_translator),
):
    try:
        logger.info(f"{current_user.id} 对 AI 回答进行评估: {evaluate_data}")
        # 根据消息 ID 找到消息
        chat_collection = getattr(chat_db, evaluate_data.group_id)
        message = await chat_collection.find_one(
            {
                "id": evaluate_data.message_id,
            }
        )

        if not message:
            return error(code=404, message=_("消息不存在"))

        # 在消息的 evaluate 字段写入 evaluate
        await chat_collection.update_one(
            {"id": evaluate_data.message_id},
            {"$set": {"evaluate": evaluate_data.evaluate}},
        )

        return success(message=_("评估记录成功"))
    except Exception as e:
        logger.error(
            f"{current_user.id} 对消息 {evaluate_data.message_id} 进行评估出错: {str(e)}",
            exc_info=True,
        )
        return error(code=500, message=_("评估记录出错"))


@router.post("/search", description="根据群名称、消息、用户模糊搜索群组")
async def search_groups(
    search: GroupInquiry,
    current_user: User = Depends(get_current_user),
    chat_db=Depends(get_chat_database),
    _ = Depends(get_translator),
):
    try:
        logger.debug(f"用户{current_user.id}搜索群组{search}")
        if search.name is None:
            return
        if search.page_size > 50:
            return error(code=422, message=_("每页数量不能超过50"))

        # 调用不同的搜索函数查询群组列表
        name_search_groups = await name_group_search(search.name, current_user)
        message_search_groups = await message_group_search(search.name, current_user)
        user_search_groups = await user_group_search(search.name, current_user)

        # 合并搜索结果并去除三个搜索结果中重复的项
        all_groups = name_search_groups + message_search_groups + user_search_groups
        unique_groups = []
        added_group_ids = set()
        for group in all_groups:
            group_id = group["id"]
            if group_id not in added_group_ids:
                unique_groups.append(group)
                added_group_ids.add(group_id)

        # 新增is_owner过滤逻辑
        if getattr(search, "is_owner", False):
            unique_groups = [
                g for g in unique_groups if g["owner_id"] == current_user.id
            ]

        # 按照search.page和search.page_size进行分页
        start_index = (search.page - 1) * search.page_size
        end_index = start_index + search.page_size
        groups = unique_groups[start_index:end_index]

        # 查询该成员的未读消息数
        for group in groups:
            chat_database = getattr(chat_db, group["id"])
            group["unread_count"] = await chat_database.count_documents(
                {
                    "group_id": group["id"],
                    "is_revoke": False,
                    "read_list": {"$ne": current_user.id},
                }
            )
            group["last_message"] = (
                MessageData(**group["last_message"]) if group["last_message"] else None
            )

        # 构建响应数据
        response_data = PaginationModel(
            total=len(unique_groups),
            page=search.page,
            page_size=search.page_size,
            items=[GroupResponse(**group).model_dump() for group in groups],
        )

        return success(data=response_data)

    except Exception as e:
        logger.error(f"{current_user.id} 搜索群组出错: {str(e)}", exc_info=True)
        return error(code=500, message=_("搜索群组失败"))


@router.post("/message/search", description="在群组内搜索对话记录")
async def search_group_messages(
    search: MessageSearch,
    current_user: User = Depends(get_current_user),
    _ = Depends(get_translator),
):
    try:
        logger.debug(f"用户{current_user.id}在群组内搜索对话记录{search}")
        if not search.message_content:
            return
        if search.page_size > 50:
            return error(code=422, message=_("每页数量不能超过50"))

        # 调用不同的搜索函数查询群组列表
        all_messages = await group_message_search(
            search.message_content,
            search.group_id,
        )

        start_index = (search.page - 1) * search.page_size
        end_index = start_index + search.page_size
        messages = all_messages[start_index:end_index]

        total = len(all_messages)

        response_data = PaginationModel(
            start_index=start_index,
            total=total,
            page=search.page,
            page_size=search.page_size,
            items=[MessageResponse(**message).model_dump() for message in messages],
        ).dict()

        return success(data=response_data)

    except Exception as e:
        logger.error(f"{current_user.id} 搜索群组出错: {str(e)}", exc_info=True)
        return error(code=500, message=_("搜索群组失败"))


@router.get(
    "/user_unread_at_messages/{group_id}", description="获取当前用户在群中未读的@消息"
)
async def get_user_unread_at_messages(
    group_id: str,
    current_user: User = Depends(get_current_user),
    chat_db=Depends(get_chat_database),
    _ = Depends(get_translator),
):
    try:
        chat_data = (
            await getattr(chat_db, group_id)
            .find(
                {
                    "at_list": current_user.id,
                    "is_revoke": False,
                    "read_list": {"$not": {"$elemMatch": {"$eq": current_user.id}}},
                }
            )
            .sort("created_at", 1)
            .to_list(None)
        )

        return success(data=[MessageResponse(**message) for message in chat_data])

    except Exception as e:
        logger.error(f"{current_user.id} 搜索群组出错: {str(e)}", exc_info=True)
        return error(code=500, message=_("搜索群组失败"))

@router.get(
    "/faq/{group_id}", description="获取当前群组的FAQ"
)
async def get_group_faq(
    group_id: str,
    current_user: User = Depends(get_current_user),
    manage_db=Depends(get_database),
    _ = Depends(get_translator),
):
    try:
        # 查询群组绑定的知识库
        group = await manage_db.groups.find_one(
            {
                "id": group_id,
                "is_dissolved": False,
            }
        )
        if not group:
            return error(code=404, message=_("群组不存在"))
        # 查询各个知识库中所有文档的FAQ数据合并
        result = []
        for database_id in group["database_ids"]:
            database_files = (
                await manage_db.files.find(
                    {
                        "vectordb_id": database_id,
                        "is_deleted": False,
                        "vector_status": "success"
                    }
                ).to_list(None)
            )
            if database_files:
                for file in database_files:
                    faq_data = {"value":file['file_name'],"label":file['file_name'],"level":0,"children":[]}
                    faq_set = file.get("faq_set", [])
                    for faq in faq_set:
                        faq_data["children"].append({"value":faq['first_class_title'],"label":faq['first_class_title'],"level":1,
                                                     "children":[{"value":qa["question"],"label":qa["question"],"level":2,} for qa in faq.get('qa_set',[])]})
                    result.append(faq_data)

        return success(data=result)

    except Exception as e:
        logger.error(f"{current_user.id} 搜索群组出错: {str(e)}", exc_info=True)
        return error(code=500, message=_("搜索群组失败"))

# @router.post(
#     "/faq", description="搜索当前群组的FAQ"
# )
# async def search_group_faq(
#     search_data: FAQSearch,
#     current_user: User = Depends(get_current_user),
#     manage_db=Depends(get_database),
# ):
#     try:
#         # 查询群组绑定的知识库
#         group = await manage_db.groups.find_one(
#             {
#                 "id": search_data.group_id,
#                 "is_dissolved": False,
#             }
#         )
#         if not group:
#             return error(code=404, message="群组不存在")
#         # 查询各个知识库中所有文档的FAQ数据合并
#         faq_data = []
#         for database_id in group["database_ids"]:
#             database_files = (
#                 await manage_db.files.find(
#                     {
#                         "vectordb_id": database_id,
#                         "is_deleted": False,
#                     }
#                 ).to_list(None)
#             )
#             if database_files:
#                 for file in database_files:
#                     faq_set = file.get("faq_set", [])
#                     for faq in faq_set:
#                         faq["qa_set"] = [qa["question"] for qa in faq["qa_set"]]
#                         faq_data.append(faq)

#         return success(data=faq_data)

#     except Exception as e:
#         logger.error(f"{current_user.id} 搜索群组出错: {str(e)}", exc_info=True)
#         return error(code=500, message="搜索群组失败")
