import uuid
from datetime import datetime
from typing import List

from pydantic import BaseModel, Field


class Organization(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str
    code: str
    created_at: datetime = Field(default_factory=lambda: datetime.now())
    department_ids: List[str] = []  # 存储部门ID列表
    group_ids: List[str] = []  # 存储群组ID列表

    class Config:
        populate_by_name = True
        json_schema_extra = {
            "example": {
                "name": "示例公司",
                "code": "DEMO",
                "department_ids": ["dept-123", "dept-456"],
                "group_ids": ["group-789"],
            }
        }

    def __getattribute__(self, name: str):
        value = super().__getattribute__(name)
        if isinstance(value, datetime):
            return datetime.strftime(value, "%Y-%m-%d %H:%M:%S")
        return value
