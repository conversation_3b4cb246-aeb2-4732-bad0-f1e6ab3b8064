import uuid
from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel, Field


class Robot(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str
    description: Optional[str] = None
    owner_id: str
    database_id: str
    is_active: bool = True
    created_at: datetime = Field(default_factory=lambda: datetime.now())
    group_ids: List[str] = []  # 存储群组ID列表，记录与机器人绑定的群组

    class Config:
        populate_by_name = True
        json_schema_extra = {
            "example": {
                "name": "助手机器人",
                "description": "智能问答助手",
                "owner_id": "user-123",
                "database_id": "db-456",
                "group_ids": ["group-789"],
            }
        }
