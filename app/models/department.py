import uuid
from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel, Field


class Department(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str
    organization_id: str
    parent_id: Optional[str] = None
    created_at: datetime = Field(default_factory=lambda: datetime.now())
    child_ids: List[str] = []  # 存储子部门ID列表
    user_ids: List[str] = []  # 存储部门用户ID列表

    class Config:
        populate_by_name = True
        json_schema_extra = {
            "example": {
                "name": "技术部",
                "organization_id": "org-123",
                "parent_id": "dept-456",
                "child_ids": ["dept-789", "dept-012"],
                "user_ids": ["user-123", "user-456"],
            }
        }

    def __getattribute__(self, name: str):
        value = super().__getattribute__(name)
        if isinstance(value, datetime):
            return datetime.strftime(value, "%Y-%m-%d %H:%M:%S")
        return value
