import uuid
from datetime import datetime
from typing import Any, List, Optional

from pydantic import BaseModel, Field, model_serializer


class Database(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str  # 用户命名
    v_name: str  # 向量数据库命名
    owner_id: str
    owner_name: str = None
    default_group_id: Optional[str] = None
    type: str = "file"  # file文档知识库 chat对话记录知识库 col 收藏内容知识库
    is_deleted: bool = False
    created_at: datetime = Field(default_factory=lambda: datetime.now())
    description: Optional[str] = None
    member_ids: List[str] = []  # 存储成员ID列表
    group_ids: List[str] = []  # 存储群组ID列表
    show_details: bool = False

    class Config:
        populate_by_name = True
        json_schema_extra = {
            "example": {
                "name": "test_database",
                "owner_id": "user-123",
                "description": "测试数据库",
                "member_ids": ["user-123", "user-456"],
            }
        }

    @model_serializer
    def serialize_model(self) -> dict:
        return {k: getattr(self, k) for k in self.__dict__}

    def __getattribute__(self, name: str) -> Any:
        value = super().__getattribute__(name)
        if isinstance(value, datetime):
            return datetime.strftime(value, "%Y-%m-%d %H:%M:%S")
        return value
