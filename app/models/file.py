import uuid
from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field, field_serializer


class File(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    file_name: str
    file_size: int
    file_type: str
    cloud_file_path: str
    local_file_path: str
    created_at: datetime = Field(default_factory=lambda: datetime.now())
    uploader_id: str
    vectordb_id: Optional[str] = None
    group_id: Optional[str] = None
    is_deleted: bool = False
    vector_status: str = (
        "pending"  # pending 等待中 uploading 上传中 parsing 解析中 writing 写入中 success 成功 failed 失败
    )

    class Config:
        populate_by_name = True
        json_schema_extra = {
            "example": {
                "file_name": "test.pdf",
                "file_size": 1024,
                "file_type": "application/pdf",
                "uploader_id": "user-123",
                "vectordb_id": "db-456",
                "group_id": "group-789",
            }
        }

    @field_serializer("created_at")
    def serialize_dt(self, dt: datetime, _info):
        return datetime.strftime(dt, "%Y-%m-%d %H:%M:%S")
