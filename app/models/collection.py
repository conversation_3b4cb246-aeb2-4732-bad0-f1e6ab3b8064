import uuid
from datetime import datetime

from pydantic import BaseModel, Field


class Collection(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    content: list[str]
    database_id: str
    group_id: str
    is_deleted: bool = False
    user_id: str
    abstract: str = ""
    created_at: datetime = Field(default_factory=lambda: datetime.now())

    class Config:
        populate_by_name = True
        json_schema_extra = {
            "example": {
                "name": "test_database",
                "owner_id": "user-123",
                "description": "测试数据库",
                "member_ids": ["user-123", "user-456"],
            }
        }
