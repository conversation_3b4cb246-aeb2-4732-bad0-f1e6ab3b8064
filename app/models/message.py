import json
import uuid
from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field


class Message(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    type: str
    content: str
    transfer: Optional[str] = None
    duration: Optional[float] = None
    sound_file_id: Optional[str] = None
    cite: Optional[dict] = None
    sender_id: str
    sender_avatar: Optional[str]
    sender_username: str
    group_id: str
    receiver: list
    vector_status: str = "pending"
    is_revoke: bool = False
    created_at: datetime = Field(default_factory=lambda: datetime.now())
    at_list: list = []
    read_list: list = []
    evaluate: str = ""
    transfer_status: Optional[str] = None
    transfer_doc_id: Optional[str] = None
    is_AI: bool = False

    def json(self, **kwargs):
        # 自定义 JSON 序列化方法
        data = super().json(**kwargs)
        data_dict = json.loads(data)
        data_dict["created_at"] = self.created_at.strftime("%Y%m%d%H%M%S")
        return json.dumps(data_dict)

    model_config = {
        "from_attributes": True,
        "json_schema_extra": {
            "example": {
                "content": "消息内容",
                "sender_id": "user-123",
                "group_id": "group-456",
                "vector_status": "pending",
            }
        },
    }
