import uuid
from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel, Field, EmailStr, field_serializer

class CustomModel(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str
    provider: str
    url: str
    key: str
    model_name: str
    max_token: int
    is_deleted: bool = False
    is_shared : bool = False
    created_at: datetime = Field(default_factory=lambda: datetime.now())
    updated_at: datetime = Field(default_factory=lambda: datetime.now())


class UserSetting(BaseModel):
    awake_keys: List[dict] = []
    # 添加自定义LLM配置
    llm_models: List[CustomModel] = []


class UserService(BaseModel):
    token_usage: int = 0
    filestorage_usage: float = 0


class User(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    username: str
    email: Optional[EmailStr] = None
    phone: Optional[str] = None
    hashed_password: str
    is_active: bool = False
    avatar: Optional[str]
    verification_code: Optional[str] = None
    created_at: datetime = Field(default_factory=lambda: datetime.now())
    department_id: Optional[str] = None
    friends: List[str] = []  # 存储好友的用户ID列表
    friend_requests: List[dict] = []  # 存储待处理的好友请求
    user_setting: UserSetting = Field(default_factory=UserSetting)
    user_service: UserService = {"token_usage": 0, "filestorage_usage": 0}

    class Config:
        populate_by_name = True
        json_schema_extra = {
            "example": {
                "username": "johndoe",
                "email": "<EMAIL>",
                "is_active": True,
                "department_id": "dept-123",
            }
        }

    def __getattribute__(self, name: str):
        value = super().__getattribute__(name)
        if isinstance(value, datetime):
            return datetime.strftime(value, "%Y-%m-%d %H:%M:%S")
        return value
