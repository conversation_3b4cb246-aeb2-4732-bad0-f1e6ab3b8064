import uuid
from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel, Field


class Abbr(BaseModel):
    origin: str
    abbr: str


class GroupAgentSetting(BaseModel):
    abbr: list[Optional[Abbr]]
    model: Optional[str]
    quality_check: bool
    role: Optional[str]
    integrate: bool

class Webhook(BaseModel):
    domain: Optional[str] = None
    description: Optional[str] = None
    welcome_message: Optional[str] = None

class Group(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str
    owner_id: str
    last_message: dict = {}
    is_dissolved: bool = False
    created_at: datetime = Field(default_factory=lambda: datetime.now())
    organization_id: Optional[str] = None
    member_ids: List[str] = []  # 存储成员ID列表
    database_ids: List[str] = []  # 存储知识库ID列表
    agent_setting: GroupAgentSetting
    web_agent_setting: Optional[Webhook] = Webhook()
    type: str = "group"
    recent_files: List[dict] = []  # 存储最近的文件信息

    class Config:
        populate_by_name = True
        json_schema_extra = {
            "example": {
                "name": "技术讨论组",
                "owner_id": "user-123",
                "organization_id": "org-456",
                "member_ids": ["user-123", "user-456"],
                "database_ids": ["database-789"],
            }
        }


class GroupForward(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    group_id: str
    message_ids: List[str]
    forward_user_id: str
    created_at: datetime = Field(default_factory=lambda: datetime.now())

    class Config:
        populate_by_name = True
        json_schema_extra = {
            "example": {
                "forward_id": "543wggw4y",
                "group_id": "34wtg43q",
                "message_ids": ["q3gat4tg", "3qatg", "3qg", "34", "q3a"],
                "forward_user_id": "sfd234ertf23",
                "forward_created_at": "2025-01-02 12:00:00+08:00",
            }
        }
