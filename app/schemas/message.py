from datetime import datetime
from typing import Optional

from pydantic import BaseModel, field_serializer


class MessageBase(BaseModel):
    created_at: datetime

    class Config:
        from_attributes = True

    @field_serializer("created_at")
    def serialize_dt(self, dt: datetime, _info):
        return datetime.strftime(dt, "%Y-%m-%d %H:%M:%S")


class MessageData(MessageBase):
    id: str
    type: str
    content: str
    transfer: Optional[str]
    duration: Optional[float]
    cite: Optional[dict]
    sender_id: str
    sender_username: str
    sender_avatar: Optional[str]
    group_id: str
    created_at: datetime
    at_list: list = []
    is_streaming: bool = False
    read_list: list = []
    evaluate: str = ""
    is_AI: bool = False
    sound_file_id: Optional[str] = None

    class Config:
        from_attributes = True


class MessageResponse(MessageData):
    is_read: bool = True

class StopMessage(BaseModel):
    message_id: str
    group_id: str