from datetime import datetime
from typing import Optional

from pydantic import BaseModel, EmailStr, field_serializer

from ..models.user import UserSetting
from .common import PaginationRequest


class UserEmailLogin(BaseModel):
    email: EmailStr
    password: str


class UserCreate(UserEmailLogin):
    username: str
    phone: Optional[str] = None
    verification_code: str
    group_id: Optional[str] = None


class UserPhoneLogin(BaseModel):
    id: str
    phone: str
    verification_code: str
    group_id: Optional[str] = None


class UserBase(BaseModel):
    created_at: datetime

    @field_serializer("created_at")
    def serialize_dt(self, dt: datetime, _info):
        return datetime.strftime(dt, "%Y-%m-%d %H:%M:%S")


class UserSearch(BaseModel):
    id: Optional[str] = None
    username: Optional[str] = None
    phone: Optional[str] = None
    email: Optional[EmailStr] = None


class UserQuery(BaseModel):
    query_str: str


class UserUpdate(BaseModel):
    # 暂支持修改用户名即手机号(在手机号不用验证的情况下)
    username: str
    phone: Optional[str] = None
    user_setting: UserSetting


class ResetPasswordSendCode(BaseModel):
    email: EmailStr


class ResetPasswordForm(ResetPasswordSendCode):
    new_password: str
    verification_code: str


class ModifyPasswordForm(ResetPasswordSendCode):
    # TODO: 修改密码需要旧密码验证
    verification_code: str
    old_password: str
    new_password: str


class UserSendCodeResponse(UserBase):
    id: str
    phone: str
    email: Optional[EmailStr] = None


class UserResponse(UserBase):
    id: str
    username: str
    phone: Optional[str] = None
    email: Optional[EmailStr] = None
    avatar: Optional[str] = None
    user_setting: dict = {"awake_keys": []}
    user_service: dict = {"token_usage": 0, "filestorage_usage": 0}
    is_friend: Optional[bool] = None

    class Config:
        from_attributes = True


class UserLoginResponse(UserResponse):
    access_token: str
    token_type: str

    class Config:
        from_attributes = True


class UserFriendHandle(BaseModel):
    friend_id: str
    action: str  # accept 或 reject


class UserFriendRequest(BaseModel):
    user_id: str
    username: str
    avatar: Optional[str] = None
    request_type: str
    status: str
    created_at: datetime
    handled_at: Optional[datetime] = None


class UserFriendRequestResponse(UserBase):
    user_id: str
    username: str
    avatar: Optional[str] = None
    request_type: str
    status: str
    created_at: datetime
    handled_at: Optional[datetime] = None


class SearchFriend(PaginationRequest):
    search: Optional[str] = None


class CustomLLMCreate(BaseModel):
    name: str
    provider: str
    url: str
    key: str
    model_name: str
    max_token: int
    is_shared: bool = False

class CustomLLMUpate(CustomLLMCreate):
    id: str

class CustomLLMResponse(CustomLLMCreate):
    id: str
    created_at: datetime
    updated_at: datetime

    @field_serializer("created_at")
    def serialize_create_time(self, dt: datetime, _info):
        return datetime.strftime(dt, "%Y-%m-%d %H:%M:%S")
    
    @field_serializer("updated_at")
    def serialize_update_time(self, dt: datetime, _info):
        return datetime.strftime(dt, "%Y-%m-%d %H:%M:%S")