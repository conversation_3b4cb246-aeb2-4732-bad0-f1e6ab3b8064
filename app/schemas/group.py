from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel, field_serializer, field_validator

from ..models.group import GroupAgentSetting,Webhook
from .common import PaginationRequest
from .database import DatabaseResponse
from .message import MessageData
from .user import UserResponse


class GroupBase(BaseModel):
    created_at: datetime

    class Config:
        from_attributes = True

    @field_serializer("created_at")
    def serialize_dt(self, dt: datetime, _info):
        return datetime.strftime(dt, "%Y-%m-%d %H:%M:%S")


class GroupMessageSearch(PaginationRequest):
    id: str
    start_index: Optional[datetime]

    @field_validator("start_index")
    def validate_start_index(cls, v, values):
        if values.data["page"] > 1 and v is None:
            raise ValueError("start_index 不能为空当 id 大于 1")
        return v


class GroupCreate(BaseModel):
    name: str
    owner_id: str
    database_ids: List[str]
    member_ids: List[str]
    agent_setting: GroupAgentSetting
    web_agent_setting: Optional[Webhook] = Webhook()
    type: str = "group"


class GroupUpdate(BaseModel):
    name: str
    owner_id: str
    database_ids: List[str]


class GroupCreateResponse(GroupBase):
    id: str
    name: str
    owner_id: str
    member_ids: List[str]
    database_ids: List[str]
    type: str = "group"
    last_message: Optional[dict]

    class Config:
        from_attributes = True


class GroupData(GroupBase):
    id: str
    name: str
    owner_id: str
    last_message: MessageData
    is_dissolved: bool = False
    created_at: datetime
    organization_id: str
    member_ids: List[str]  # 存储成员ID列表
    database_ids: List[str]  # 存储机器人ID列表


class GroupMemberManage(BaseModel):
    user_ids: list
    group_id: str


class GroupDatabaseManage(BaseModel):
    database_ids: list
    group_id: str


class GroupReadUpdate(BaseModel):
    message_ids: list
    group_id: str


class GroupResponse(GroupBase):
    id: str
    name: str
    owner_id: str
    last_message: Optional[MessageData]
    member_ids: List[str]
    database_ids: List[str]
    unread_count: int
    type: str = "group"
    was_at: bool = False

    class Config:
        from_attributes = True


class GroupDetailResponse(BaseModel):
    id: str
    name: str
    owner_id: str
    last_message: Optional[MessageData]
    members: List[UserResponse]
    databases: List[DatabaseResponse]
    agent_setting: GroupAgentSetting
    web_agent_setting: Webhook
    type: str = "group"

    class Config:
        from_attributes = True


class GroupForwardMessage(BaseModel):
    group_id: str
    message_ids: List[str]


class ForwardMessage(BaseModel):
    id: str
    group_id: str
    messages: List[MessageData]
    forward_user_id: str
    created_at: datetime

    @field_serializer("created_at")
    def serialize_dt(self, dt: datetime, _info):
        return datetime.strftime(dt, "%Y-%m-%d %H:%M:%S")


class GroupInquiry(PaginationRequest):
    name: Optional[str] = None
    is_owner: Optional[bool] = None
    list_share: Optional[bool] = None


class ResponseEvaluate(BaseModel):
    group_id: str
    message_id: str
    evaluate: str


class MessageSearch(PaginationRequest):
    group_id: str
    message_content: Optional[str] = None

class FAQSearch(PaginationRequest):
    group_id: str
    search_content: Optional[str] = None
