from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel, field_serializer

from .common import PaginationRequest


class DatabaseBase(BaseModel):
    created_at: datetime

    class Config:
        from_attributes = True

    @field_serializer("created_at")
    def serialize_dt(self, dt: datetime, _info):
        return datetime.strftime(dt, "%Y-%m-%d %H:%M:%S")


class DatabaseCreate(BaseModel):
    name: str
    group_id: Optional[str]
    description: Optional[str] = None
    member_ids: List[str]
    group_ids: List[str] = []
    show_details: bool = False


class DatabaseUpdate(BaseModel):
    name: str
    description: Optional[str] = None
    member_ids: List[str]
    show_details: bool = False


class DatabaseListFile(PaginationRequest):
    database_id: str
    file_name: Optional[str] = None


class DatabaseQueryType(PaginationRequest):
    type: Optional[str] = None
    is_owner: Optional[bool] = None


class DatabaseResponse(DatabaseBase):
    created_at: datetime
    description: Optional[str] = None
    id: str
    default_group_id: Optional[str] = None
    owner_id: str
    owner_name: str = None
    is_deleted: bool
    member_ids: List[str] = []
    group_ids: List[str] = []
    name: str
    v_name: str
    type: str = "file"
    show_details: bool = False

    class Config:
        from_attributes = True


class DatabaseGroupManage(BaseModel):
    group_ids: list
    database_id: str


class DatabaseSearch(PaginationRequest):
    content: str
