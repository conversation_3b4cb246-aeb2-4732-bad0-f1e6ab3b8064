from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel, field_serializer


class RobotBase(BaseModel):
    created_at: datetime

    class Config:
        from_attributes = True

    @field_serializer("created_at")
    def serialize_dt(self, dt: datetime, _info):
        return datetime.strftime(dt, "%Y-%m-%d %H:%M:%S")


class RobotCreate(BaseModel):
    name: str
    description: Optional[str] = None
    database_id: str
    group_ids: List[str] = []


class RobotUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    is_active: Optional[bool] = None
    database_id: str
    group_ids: Optional[List[str]] = None


class RobotResponse(RobotBase):
    id: str
    name: str
    description: Optional[str]
    database_id: str
    owner_id: str
    created_at: datetime
    group_ids: List[str]
