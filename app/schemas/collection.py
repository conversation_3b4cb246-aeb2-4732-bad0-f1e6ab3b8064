from datetime import datetime
from typing import Optional
from pydantic import BaseModel, field_serializer

from .common import PaginationRequest


class CollectionBase(BaseModel):
    created_at: datetime

    class Config:
        from_attributes = True

    @field_serializer("created_at")
    def serialize_dt(self, dt: datetime, _info):
        return datetime.strftime(dt, "%Y-%m-%d %H:%M:%S")


class CollectionCreate(BaseModel):
    content: list[str]
    group_id: str


class CollectionResponse(CollectionBase):
    id: str
    content: list[str]
    database_id: str
    group_id: str
    group_name: Optional[str] = None
    user_id: str
    abstract: str
    created_at: datetime


class GetCollectionList(PaginationRequest):
    group_id: str


class GetCollectionInfo(BaseModel):
    group_id: str
    collection_id: str


class SearchCollection(PaginationRequest):
    content: str
