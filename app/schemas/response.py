from datetime import datetime
from typing import Any, Generic, List, Optional, TypeVar
from pydantic import BaseModel

T = TypeVar("T")


class ResponseModel(Generic[T]):
    def __init__(
        self, *, code: int = 200, data: Optional[T] = None, message: str = "Success"
    ):
        self.code = code
        self.data = data
        self.message = message

    def dict(self):
        return {"code": self.code, "data": self.data, "message": self.message}


class PaginationModel(Generic[T]):
    def __init__(
        self,
        *,
        total: int = 0,
        page: int = 1,
        page_size: int = 10,
        items: List[T] = [],
        start_index: datetime = None
    ):
        self.total = total
        self.page = page
        self.page_size = page_size
        self.items = items
        self.start_index = start_index

    def dict(self):
        return {
            "total": self.total,
            "page": self.page,
            "page_size": self.page_size,
            "items": self.items,
            "start_index": self.start_index,
        }


def success(*, code: int = 200, data: Any = None, message: str = "Success") -> dict:
    if isinstance(data, BaseModel):
        data = data.model_dump()
    return ResponseModel(code=code, data=data, message=message).dict()


def error(*, code: int = 400, message: str = "Error") -> dict:
    return ResponseModel(code=code, message=message).dict()


def paginate(
    *, total: int = 0, page: int = 1, page_size: int = 10, items: List[Any] = []
) -> dict:
    return PaginationModel(
        total=total, page=page, page_size=page_size, items=items
    ).dict()
