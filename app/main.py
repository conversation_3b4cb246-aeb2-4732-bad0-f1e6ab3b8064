import asyncio
import re
from fastapi import <PERSON><PERSON><PERSON>,Request,HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from filelock import FileLock
from .mcp_server.retrieval_mcp import mcp_app
from .config import settings
from .utils.log import logger
from .utils.message2vector import flush_all_collections_to_file_and_milvus
from .utils.locales import TranslationMiddleware
from contextlib import asynccontextmanager

@asynccontextmanager
async def app_lifespan(app: FastAPI):
    # Startup
    print("Starting up the app...")
    await startup_event()
    # Initialize database, cache, etc.
    yield
    # Shutdown
    print("Shutting down the app...")


@asynccontextmanager
async def combined_lifespan(app: FastAPI):
    # Run both lifespans
    async with app_lifespan(app):
        async with mcp_app.lifespan(app):
            yield

app = FastAPI(
    docs_url="/docs" if settings.OPENAPI_DOCS else None,
    redoc_url="/redoc" if settings.OPENAPI_DOCS else None,
    openapi_url=(
        "/openapi.json" if settings.OPENAPI_DOCS else None
    ),  # 禁用 /openapi.json
    lifespan=combined_lifespan
)
# Mount the MCP server
app.mount("/analytics", mcp_app)
# CORS设置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(TranslationMiddleware)

# 常见扫描工具特征（正则表达式）
SCANNER_PATTERNS = [
    r"nikto", r"acunetix", r"sqlmap", r"nmap", r"dirb", 
    r"wpscan", r"\/\.env", r"\/\.git", r"\/wp\-admin"
]

@app.middleware("http")
async def block_scanners(request: Request, call_next):
    user_agent = request.headers.get("user-agent", "").lower()
    path = request.url.path.lower()
    
    # 检测并阻止扫描特征
    if any(
        re.search(pattern, user_agent) or re.search(pattern, path)
        for pattern in SCANNER_PATTERNS
        ):
        # 记录攻击者IP
        logger.info(f"BLOCKED SCANNER: {request.client.host} - {user_agent}")
        raise HTTPException(status_code=403, detail="Forbidden")
    
    return await call_next(request)

def create_route():
    from .routes import (
        admin_route,
        auth,
        chat,
        collection_route,
        database_route,
        file_route,
        group_route,
        llm_route,
        organization_route,
        stt_route,
        tts_route,
        user_route,
        version,
    )

    # 路由注册
    app.include_router(admin_route.router, prefix="/api/admin", tags=["admin"])
    app.include_router(auth.router, prefix="/api/auth", tags=["auth"])
    app.include_router(chat.router, prefix="/api/chat", tags=["chat"])
    app.include_router(group_route.router, prefix="/api/group", tags=["group"])
    app.include_router(file_route.router, prefix="/api/files", tags=["files"])
    app.include_router(
        database_route.router, prefix="/api/database", tags=["databases"]
    )
    app.include_router(user_route.router, prefix="/api/user", tags=["user"])
    app.include_router(organization_route.router, prefix="/api", tags=["organization"])
    app.include_router(llm_route.router, prefix="/api/llm", tags=["llm"])
    app.include_router(
        collection_route.router, prefix="/api/collection", tags=["collection"]
    )
    app.include_router(stt_route.router, prefix="/api/stt", tags=["stt"])
    app.include_router(version.router, prefix="/api/version", tags=["version"])
    app.include_router(tts_route.router, prefix="/api/tts", tags=["tts"])


# 创建路由
create_route()
app.mount("/static", StaticFiles(directory=rf"./static"), name="static")

# 创建一个文件锁，用于防止多个进程同时执行定时任务
flush_task_lock = FileLock("/tmp/flush_task.lock")


# 启动定时任务
# @app.on_event("startup")
async def startup_event():
    try:
        if flush_task_lock.acquire(blocking=False):  # 非阻塞模式，获取不到锁时跳过
            logger.info("启动定时向量化任务")
            asyncio.create_task(flush_all_collections_to_file_and_milvus())
            logger.info("启动定时向量化任务完成")
    except Exception as e:
        logger.error(f"启动定时任务失败: {e}")
        pass
