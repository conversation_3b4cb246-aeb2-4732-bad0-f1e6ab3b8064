from langchain_docling import <PERSON>ling<PERSON>oader
from langchain_community.document_loaders import PlaywrightUR<PERSON>oader, BiliBiliLoader
from langchain_community.document_loaders.csv_loader import CSVLoader
import asyncio
from app.config import get_settings


def save_document_from_url(url, save_path):
    """
    根据给定的URL判断文件类型并进行相应的保存处理。
    
    参数:
    url (str): 要处理的URL。
    save_path (str): 保存文件的路径。
    
    返回:
    str: 保存结果的消息。
    """
    if url.startswith("https://www.bilibili.com/"):
        return save_bilibili(url, save_path)
    elif url.endswith(".csv"):
        return save_csv(url, save_path)
    elif url.endswith(('.pdf', '.docx', '.pptx', '.html', 'xlsx', 'md')) or ('/pdf/' in url):
        return save_docling(url, save_path)
    elif url.startswith("http://") or url.startswith("https://"):
        return asyncio.run(save_url(url, save_path))
    else:
        return "不支持的文件类型"


def save_bilibili(url, save_path):
    settings = get_settings()
    loader = BiliBiliLoader(
        [url],
        sessdata=settings.BILIBILI_SESSDATA,
        bili_jct=settings.BILIBILI_BILI_JCT,
        buvid3=settings.BILIBILI_BUVID3,
    )
    docs = loader.load()
    content_list = [doc.page_content for doc in docs]
    # 将所有行拼接成一个字符串
    concatenated_content = "\n".join(content_list)
    return save(concatenated_content, save_path)

def save_docling(url, save_path):
    FILE_PATH = url
    loader = DoclingLoader(FILE_PATH)
    docs = loader.load()
    # 提取每个文档的page_content属性，并将其转换为字符串
    content_list = [doc.page_content for doc in docs]
    # 将所有行拼接成一个字符串
    concatenated_content = "\n".join(content_list)
    return save(concatenated_content, save_path)

def save_csv(file_path, save_path):
    loader = CSVLoader(file_path=file_path)
    docs = loader.load()
    content_list = [doc.page_content for doc in docs]
    # 将所有行拼接成一个字符串
    concatenated_content = "\n".join(content_list)
    return save(concatenated_content, save_path)

async def save_url(url, save_path):
    urls = [url]
    loader = PlaywrightURLLoader(urls=urls, remove_selectors=["header", "footer"])
    docs = await loader.aload()
    content_list = [doc.page_content for doc in docs]
    # 将所有行拼接成一个字符串
    concatenated_content = "\n".join(content_list)
    return save(concatenated_content, save_path)

def save(content, save_path):
    print(content)
    return "保存成功！"


if __name__ == "__main__":
    save_document_from_url("https://www.bilibili.com/video/BV1AUAAeUEYh/?spm_id_from=333.1007.tianma.1-1-1.click", "test.txt")



