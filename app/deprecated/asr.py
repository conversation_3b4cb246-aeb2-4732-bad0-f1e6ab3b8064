# 原本语音识别方式先不删，另外再加上一个直接识别二进制wav的识别方式

import base64
import json
from tencentcloud.common import credential
from tencentcloud.common.profile.client_profile import ClientProfile
from tencentcloud.common.profile.http_profile import HttpProfile
from tencentcloud.asr.v20190614 import asr_client, models
from pydub import AudioSegment  # 导入 PyDub
import os
import time
import requests
import urllib.parse
import hashlib
import hmac
import uuid  # 导入uuid模块
from fastapi import UploadFile
from ..config import get_settings

settings = get_settings()

# 添加音频压缩函数
def compress_audio(file_path):
    unique_filename = f"{uuid.uuid4()}.wav"  # 生成唯一文件名
    audio = AudioSegment.from_file(file_path)  # 读取音频文件
    audio = audio.set_frame_rate(16000)  # 设置帧率为16kHz
    audio = audio.set_channels(1)  # 设置为单声道
    audio = audio.set_sample_width(2)  # 设置采样宽度为2字节（16位）
    
    # 导出为压缩后的音频文件，使用较低的比特率
    audio.export(unique_filename, format="wav", bitrate="128k")  # 设置比特率为128kbps
    return unique_filename  # 返回唯一文件名

class SentenceRecognizer:
    def __init__(self, secret_id, secret_key):
        # 配置腾讯云API
        self.cred = credential.Credential(secret_id, secret_key)
        self.httpProfile = HttpProfile()
        self.httpProfile.endpoint = "asr.tencentcloudapi.com"
        self.clientProfile = ClientProfile()
        self.clientProfile.httpProfile = self.httpProfile
        self.client = asr_client.AsrClient(self.cred, "ap-shanghai", self.clientProfile)

    def recognize_audio(self, file_path):
        compressed_file_path = compress_audio(file_path)  # 使用唯一文件名
        # 读取并Base64编码压缩后的语音文件
        with open(compressed_file_path, "rb") as f:
            audio_data = f.read()
        base64_audio_data = base64.b64encode(audio_data).decode('utf-8')

        # 获取文件后缀
        file_extension = os.path.splitext(file_path)[1].lower()
        voice_format = "wav" if file_extension == ".wav" else "mp3"  # 根据文件后缀设置VoiceFormat

        # 构造请求
        req = models.SentenceRecognitionRequest()
        params = {
            "ProjectId": 0,
            "SubServiceType": 2,
            "EngSerViceType": "16k_zh",
            "SourceType": 1,
            "VoiceFormat": voice_format,
            "UsrAudioKey": "test",
            "Data": base64_audio_data
        }
        req.from_json_string(json.dumps(params))
        
        # 发送请求并处理响应
        try:
            resp = self.client.SentenceRecognition(req)
            os.remove(compressed_file_path)  # 删除压缩的音频文件

            # 提取识别结果中的文本部分
            result_json = json.loads(resp.to_json_string())
            recognized_text = result_json.get("Result", "")  # 获取识别结果中的文本
            
            return recognized_text  # 返回识别的文本
        except Exception as e:
            os.remove(compressed_file_path)  # 删除压缩的音频文件
            return f"Error: {e}"

class RecFileRecognizer:#腾讯录音文件识别，录音文件识别已换api，这个类暂时未引用
    def __init__(self, secret_id, secret_key):
        # 配置腾讯云API
        self.cred = credential.Credential(secret_id, secret_key)
        self.httpProfile = HttpProfile()
        self.httpProfile.endpoint = "asr.tencentcloudapi.com"
        self.clientProfile = ClientProfile()
        self.clientProfile.httpProfile = self.httpProfile
        self.client = asr_client.AsrClient(self.cred, "ap-guangzhou", self.clientProfile)

    def create_rec_task(self, file_path):
        # 读取并Base64编码语音文件
        with open(file_path, "rb") as f:
            file_data = f.read()
        base64_data = base64.b64encode(file_data).decode('utf-8')

        req = models.CreateRecTaskRequest()
        params = {
            "EngineModelType": "16k_zh",
            "ChannelNum": 1,
            "ResTextFormat": 0,
            "SourceType": 1,
            "Data": base64_data,
            "DataLen": len(file_data)
        }
        req.from_json_string(json.dumps(params))

        resp = self.client.CreateRecTask(req)
        return json.loads(resp.to_json_string())['Data']['TaskId']

    def query_rec_task_status(self, task_id):
        req = models.DescribeTaskStatusRequest()
        params = {
            "TaskId": task_id
        }
        req.from_json_string(json.dumps(params))

        resp = self.client.DescribeTaskStatus(req)
        return json.loads(resp.to_json_string())

class IfasrRecognizer:
    def __init__(self, appid, secret_key, upload_file_path):
        self.appid = appid
        self.secret_key = secret_key
        self.upload_file_path = upload_file_path
        self.lfasr_host = 'https://raasr.xfyun.cn/v2/api'
        self.api_upload = '/upload'
        self.api_get_result = '/getResult'
        self.ts = str(int(time.time()))
        self.signa = self.get_signa()

    def get_signa(self):
        m2 = hashlib.md5()
        m2.update((self.appid + self.ts).encode('utf-8'))
        md5 = m2.hexdigest()
        md5 = bytes(md5, encoding='utf-8')
        signa = hmac.new(self.secret_key.encode('utf-8'), md5, hashlib.sha1).digest()
        signa = base64.b64encode(signa)
        return str(signa, 'utf-8')

    def upload(self):
        print("上传部分：")
        # 压缩音频文件
        compressed_file_path = compress_audio(self.upload_file_path)  # 使用唯一文件名
        file_len = os.path.getsize(compressed_file_path)  # 获取压缩后文件的大小
        file_name = os.path.basename(compressed_file_path)  # 获取压缩后文件的名称

        param_dict = {
            'appId': self.appid,
            'signa': self.signa,
            'ts': self.ts,
            'fileSize': file_len,
            'fileName': file_name,
            'duration': "200"
        }
        print("upload参数：", param_dict)
        data = open(compressed_file_path, 'rb').read(file_len)  # 读取压缩后的文件数据

        response = requests.post(url=self.lfasr_host + self.api_upload + "?" + urllib.parse.urlencode(param_dict),
                                 headers={"Content-type": "application/json"}, data=data)
        result = json.loads(response.text)
        print("upload resp:", result)

        os.remove(compressed_file_path)  # 删除压缩后的音频文件
        return result

    def get_result(self):
        uploadresp = self.upload()
        orderId = uploadresp['content']['orderId']
        param_dict = {
            'appId': self.appid,
            'signa': self.signa,
            'ts': self.ts,
            'orderId': orderId,
            'resultType': "transfer"
        }
        print("查询部分：")
        # print("get result参数：", param_dict)
        status = 3
        while status == 3:
            time.sleep(5)
            response = requests.post(url=self.lfasr_host + self.api_get_result + "?" + urllib.parse.urlencode(param_dict),
                                     headers={"Content-type": "application/json"})
            result = json.loads(response.text)
            status = result['content']['orderInfo']['status']
            print("status=", status)
            if status == 4:
                break
        
        # 数据清洗逻辑
        order_result = result.get('content', {}).get('orderResult', '')
        parsed_result = json.loads(order_result) if order_result else {}
        
        recognized_text = []
        for lattice in parsed_result.get('lattice', []):
            json_1best = json.loads(lattice.get('json_1best', '{}'))
            words = json_1best.get('st', {}).get('rt', [{}])[0].get('ws', [])
            recognized_text.extend([word['cw'][0]['w'] for word in words if word['cw']])

        # 将识别结果拼接成字符串
        cleaned_result = ''.join(recognized_text)
        print("Cleaned IfasrRecognizer Result:", cleaned_result)
        return cleaned_result  # 返回清洗后的结果
    
async def recognize_audio_file(file: UploadFile):
    recognizer = IfasrRecognizer(settings.xf_APPID, settings.xf_ASR_SECRET_KEY, file)  # 使用文件对象
    recognized_text = recognizer.get_result()  # 使用文件对象进行识别
    return recognized_text
