# denoise.py

import noisereduce as nr
from scipy.io import wavfile
import asyncio

# 降噪处理函数
async def denoise_audio(input_file, output_file):
    loop = asyncio.get_event_loop()
    
    # 使用 run_in_executor 来执行阻塞操作
    rate, audio = await loop.run_in_executor(None, wavfile.read, input_file)
    
    # 降噪
    reduced_noise = nr.reduce_noise(y=audio, sr=rate)
    
    # 保存降噪后的音频
    await loop.run_in_executor(None, wavfile.write, output_file, rate, reduced_noise)
    print(f"降噪后的音频已保存为 {output_file}")
    
    return True  # 返回成功标志
