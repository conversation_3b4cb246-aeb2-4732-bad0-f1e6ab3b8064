import asyncio
import json
import os
import re
import subprocess
import uuid

from vosk import KaldiRecognizer, Model, SetLogLevel

from ..config import settings
from ..database import get_chat_database, get_database
from ..models.user import User
from ..utils.file_handler import download_file, save_file
from ..utils.llm import background_answer, broadcast_and_save_msg
from ..utils.log import logger


class VoiceTranscriber:
    def __init__(self, language="cn", sample_rate=16000):
        self.sample_rate = sample_rate
        self.language = language.lower()

        if self.language not in ["cn", "en"]:
            raise ValueError("Language must be 'cn' or 'en'")

        self.model = vosk_models[self.language]
        self.recognizer = KaldiRecognizer(self.model, self.sample_rate)
        self.text_processor = SpaceToSentence(language=self.language)

    def _convert_audio(self, audio_data):
        """使用ffmpeg将原始音频数据转换为vosk需要的格式"""
        cmd = [
            "ffmpeg",
            "-loglevel",
            "quiet",  # 隐藏日志输出
            "-i",
            "pipe:0",  # 从标准输入读取
            "-ar",
            str(self.sample_rate),  # 确保采样率正确（如16000）
            "-ac",
            "1",  # 单声道
            "-f",
            "s16le",  # 输出格式为16bit小端
            "-y",  # 覆盖输出文件（此处用于管道）
            "pipe:1",  # 输出到标准输出
        ]

        try:
            with subprocess.Popen(
                cmd,
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,  # 捕获错误输出以便调试
            ) as process:
                # 使用communicate一次性处理输入输出
                stdout_data, stderr_data = process.communicate(input=audio_data)

                # 检查返回码
                if process.returncode != 0:
                    raise RuntimeError(
                        f"FFmpeg转换失败，错误信息：{stderr_data.decode('utf-8')}"
                    )

                return stdout_data
        except Exception as e:
            raise RuntimeError(f"音频转换异常: {str(e)}")

    def transcribe_audio(self, audio_data):
        """直接传入音频数据进行识别"""
        # 转换音频格式
        converted_data = self._convert_audio(audio_data)

        # 分块处理音频数据
        for offset in range(0, len(converted_data), 4000):
            chunk = converted_data[offset : offset + 4000]
            if self.recognizer.AcceptWaveform(chunk):
                continue

        # 获取识别结果
        final_result = json.loads(self.recognizer.FinalResult())
        recognized_text = final_result["text"]

        return (
            self.text_processor.process(recognized_text)
            if recognized_text.strip()
            else "未能识别任何内容，请提示用户检查音频文件。"
        )


class SpaceToSentence:
    def __init__(self, language="cn"):
        self.language = language.lower()
        self.end_punctuation = {"cn": "。", "en": "."}

        # 中文规则以保守策略处理
        if self.language == "cn":
            # 标点预测规则：根据句子结构和词语特征自动添加合适的标点符号
            self.punctuation_rules = [
                # 1. 疑问语气词检测：句子以语气词结尾时添加问号
                (
                    r"(\S+)(吗|呢|吧|啊|么|哪|嘛)$",
                    lambda m: f"{m.group(1)}{m.group(2)}？",
                ),
                # 2. 陈述句结尾处理：以特定语气词结尾时添加句号
                (r"(哦)$", lambda m: f"{m.group(1)}。"),
                # 3. 过渡句处理：以逻辑连词结尾时添加逗号
                (
                    r"(然后|因此|所以|因为|此外|因而|由此|而且|并且|但是|然而|换句话说|也就是说|也即是说)$",
                    lambda m: f"{m.group(1)}，",
                ),
                # 4. 感叹词处理：在语气词句子末尾添加感叹号
                (r"(呀|啦|哇)", lambda m: f"{m.group(1)}、"),
            ]
            # 连接词合并规则：处理特殊语言结构的连接和格式化
            self.connection_rules = [
                # 1. 助词处理：确保"的地得"等助词与相邻词正确连接
                (r"(\S+)\s+([的地得])\s+", r"\1\2"),
                # 2. 并列词处理：在并列连词前添加顿号
                # (r'([^\s，。！？]) (?=等|及|与|和|或)', r'\1、'),
                # 3. 日期格式化：确保日期格式的连贯性
                # (r'(\d+月)(\d+日)', r'\1\2日')
            ]

        # 英文规则集合
        elif self.language == "en":
            # 标点预测规则：根据英文句子结构添加合适的标点符号
            self.punctuation_rules = [
                # 1. WH疑问句处理：识别以疑问词开头的句子并添加问号
                (
                    r"\b(what|how|where|when|why|who)\b\s+([^.?!]+)(\s*)$",
                    lambda m: f"{m.group(0).strip()}?",
                ),
                # 2. 一般疑问句处理：识别以助动词开头的句子并添加问号
                # (r'\b(is|are|do|does|did|can|could|will|would)\b\s+([^.?!]+)(\s*)$',
                #  lambda m: f"{m.group(0).strip()}?"),
                # 3. 过渡词处理：对常见过渡词进行首字母大写并添加句号
                (
                    r"\b(okay|alright|well|right|sure|now|so|then)\b[.]?(\s+)",
                    lambda m: f"{m.group(1).title()}.{m.group(2)}",
                ),
                # 4. 复合句处理：在转折和因果连词前添加适当的句号
                (
                    r"(.{8,}?)(\s+but\s+|\s+however\s+|\s+therefore\s+)(.*?)(\s*)$",
                    lambda m: f"{m.group(1)}.{m.group(2)}{m.group(3)}",
                ),
            ]
            # 连接词合并规则：处理英文特殊语言结构
            self.connection_rules = [
                # 1. 连接词格式化：在转折词和连接词前添加逗号
                (r"\s+(but|however|therefore|moreover)\b", r", \1"),
                # 2. 缩写处理：确保英文缩写形式的正确性（如's, 'd, 't等）
                (r"(\w+)'\s(s|d|t|m|re|ll|ve)\b", r"\1'\2"),
            ]
        else:
            raise ValueError("Language must be 'cn' or 'en'")

    def process(self, text):
        # 预处理
        text = re.sub(r"\s+", " ", text).strip()

        # 语言分支处理
        if self.language == "cn":
            # 中文保持分块处理
            for pattern, repl in self.connection_rules:
                text = re.sub(pattern, repl, text)

            segments = re.split(r"\s+", text)
            for i in range(len(segments) - 1, -1, -1):
                for pattern, handler in self.punctuation_rules:
                    if match := re.search(pattern, segments[i]):
                        segments[i] = handler(match)
                        break
            text = "".join(segments)

        elif self.language == "en":
            # 英文整体处理
            for pattern, repl in self.connection_rules:
                text = re.sub(pattern, repl, text, flags=re.IGNORECASE)

            for pattern, handler in self.punctuation_rules:
                text = re.sub(pattern, handler, text, flags=re.IGNORECASE)

            # 英文格式处理
            text = re.sub(
                r"([.!?])\s*([a-z])",
                lambda m: f"{m.group(1)} {m.group(2).upper()}",
                text,
            )
            text = text[0].upper() + text[1:]

        # 统一添加结尾标点
        if not re.search(r"[.!?。！？]$", text):
            text += self.end_punctuation[self.language]

        # 清理多余空格
        return re.sub(r"\s+([.,!?。！？])", r"\1", text)


async def process_audio_for_wakeword(
    content: bytes,
    group: dict,
    group_collection,
    message_id: str,
    model: str,
    user: User,
    language: str = "cn",
    _=None
) -> None:
    """
    异步处理音频文件并触发唤醒词响应
    Args:
        file: 文件对象
        group: 群组数据
        group_collection: 群组数据库集合
        message_id: 消息ID
        model: 模型名称
        language: 识别语言，默认为'cn'
    """
    manage_db = await get_database()
    # 异步读取文件内容
    transcriber = VoiceTranscriber()
    # recognized_text = transcriber.transcribe_audio(content)
    recognized_text = await asyncio.to_thread(transcriber.transcribe_audio, content)
    logger.critical(f"识别结果: {recognized_text}")

    # 1. 根据中文选模型
    # 2. TODO: 增加唤醒词设置后，编写唤醒词模型映射查询逻辑
    # 触发智能体回应
    user_data = await manage_db.users.find_one(
        {
            "id": user.id,
            "is_active": True,
        }
    )
    asyncio.create_task(
        background_answer(
            group_data=group,
            question=recognized_text,
            llm_model=model,
            group_collection=group_collection,
            user_data=user_data,
            cite_id=message_id,
            _=_
        )
    )

    try:
        # 将识别结果写入transfer字段
        await group_collection.update_one(
            {"id": message_id}, {"$set": {"transfer": recognized_text}}
        )
    except Exception as e:
        logger.error(f"保存识别结果失败: {str(e)}")


async def recognize_audio_file(
    file_id: str, message_id: str, group_id: str, current_user, long: bool
) -> str:
    """
    根据文件ID和群组ID获取音频数据

    Args:
        file_id: 文件唯一标识
        group_id: 群组ID（用于日志追踪）
        manage_db: 数据库连接
        current_user: 用户数据
        long: 是否长语音识别
    Returns:
        str: 识别结果
    """
    try:
        manage_db = await get_database()
        chat_db = await get_chat_database()
        group_collection = getattr(chat_db, group_id)
        # 查询文件记录
        file_record = await manage_db.files.find_one(
            {"id": file_id, "is_deleted": False}
        )

        if not file_record:
            logger.error(f"音频文件 {file_id} 不存在或已被删除")
            await group_collection.update_one(
                {"id": message_id}, {"$set": {"transfer_status": "fail"}}
            )
            return "识别失败,音频文件不存在或已被删除"

        # 获取文件路径
        local_file_path = file_record.get("local_file_path")
        cloud_file_path = file_record.get("cloud_file_path")

        # 路径校验
        if not all([local_file_path, cloud_file_path]):
            logger.error(f"文件路径信息不完整 [文件:{file_id}")
            await group_collection.update_one(
                {"id": message_id}, {"$set": {"transfer_status": "fail"}}
            )
            return "识别失败,文件路径信息不完整,请重试"

        if os.path.exists(local_file_path):
            # 本地文件存在，直接打开本地文件获取audio_data
            with open(local_file_path, "rb") as f:
                audio_data = f.read()
        else:
            # 本地不存在则从云端下载
            audio_file_path = cloud_file_path
            try:
                # 下载音频文件
                local_audio_file_path = await download_file(audio_file_path)
                with open(local_audio_file_path, "rb") as f:
                    audio_data = f.read()
            except Exception as download_error:
                logger.error(
                    f"文件下载失败 [路径:{cloud_file_path}: {str(download_error)}"
                )
                await group_collection.update_one(
                    {"id": message_id}, {"$set": {"transfer_status": "fail"}}
                )
                return "识别失败,服务器未能获取录音文件,请重试"

        await group_collection.update_one(
            {"id": message_id}, {"$set": {"transfer_status": "parsing"}}
        )
        logger.info(f"开始识别音频文件 {file_id}")
        transcriber = VoiceTranscriber()
        recognition_result = await asyncio.to_thread(
            transcriber.transcribe_audio, audio_data
        )
        logger.info(f"结束识别音频文件 {file_id}")
        if long:
            # 把文件通过save上传到云端
            create_time = file_record.get("created_at")
            txt_file_name = f"{create_time}音频识别结果.txt"
            safe_filename = "".join(
                char for char in txt_file_name if char.isalnum() or char in "._- "
            )
            txt_header = {
                "filename": safe_filename,
                "content_type": "text/plain",
                "size": len(recognition_result),
            }
            txt_file_content = recognition_result.encode("utf-8")
            doc_uuid = str(uuid.uuid4())
            await save_file(
                txt_file_content,
                current_user,
                doc_uuid,
                manage_db,
                txt_header,
                None,
                group_id,
            )
            # 广播发送文档的消息
            message = await broadcast_and_save_msg(
                group_collection=group_collection,
                manage_db=manage_db,
                content={"id": doc_uuid, "filename": safe_filename},
                type="text/plain",
                group_id=group_id,
                sender_id=current_user.id,
                cite_id=message_id,
                duration=None,
            )
            update_data = {
                "$set": {
                    "transfer_status": "success",
                    "transfer_doc_id": doc_uuid,
                    "cite": message.model_dump(),
                }
            }
        else:
            update_data = {
                "$set": {"transfer": recognition_result, "transfer_status": "success"}
            }
        await group_collection.update_one(
            {"id": message_id},
            update_data,
        )
        return recognition_result
    except Exception as e:
        logger.error(f"获取识别结果异常 [文件:{file_id}]: {str(e)}", exc_info=True)
        await group_collection.update_one(
            {"id": message_id}, {"$set": {"transfer_status": "fail"}}
        )
        return "识别失败,识别过程出现异常,请重试"


# 初始化Vosk模型
SetLogLevel(0)  # 只显示 WARNING 和 ERROR
vosk_models = {
    "cn": Model(model_path=rf"{settings.MODEL_root_dir}/vosk-model-small-cn-0.22"),
    "en": Model(model_path=rf"{settings.MODEL_root_dir}/vosk-model-small-en-us-0.15"),
}
