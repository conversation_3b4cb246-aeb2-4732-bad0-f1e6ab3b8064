from fastapi import APIRouter, Depends

from ..database import get_database
from ..models.robot import Robot
from ..models.user import User
from ..schemas.common import PaginationRequest
from ..schemas.response import PaginationModel, error, success
from ..schemas.robot import RobotCreate, RobotResponse, RobotUpdate
from ..utils.auth import get_current_user
from ..utils.log import logger

router = APIRouter()


# 创建机器人
@router.post("/robot/create", description="创建机器人")
async def create_robot(
    robot_data: RobotCreate,
    current_user: User = Depends(get_current_user),
    db=Depends(get_database),
):
    try:
        # 查询知识库
        database = await db.databases.find_one(
            {"id": robot_data.database_id, "is_deleted": False}
        )
        if not database:
            return error(code=404, message="知识库不存在")

        # 检查权限
        if current_user.id not in database["member_ids"]:
            logger.warning(
                f"发现用户 {current_user.id} 越权使用知识库 {database['id']}"
            )
            return error(code=403, message="无权使用此知识库")

        # 创建机器人
        robot = Robot(
            name=robot_data.name,
            description=robot_data.description,
            owner_id=current_user.id,
            database_id=robot_data.database_id,
            group_ids=robot_data.group_ids,
        )
        await db.robots.insert_one(robot.model_dump())
        return success(data=robot, message="创建成功")
    except Exception as e:
        logger.error(f"创建机器人失败: {str(e)}", exc_info=True)
        return error(code=500, message=f"创建机器人失败：{str(e)}")


# 获取单个机器人
@router.get("/robot/{robot_id}", description="获取单个机器人")
async def get_robot(
    robot_id: str,
    current_user: User = Depends(get_current_user),
    db=Depends(get_database),
):
    try:
        robot_dict = await db.robots.find_one({"id": robot_id, "is_active": True})
        if not robot_dict:
            return error(code=404, message="机器人不存在")

        # 检查权限
        if robot_dict["owner_id"] != current_user.id:
            return error(code=403, message="无权访问此机器人")

        robot = RobotResponse(**robot_dict).model_dump()
        return success(data=robot)
    except Exception as e:
        logger.error(f"获取机器人失败: {str(e)}", exc_info=True)
        return error(code=500, message=f"获取机器人失败：{str(e)}")


# 获取用户的所有机器人
@router.post("/robot/list/my", description="获取用户的所有机器人")
async def list_my_robots(
    pagination: PaginationRequest,
    current_user: User = Depends(get_current_user),
    db=Depends(get_database),
):
    try:
        if pagination.page_size > 50:
            return error(code=422, message="每页数量不能超过50")
        skip = (pagination.page - 1) * pagination.page_size

        # 获取总数
        total = await db.robots.count_documents(
            {"owner_id": current_user.id, "is_active": True}
        )

        # 获取分页数据
        robots = (
            await db.robots.find({"owner_id": current_user.id, "is_active": True})
            .sort("created_at", -1)
            .skip(skip)
            .limit(pagination.page_size)
            .to_list(None)
        )

        return success(
            data=PaginationModel(
                total=total,
                page=pagination.page,
                page_size=pagination.page_size,
                items=[RobotResponse(**robot).model_dump() for robot in robots],
            )
        )
    except Exception as e:
        logger.error(f"获取机器人列表失败: {str(e)}", exc_info=True)
        return error(code=500, message=f"获取机器人列表失败：{str(e)}")


# 更新机器人
@router.put("/robot/{robot_id}", description="更新机器人信息")
async def update_robot(
    robot_id: str,
    robot_data: RobotUpdate,
    current_user: User = Depends(get_current_user),
    db=Depends(get_database),
):
    try:
        # 查询知识库
        database = await db.databases.find_one(
            {"id": robot_data.database_id, "is_deleted": False}
        )
        if not database:
            return error(code=404, message="知识库不存在")

        # 检查机器人是否存在
        robot_dict = await db.robots.find_one({"id": robot_id, "is_active": True})
        if not robot_dict:
            return error(code=404, message="机器人不存在")

        # 检查权限
        if robot_dict["owner_id"] != current_user.id:
            logger.warning(
                f"发现用户 {current_user.id} 越权修改机器人 {database['id']}"
            )
            return error(code=403, message="无权修改此机器人")

        # 检查权限
        if current_user.id not in database["member_ids"]:
            logger.warning(
                f"发现用户 {current_user.id} 越权使用知识库 {database['id']}"
            )
            return error(code=403, message="无权使用此知识库")

        # 更新数据
        update_data = {
            k: v for k, v in robot_data.model_dump().items() if v is not None
        }
        await db.robots.update_one({"id": robot_id}, {"$set": update_data})

        # 返回更新后的数据
        updated_robot = await db.robots.find_one({"id": robot_id, "is_active": True})
        return success(
            data=RobotResponse(**updated_robot).model_dump(), message="更新成功"
        )
    except Exception as e:
        logger.error(f"更新机器人失败: {str(e)}", exc_info=True)
        return error(code=500, message=f"更新机器人失败：{str(e)}")


# 删除机器人
@router.delete("/robot/{robot_id}", description="删除机器人")
async def delete_robot(
    robot_id: str,
    current_user: User = Depends(get_current_user),
    db=Depends(get_database),
):
    try:
        # 检查机器人是否存在
        robot_dict = await db.robots.find_one({"id": robot_id, "is_active": True})
        if not robot_dict:
            return error(code=404, message="机器人不存在")

        # 检查权限
        if robot_dict["owner_id"] != current_user.id:
            return error(code=403, message="无权删除此机器人")

        # 删除机器人
        await db.robots.update_one({"id": robot_id}, {"$set": {"is_active": False}})
        return success(message="删除成功")
    except Exception as e:
        logger.error(f"删除机器人失败: {str(e)}", exc_info=True)
        return error(code=500, message=f"删除机器人失败：{str(e)}")
