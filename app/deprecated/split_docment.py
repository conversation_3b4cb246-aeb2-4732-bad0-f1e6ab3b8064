import spacy

nlp = spacy.load("en_core_web_sm")

def cut_content(content, max_length):

    doc = nlp(content)
    final_sentences = []
    for sent in doc.sents:
        sent_text = sent.text.strip()  # 去除首尾空格
        if len(sent_text) > max_length:
            # 按空格或其他规则拆分长句子
            words = sent_text.split()
            current_sentence = ""
            for word in words:
                if len(current_sentence) + len(word) + 1 <= max_length:
                    current_sentence += " " + word if current_sentence else word
                else:
                    if current_sentence:  # 确保当前句子不为空
                        final_sentences.append(current_sentence)
                    current_sentence = word
            if current_sentence:
                final_sentences.append(current_sentence)
        else:
            final_sentences.append(sent_text)
    return final_sentences
