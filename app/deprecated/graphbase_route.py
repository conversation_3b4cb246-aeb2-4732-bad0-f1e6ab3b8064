import os
import uuid
from typing import Annotated

from fastapi import APIRouter, BackgroundTasks, Depends, File, Form, UploadFile

from ..config import settings
from ..database import get_database
from ..models.user import User
from ..schemas.response import error, success
from ..utils.auth import get_current_user
from ..utils.database import save_file_to_graphbase
from ..utils.file_handler import validate_file
from ..utils.graphbase import AsyncGraphDatabase
from ..utils.llm import query_graph
from ..utils.log import logger
from ..utils.remote_model import system_graphbase_manager

router = APIRouter()


@router.post("/database/upload", description="知识图谱上传文档")
async def upload_file_to_database(
    background_tasks: BackgroundTasks,
    file: Annotated[UploadFile, File()],
    database_id: Annotated[str, Form()],
    current_user: User = Depends(get_current_user),
    manage_db=Depends(get_database),
) -> dict:
    try:
        logger.critical(f"用户{current_user.id}上传文档到数据库{database_id}接口")
        doc_uuid = str(uuid.uuid4())
        # 验证文件
        await validate_file(file, settings.ALLOWED_EXTENSIONS, 0)

        # 查询collection_name
        database = await manage_db.databases.find_one(
            {"id": database_id, "is_deleted": False}
        )

        if not database:
            return error(code=404, message="数据库不存在")

        if current_user.id not in database["member_ids"]:
            logger.warning(
                f"发现用户{current_user.id}非法上传文档到数据库{database_id}"
            )
            return error(code=403, message="您没有权限访问该数据库")
        safe_filename = "".join(
            char for char in file.filename if char.isalnum() or char in "._- "
        )
        file.filename = safe_filename
        content = await file.read()
        file_header = {
            "filename": file.filename,
            "content_type": file.content_type,
            "size": file.size,
        }
        background_tasks.add_task(
            save_file_to_graphbase,
            content,
            file_header,
            current_user,
            doc_uuid,
            database,
            manage_db,
        )
        return success(message="后台上传中")

    except ValueError as e:
        return error(code=400, message=str(e))
    except Exception as e:
        logger.error(
            f"用户{current_user.id}在知识库{database_id}上传文档接口出错:{str(e)}"
        )
        return error(code=500, message=f"服务端遇到未知异常：{str(e)}")


@router.post("/query", description="检查检索效果")
async def query_check(
    group_id: Annotated[str, Form()],
    question: Annotated[str, Form()],
    manage_db=Depends(get_database),
):
    try:
        group_data = await manage_db.groups.find_one(
            {"id": group_id, "is_dissolved": False}
        )
        query = await query_graph(group_data, question)
        return success(message=query)
    except Exception as e:
        return error(code=500, message=f"服务端遇到未知异常：{str(e)}")


@router.delete("/database/delete/{file_id}", description="删除知识库文件")
async def delete_database_file(
    file_id: str,
    current_user: User = Depends(get_current_user),
    manage_db=Depends(get_database),
):
    try:
        logger.critical(f"用户{current_user.id}删除知识库文件{file_id}")
        # 查询文件记录
        file_record = await manage_db.files.find_one(
            {
                "id": file_id,
                "is_deleted": False,
            }
        )
        if not file_record:
            return error(code=404, message="文件不存在")

        # 获取关联的数据库ID
        database_id = file_record.get("vectordb_id")
        if not database_id:
            return error(code=409, message="文件未关联到知识库")

        # 查询数据库信息
        database = await manage_db.databases.find_one(
            {
                "id": database_id,
                "is_deleted": False,
            }
        )
        if not database:
            return error(code=404, message="关联的数据库不存在")

        # 检查用户权限
        if current_user.id not in database["member_ids"]:
            logger.warning(f"用户{current_user.id}尝试非法删除文件{file_id}")
            return error(code=403, message="无操作权限")

        # 删除云存储和本地文件
        if os.path.exists(file_record["local_file_path"]):
            os.remove(file_record["local_file_path"])

        # 删除neo4j中的项
        if await system_graphbase_manager.check_graph_database_exists(database_id):
            kgdb = None  # 声明变量用于finally访问
            try:
                kgdb = AsyncGraphDatabase(database_id)
                await kgdb.delete_entity(file_id)
            finally:
                if kgdb:
                    await kgdb.close()  # 新增关闭连接

        # 把file中的is_deleted设置为True
        await manage_db.files.update_one(
            {"id": file_id}, {"$set": {"is_deleted": True}}
        )

        return success(message="文件删除成功")

    except Exception as e:
        logger.error(f"删除文件{file_id}失败: {str(e)}")
        return error(code=500, message=f"删除失败: {str(e)}")
