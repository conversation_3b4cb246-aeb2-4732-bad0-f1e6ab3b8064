# For prerequisites running the following sample, visit https://help.aliyun.com/document_detail/611472.html

from http import HTTPStatus
import dashscope
import json
import requests

from app.config import get_settings

def voice_to_text(file_url: str) -> str:
    """
    将语音文件转换为文本
    Args:
        file_url: 语音文件的URL链接
    Returns:
        str: 转换后的文本内容
    """
    # 初始化API Key
    settings = get_settings()
    dashscope.api_key = settings.ALITONGYI_SK

    # 调用语音识别API
    task_response = dashscope.audio.asr.Transcription.async_call(
        model='paraformer-v2',
        file_urls=[file_url],
        language_hints=['zh', 'en']  # "language_hints"只支持paraformer-v2和paraformer-realtime-v2模型
    )

    # 等待识别结果
    transcribe_response = dashscope.audio.asr.Transcription.wait(task=task_response.output.task_id)
    if transcribe_response.status_code == HTTPStatus.OK:
        results = transcribe_response.output.results
        for result in results:
            transcription_url = result['transcription_url']
            # 发送HTTP请求获取JSON内容
            response = requests.get(transcription_url)
            if response.status_code == HTTPStatus.OK:
                transcription_json = response.json()
                return transcription_json['transcripts'][0]['text']
    
    return ""  # 如果识别失败返回空字符串

if __name__ == "__main__":
    print(voice_to_text('https://dashscope.oss-cn-beijing.aliyuncs.com/samples/audio/paraformer/hello_world_female2.wav'))
