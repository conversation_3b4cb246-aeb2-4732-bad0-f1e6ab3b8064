# 用于识别socket连接的语音识别
import base64
import json
from tencentcloud.common import credential
from tencentcloud.common.profile.client_profile import ClientProfile
from tencentcloud.common.profile.http_profile import HttpProfile
from tencentcloud.asr.v20190614 import asr_client, models
from pydub import AudioSegment  # 导入 PyDub
import os
import time
import requests
import urllib.parse
import hashlib
import hmac
import uuid  # 导入uuid模块
from ..config import get_settings

# 添加音频压缩函数
def compress_audio(file_path):
    unique_filename = f"{uuid.uuid4()}.wav"  # 生成唯一文件名
    audio = AudioSegment.from_file(file_path)  # 读取音频文件
    audio = audio.set_frame_rate(16000)  # 设置帧率为16kHz
    audio = audio.set_channels(1)  # 设置为单声道
    audio = audio.set_sample_width(2)  # 设置采样宽度为2字节（16位）
    
    # 导出为压缩后的音频文件，使用较低的比特率
    audio.export(unique_filename, format="wav", bitrate="128k")  # 设置比特率为128kbps
    return unique_filename  # 返回唯一文件名

class SentenceRecognizer:
    def __init__(self, secret_id, secret_key):
        # 配置腾讯云API
        self.cred = credential.Credential(secret_id, secret_key)
        self.httpProfile = HttpProfile()
        self.httpProfile.endpoint = "asr.tencentcloudapi.com"
        self.clientProfile = ClientProfile()
        self.clientProfile.httpProfile = self.httpProfile
        self.client = asr_client.AsrClient(self.cred, "ap-shanghai", self.clientProfile)

    def recognize_audio(self, audio_string):
        # 将字符串转换为字节数据
        audio_data = base64.b64decode(audio_string)  # 假设输入的字符串是Base64编码的音频数据
        
        # 将音频数据保存为临时文件以进行压缩
        temp_audio_file = "temp_audio.wav"
        with open(temp_audio_file, 'wb') as f:
            f.write(audio_data)

        # 压缩音频
        compressed_audio_file = compress_audio(temp_audio_file)

        # 读取压缩后的音频文件并转换为Base64字符串
        with open(compressed_audio_file, 'rb') as f:
            compressed_audio_data = f.read()
            base64_audio_data = base64.b64encode(compressed_audio_data).decode('utf-8')

        # 删除临时文件
        os.remove(temp_audio_file)
        os.remove(compressed_audio_file)

        # 构造请求
        req = models.SentenceRecognitionRequest()
        params = {
            "ProjectId": 0,
            "SubServiceType": 2,
            "EngSerViceType": "16k_zh",
            "SourceType": 1,
            "VoiceFormat": "wav",
            "UsrAudioKey": "test",
            "Data": base64_audio_data
        }
        req.from_json_string(json.dumps(params))
        
        # 发送请求并处理响应
        try:
            resp = self.client.SentenceRecognition(req)
            
            # 提取识别结果中的文本部分
            result_json = json.loads(resp.to_json_string())
            recognized_text = result_json.get("Result", "")  # 获取识别结果中的文本
            
            return recognized_text  # 返回识别的文本
        except Exception as e:
            return f"Error: {e}"

class IfasrRecognizer:
    def __init__(self, appid, secret_key, audio_string):
        self.appid = appid
        self.secret_key = secret_key
        self.audio_string = audio_string  # 修改为音频字符串
        self.lfasr_host = 'https://raasr.xfyun.cn/v2/api'
        self.api_upload = '/upload'
        self.api_get_result = '/getResult'
        self.ts = str(int(time.time()))
        self.signa = self.get_signa()

    def get_signa(self):
        m2 = hashlib.md5()
        m2.update((self.appid + self.ts).encode('utf-8'))
        md5 = m2.hexdigest()
        md5 = bytes(md5, encoding='utf-8')
        signa = hmac.new(self.secret_key.encode('utf-8'), md5, hashlib.sha1).digest()
        signa = base64.b64encode(signa)
        return str(signa, 'utf-8')

    def upload(self):
        print("上传部分：")
        # 将字符串转换为字节数据
        audio_data = base64.b64decode(self.audio_string)  # 假设输入的字符串是Base64编码的音频数据
        
        # 将音频数据保存为临时文件以进行压缩
        temp_audio_file = "temp_audio.wav"
        with open(temp_audio_file, 'wb') as f:
            f.write(audio_data)

        # 压缩音频
        compressed_audio_file = compress_audio(temp_audio_file)

        # 读取压缩后的音频文件并转换为Base64字符串
        with open(compressed_audio_file, 'rb') as f:
            compressed_audio_data = f.read()
            base64_audio_data = base64.b64encode(compressed_audio_data).decode('utf-8')

        # 删除临时文件
        os.remove(temp_audio_file)
        os.remove(compressed_audio_file)

        file_len = len(compressed_audio_data)  # 获取压缩后音频数据的大小
        file_name = "audio.wav"  # 可以设置一个默认的文件名

        param_dict = {
            'appId': self.appid,
            'signa': self.signa,
            'ts': self.ts,
            'fileSize': file_len,
            'fileName': file_name,
            'duration': "200"
        }
        print("upload参数：", param_dict)

        response = requests.post(url=self.lfasr_host + self.api_upload + "?" + urllib.parse.urlencode(param_dict),
                                 headers={"Content-type": "application/json"}, data=compressed_audio_data)
        result = json.loads(response.text)
        print("upload resp:", result)

        return result

    def get_result(self):
        uploadresp = self.upload()
        orderId = uploadresp['content']['orderId']
        param_dict = {
            'appId': self.appid,
            'signa': self.signa,
            'ts': self.ts,
            'orderId': orderId,
            'resultType': "transfer"
        }
        print("查询部分：")
        print("get result参数：", param_dict)
        status = 3
        while status == 3:
            time.sleep(5)
            response = requests.post(url=self.lfasr_host + self.api_get_result + "?" + urllib.parse.urlencode(param_dict),
                                     headers={"Content-type": "application/json"})
            result = json.loads(response.text)
            status = result['content']['orderInfo']['status']
            print("status=", status)
            if status == 4:
                break
        
        # 数据清洗逻辑
        order_result = result.get('content', {}).get('orderResult', '')
        parsed_result = json.loads(order_result) if order_result else {}
        
        recognized_text = []
        for lattice in parsed_result.get('lattice', []):
            json_1best = json.loads(lattice.get('json_1best', '{}'))
            words = json_1best.get('st', {}).get('rt', [{}])[0].get('ws', [])
            recognized_text.extend([word['cw'][0]['w'] for word in words if word['cw']])

        # 将识别结果拼接成字符串
        cleaned_result = ''.join(recognized_text)
        print("Cleaned IfasrRecognizer Result:", cleaned_result)
        return cleaned_result  # 返回清洗后的结果


async def voice_message_handler(data, websocket):
    """
    处理语音消息的函数
    """
    # 识别语音
    audio_data = data['content']  # 获取音频数据
    audio_size = len(base64.b64decode(audio_data))  # 计算音频数据大小

    # 将base64字符串转换为临时文件并进行压缩
    temp_audio_file = "temp_audio.wav"
    with open(temp_audio_file, 'wb') as f:
        f.write(base64.b64decode(audio_data))

    # 压缩音频
    compressed_audio_file = compress_audio(temp_audio_file)

    # 读取压缩后的音频文件并转换为Base64字符串
    with open(compressed_audio_file, 'rb') as f:
        compressed_audio_data = f.read()
        compressed_audio_base64 = base64.b64encode(compressed_audio_data).decode('utf-8')

    # 删除临时文件
    os.remove(temp_audio_file)
    os.remove(compressed_audio_file)

    audio_size = len(base64.b64decode(compressed_audio_base64))  # 计算压缩后音频数据大小

    if audio_size < 3 * 1024 * 1024:  # 小于3MB
        secret_id, secret_key = get_settings().TC_ASR_SECRET_ID, get_settings().TC_ASR_SECRET_KEY
        recognizer = SentenceRecognizer(secret_id, secret_key) 
        recognition_result = recognizer.recognize_audio(compressed_audio_base64)  # 使用压缩后的音频数据
        await websocket.send_json({
            "type": "voice_recognition",
            "content": recognition_result
        })
    else:  # 大于3MB
        appid, secret_key = get_settings().xf_APPID, get_settings().xf_ASR_SECRET_KEY
        recognizer = IfasrRecognizer(appid, secret_key, compressed_audio_base64)  # 使用压缩后的音频数据
        recognition_result = recognizer.get_result()  # 直接获取清洗后的结果

        txt_file_path = f"{uuid.uuid4()}.txt"
        with open(txt_file_path, 'w') as f:
            f.write(recognition_result)  # 保存识别结果到txt文件
        # 调用上传文件到数据库的函数（等待实现）
        # await upload_file_to_database(txt_file_path, database_id, user, manage_db)  # 替换为实际参数
        
        # 删除txt文件
        os.remove(txt_file_path)  # 删除保存的txt文件
