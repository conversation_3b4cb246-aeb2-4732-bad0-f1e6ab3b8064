# http语音识别方式暂时保留

from fastapi import APIRouter, UploadFile, File, Form, Depends, BackgroundTasks
from ..utils.asr import (
    SentenceRecognizer,
    IfasrRecognizer,
)
import uuid
import os  # 导入 os 模块
from ..config import get_settings  # 导入 get_settings 函数
from ..routes.file_route import upload_file_to_database  # 导入上传文件的函数
from ..models.user import User
from ..database import get_database, get_chat_database
from typing import Optional
from ..utils.auth import get_current_user
from ..utils.file_handler import download_file

router = APIRouter()

@router.post("/sentence", description="处理句子识别")
async def recognize_sentence(
    input_audio_file: UploadFile = File(...),
    current_user: User = Depends(get_current_user)
):
    # 语音识别
    secret_id, secret_key = get_settings().TC_ASR_SECRET_ID, get_settings().TC_ASR_SECRET_KEY
    recognizer = SentenceRecognizer(secret_id, secret_key)
    
    try:
        # 直接传递 UploadFile 对象的文件内容
        recognition_result = recognizer.recognize_audio(input_audio_file.file)

        if not recognition_result:
            return {"message": "未能识别任何内容，请检查音频文件。", "result": None}
        
        return {"message": "句子识别完成", "result": recognition_result}  # 直接返回识别结果

    except Exception as e:
        return {"message": f"识别过程中发生错误: {str(e)}", "result": None}

@router.post("/rec_file", description="处理录音文件识别")
async def recognize_rec_file(
    background_tasks: BackgroundTasks,
    input_audio_file: UploadFile = File(...),
    database_id: Optional[str] = Form(...),
    current_user: User = Depends(get_current_user),
    db = Depends(get_database)
):
    # 生成唯一的文件名
    unique_id = str(uuid.uuid4())
    txt_output_file = f"{unique_id}_recognition_result.txt"

    # 2. 语音识别
    appid, secret_key = get_settings().xf_APPID, get_settings().xf_ASR_SECRET_KEY
    recognizer = IfasrRecognizer(appid, secret_key, input_audio_file.file)
    recognition_result = recognizer.get_result()  # 直接获取清洗后的结果

    # 将识别结果保存到 TXT 文件
    with open(txt_output_file, 'w') as f:
        f.write(recognition_result)

    # 调用上传文件的函数，将 TXT 文件上传至云端
    with open(txt_output_file, 'rb') as file:
        await upload_file_to_database(
            background_tasks=background_tasks,
            file=UploadFile(filename=txt_output_file, file=file),  # 传递 UploadFile 对象
            database_id=database_id,
            current_user=current_user,
            db=db
        )
    
    # 删除生成的 TXT 文件
    os.remove(txt_output_file)

    return {"message": "录音文件识别完成", "result": recognition_result}

# 新增接口：根据消息ID获取翻译内容
@router.post("/get_transfer", description="根据消息ID获取识别内容")
async def get_transfer_by_message_id(
    message_id: Optional[str] = Form(...),
    group_id: Optional[str] = Form(...),
    chat_db = Depends(get_chat_database),
    manage_db = Depends(get_database),
    current_user: User = Depends(get_current_user)
):
    try:
        # 检查用户是否为群组成员
        group = await manage_db.groups.find_one({
            "id": group_id,
            "is_dissolved": False
        })
        
        if not group:
            return {"message": "群组不存在或已解散", "result": None}
            
        if current_user.id not in group["member_ids"]:
            return {"message": "您不在该群组中", "result": None}

        # 根据群ID获取群聊天记录集合
        group_collection = getattr(chat_db, group_id)
        
        # 从群聊天记录中查找对应的消息
        message_record = await group_collection.find_one({"id": message_id})
        
        if not message_record:
            return {"message": "未找到对应的消息记录", "result": None}
        
        # 检查transfer字段
        transfer_content = message_record.get("transfer")
        
        if transfer_content is not None:
            return {"message": "识别内容获取成功", "result": transfer_content}
        
        else:
            # 如果识别结果为空，检查content字段
            content_id = message_record.get("content")

            if content_id is None:
                return {"message": "未找到对应的音频文件记录", "result": None}

            # 从manage_db的files目录下找到对应的项
            audio_file_record = await manage_db.files.find_one({"id": content_id})
            
            if not audio_file_record:
                return {"message": "未找到对应的音频文件记录", "result": None}
            
            # 获取文件路径和文件名
            audio_file_path = audio_file_record.get("file_path")
            file_name = audio_file_record.get("file_name")
            
            # 将文件名添加到路径末尾
            full_audio_file_path = f"{audio_file_path}/{file_name}"
            
            # 下载音频文件
            local_audio_file_path = await download_file(full_audio_file_path)
            
            # 判断文件大小
            file_size = os.path.getsize(local_audio_file_path)  # 获取文件大小（字节）
            
            # 使用不同的识别器进行识别
            if file_size < 3 * 1024 * 1024:  # 小于3MB
                secret_id, secret_key = get_settings().TC_ASR_SECRET_ID, get_settings().TC_ASR_SECRET_KEY
                recognizer = SentenceRecognizer(secret_id, secret_key)
                recognition_result = recognizer.recognize_audio(local_audio_file_path)
            else:  # 大于等于3MB
                appid, secret_key = get_settings().xf_APPID, get_settings().xf_ASR_SECRET_KEY
                recognizer = IfasrRecognizer(appid, secret_key, local_audio_file_path)
                recognition_result = recognizer.get_result()  # 直接获取清洗后的结果
            
            # 将识别结果存入chat_db的transfer字段
            await group_collection.update_one(
                {"id": message_id},
                {"$set": {"transfer": recognition_result}}
            )
            
            # 删除下载的音频文件
            if os.path.exists(local_audio_file_path):
                os.remove(local_audio_file_path)
            
            return {"message": "识别内容获取成功", "result": recognition_result}
        
    except Exception as e:
        return {"message": f"获取识别内容时发生错误: {str(e)}", "result": None}
