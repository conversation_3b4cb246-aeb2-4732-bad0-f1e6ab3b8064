from functools import lru_cache

from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    DATABASE_URL: str
    SECRET_KEY: str
    MAIL_USERNAME: str
    MAIL_PASSWORD: str
    MAIL_FROM: str
    MAIL_PORT: int
    MAIL_SERVER: str
    MILVUS_HOST: str
    MILVUS_PORT: int
    OSS_endpoint: str
    OSS_key: str
    OSS_secret: str
    OSS_bucket_name: str
    HWC_BUCKET_NAME: str
    HWC_ID: str
    HWC_KEY: str
    HWC_BUCKET_ENDPOINT: str
    HWC_LLM_ENDPOINT: str
    HWC_LLM_API_KEY: str
    MODEL_root_dir: str
    DOCUMENT_SAVE_PATH: str
    # 哔哩哔哩接口密钥
    BILIBILI_SESSDATA: str
    BILIBILI_BILI_JCT: str
    BILIBILI_BUVID3: str

    # 模型密钥
    ALITONGYI_SK: str
    ALITONGYI_URL: str
    ALITONGYI_MODEL_NAME: str
    ALITONGYI_VL_MODEL_NAME: str
    ALITONGYI_LONG_MODEL_NAME: str
    ALITONGYI_WANX_T2I_MODEL_NAME: str
    ALITONGYI_WANX_T2V_MODEL_NAME: str
    ZHIPU_SK: str
    HUNYUAN_SK: str
    QIANFAN_AK: str
    QIANFAN_SK: str
    DEEPSEEK_KEY: str
    DEEPSEEK_URL: str
    DEEPSEEK_MODEL_NAME: str
    GEMINI_KEY: str
    GEMINI_URL: str
    GEMINI_MODEL_NAME: str
    MAX_FILE_SIZE: int
    ALLOWED_EXTENSIONS: list
    TEST_TOKEN: str
    HEAD_COMPRESS_RATIO: float
    TIKA_SERVER: str
    EMBEDDING_MODEL_URL: str
    EMBEDDING_MODEL_NAME: str
    EMBEDDING_MODEL_KEY: str
    DEFAULT_GROUP_ASSISTANT_NAME: str
    DEFAULT_GROUP_NAME: str
    DEFAULT_PRODUCT_NAME: str
    DEFAULT_PRODUCT_LANG_LIST: str
    WAKE_WORD: str
    LOCAL_MODEL_URL: str
    LOCAL_MODEL_NAME: str
    LOCAL_MODEL_KEY: str
    RERANKER_MODEL_URL: str
    RERANKER_MODEL_NAME: str
    RERANKER_MODEL_KEY: str
    NEO4J_URL: str
    NEO4J_USERNAME: str
    NEO4J_PASSWORD: str
    WEB_API_URL: str
    WEB_API_KEY: str
    TC_SECRET_ID: str
    TC_SECRET_KEY: str
    TC_SmsSdkAppId: str
    TC_SmsSignName: str
    TC_SmsTemplateId: str
    SECRET_ENCRYPTION_KEY: str
    SECRET_IV: str
    TTS_MODEL_URL: str
    OPENAPI_DOCS: bool
    xf_APPID: str
    xf_ASR_SECRET_KEY: str
    SUPPORTED_UNSTRUCTURED_FILE_TYPE: list
    EN_WEB_API_KEY: str
    HTTP_PROXY: str
    HTTPS_PROXY: str

    class Config:
        env_file = ".env"


@lru_cache()
def get_settings():
    return Settings()


settings = get_settings()
