import asyncio
import os
from io import BytesIO, StringIO
import json

import pandas as pd
from langchain_core.documents import Document

from ..database import get_database
from ..models.user import User
from ..utils.milvusdb import delete_items_by_doc_uuid
from .asr import VoiceTranscriber
from .file_handler import (
    delete_file,
    identify_and_load,
    identify_and_load_unstructured_doc,
    save_file,
)
from .llm import generate_faq, get_FAQ_from_llm

# from .graphbase import AsyncGraphDatabase
from .log import logger
from .milvusdb import store_embeddings_in_milvus
from ..utils.websocket_manager import connection_dic

# from .remote_model import system_graphbase_manager
# from .to_jsonl import csv_to_jsonl, excel_to_jsonl, text_to_jsonl


async def save_file_to_database(
    content: bytes,
    file_header: dict,
    current_user: User,
    doc_uuid: str,
    database: dict,
    _:None
) -> None:
    try:
        manage_db = await get_database()
        # 更新状态[上传中]..
        await manage_db.files.update_one(
            {"id": doc_uuid},
            {"$set": {"vector_status": "uploading"}},
        )
        # 无论是什么类型的文件，先上传到云储存
        file_path = await save_file(
            content,
            current_user,
            doc_uuid,
            manage_db,
            file_header,
            database["id"],
        )
        # 初始化图谱数据库
        database_id = database["id"]
        # if not await system_graphbase_manager.check_graph_database_exists(database_id):
        #     await system_graphbase_manager.create_graph_database(database_id)
        # graphbase = AsyncGraphDatabase(database_id)

        # 音频文件对识别的txt进行向量化
        if file_header["content_type"].startswith("audio/"):
            # 更新状态[解析中]..
            await manage_db.files.update_one(
                {"id": doc_uuid},
                {"$set": {"vector_status": "parsing"}},
            )
            # 识别结果作为txt文件内容
            transcriber = VoiceTranscriber()
            recognized_text = await asyncio.to_thread(
                transcriber.transcribe_audio, content
            )

            # 使用音频文件名作为txt文件名
            txt_file_name = f"{os.path.splitext(file_header['filename'])[0]}.txt"
            txt_header = {
                "filename": txt_file_name,
                "content_type": "text/plain",
                "size": len(recognized_text),
            }
            # 将文本内容编码为字节
            txt_file_content = recognized_text.encode("utf-8")
            # 保存txt文件到储存桶
            txt_file_path = await save_file(
                txt_file_content,
                current_user,
                doc_uuid,
                manage_db,
                txt_header,
                database["id"],
            )
            # 向量化txt文件
            chunks = await identify_and_load_unstructured_doc(txt_file_path)
            # 更新状态[写入中]..
            await manage_db.files.update_one(
                {"id": doc_uuid},
                {"$set": {"vector_status": "writing"}},
            )
            await store_embeddings_in_milvus(
                database["v_name"],
                chunks,
                doc_uuid,
                file_name=os.path.basename(txt_file_path),
            )
            # 生成FAQ逻辑
            logger.debug(f"音频文件 {doc_uuid} 开始生成FAQ")
            FAQ, cut_off = await get_FAQ_from_llm(
                "".join([content.page_content for content in chunks]),
                _
            )
            logger.debug(
                f'文件 {doc_uuid} 生成FAQ结束,{"由于文档内容过长，切割了部分内容" if cut_off is True else ""}'
            )
            if not FAQ:
                raise Exception("生成FAQ失败")
            # if cut_off:
            #    logger.debug(f"音频文件 {file_header['filename']} 生成FAQ时内容过长，已截断")
            # 将FAQ写入mongo
            await manage_db.files.update_one(
                {"id": doc_uuid},
                {"$set": {"faq_set": FAQ}},
            )
            # 提取关系三元组至jsonl
            # jsonl_content = await text_to_jsonl(chunks, file_header["filename"],doc_uuid)
            # # 再将jsonl传入neo4j
            # await graphbase.jsonl_file_add_entity(jsonl_content, doc_uuid)

        # # 对于excel和csv文件，转化为jsonl格式后再向量化
        # elif file_header["content_type"] in [
        #     "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        #     "application/vnd.ms-excel",
        #     "text/csv",
        # ]:
        #     # 转化为jsonl格式
        #     if file_header["content_type"] == "text/csv":
        #         # 若文件类型为csvx
        #         jsonl_content = await csv_to_jsonl(content, file_header["filename"])
        #     else:
        #         # 若文件类型为excel
        #         jsonl_content = await excel_to_jsonl(content, file_header["filename"])

        #     # 再将jsonl传入neo4j
        #     await graphbase.jsonl_file_add_entity(jsonl_content, doc_uuid)
        # 对于excel和csv文件，转化为jsonl格式后再向量化
        elif file_header["content_type"] in [
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "application/vnd.ms-excel",
            "text/csv",
        ]:
            try:
                if file_header["content_type"] == "text/csv":
                    # 使用 pandas 解析 CSV 文件
                    encodings = ["utf-8", "gbk", "ansi", "latin1"]
                    decoded_content = None
                    for encoding in encodings:
                        try:
                            decoded_content = content.decode(encoding)
                            break
                        except UnicodeDecodeError:
                            continue
                    if decoded_content is None:
                        raise Exception("无法解码 CSV 文件内容")
                    df = pd.read_csv(StringIO(decoded_content))
                    jsonl_content = df.to_json(
                        orient="records", force_ascii=False, lines=True
                    )
                else:
                    # 使用 pandas 解析 Excel 文件
                    df = pd.read_excel(BytesIO(content))
                    jsonl_content = df.to_json(
                        orient="records", force_ascii=False, lines=True
                    )

                # 再将 jsonl 传入 neo4j
                # await graphbase.jsonl_file_add_entity(jsonl_content, doc_uuid)

                # 向量化 jsonl 内容
                chunks = [
                    Document(  # 改为使用 Document 对象
                        page_content=line, metadata={}  # 添加空元数据
                    )
                    for line in jsonl_content.split("\n")
                    if line
                ]
                # 更新状态[写入中]..
                await manage_db.files.update_one(
                    {"id": doc_uuid},
                    {"$set": {"vector_status": "writing"}},
                )
                await store_embeddings_in_milvus(
                    database["v_name"],
                    chunks,
                    doc_uuid,
                    file_name=os.path.basename(file_path),
                )
            except Exception as e:
                logger.error(f"解析 {file_header['filename']} 失败: {str(e)}")
                # 更新状态[失败]..
                await manage_db.files.update_one(
                    {"id": doc_uuid},
                    {"$set": {"vector_status": "failed"}},
                )
                return

            # 生成FAQ逻辑
            logger.debug(f"文件 {doc_uuid} 开始生成FAQ")
            FAQ, cut_off = await get_FAQ_from_llm(
                "".join([content.page_content for content in chunks]),
                _
            )
            logger.debug(
                f'文件 {doc_uuid} 生成FAQ结束,{"由于文档内容过长，切割了部分内容" if cut_off is True else ""}'
            )
            # if cut_off:
            #     logger.debug(f"文件 {file_header['filename']} 生成FAQ时内容过长，已截断")
            # 将FAQ写入mongo
            await manage_db.files.update_one(
                {"id": doc_uuid},
                {"$set": {"faq_set": FAQ}},
            )
            # # 提取关系三元组至jsonl
            # jsonl_content = await text_to_jsonl(chunks, file_header["filename"], doc_uuid)
            # # 再将jsonl传入neo4j
            # await graphbase.jsonl_file_add_entity(jsonl_content, doc_uuid)

        # 对于其他文件类型，直接向量化，并转为jsonl放入neo4j
        else:
            # 更新状态[解析中]..
            await manage_db.files.update_one(
                {"id": doc_uuid},
                {"$set": {"vector_status": "parsing"}},
            )
            chunks = await identify_and_load_unstructured_doc(file_path)
            # 更新状态[写入中]..
            await manage_db.files.update_one(
                {"id": doc_uuid},
                {"$set": {"vector_status": "writing"}},
            )
            await store_embeddings_in_milvus(
                database["v_name"],
                chunks,
                doc_uuid,
                file_name=os.path.basename(file_path),
            )
            # 生成FAQ逻辑
            logger.debug(f"文件 {doc_uuid} 开始生成FAQ")
            FAQ, cut_off = await get_FAQ_from_llm(
                "".join([content.page_content for content in chunks]),
                _
            )
            logger.debug(
                f'文件 {doc_uuid} 生成FAQ结束,{"由于文档内容过长，切割了部分内容" if cut_off is True else ""}'
            )
            if not FAQ:
                raise Exception("生成FAQ失败")
            # if cut_off:
            #     logger.debug(f"文件 {file_header['filename']} 生成FAQ时内容过长，已截断")
            # 将FAQ写入mongo
            await manage_db.files.update_one(
                {"id": doc_uuid},
                {"$set": {"faq_set": FAQ}},
            )
            # # 提取关系三元组至jsonl
            # jsonl_content = await text_to_jsonl(chunks, file_header["filename"], doc_uuid)
            # # 再将jsonl传入neo4j
            # await graphbase.jsonl_file_add_entity(jsonl_content, doc_uuid)
        # 更新状态[成功]..
        await manage_db.files.update_one(
            {"id": doc_uuid},
            {"$set": {"vector_status": "success"}},
        )
        await connection_dic.broadcast([current_user.id],json.dumps({"type":"info","content":_("{}文档已上传到{}").format(file_header['filename'],database['name'])}))

    except Exception as e:
        # 更新状态[失败]..
        await manage_db.files.update_one(
            {"id": doc_uuid},
            {"$set": {"vector_status": "failed"}},
        )
        logger.error(f"{doc_uuid} 保存文档到知识库业务函数出错: {str(e)}")
        await connection_dic.broadcast([current_user.id],json.dumps({"type":"alert","content":_("{}文档上传到{}失败").format(file_header['filename'],database['name'])}))

    finally:
        # 关闭neo4j数据库连接
        # await graphbase.close()
        pass


async def delete_file_from_database(
    file_id: str,
    database: dict,
    current_user: User,
    file_size: int,
    delete_database: bool = False,
):
    """
    # 删除数据库文件
    :param file_id: 文件id
    :param database: 数据库信息
    :param current_user: 当前用户
    :param file_size: 文件大小
    :param delete_database: 默认为false，当删除数据库启动时，不从数据库中删除文件，而是等待后续直接删除数据库
    :return:
    """
    try:
        manage_db = await get_database()

        # 删除储存桶文件
        await delete_file(file_id, current_user, file_size)

        # 在删除单个文件时,删除向量库中文件,否则等到删除数据库时一并删除
        if not delete_database:
            # 删除milvus数据库内容
            v_name = database.get("v_name")
            await delete_items_by_doc_uuid(v_name, file_id)

            # # 删除neo4j数据库内容
            # if await system_graphbase_manager.check_graph_database_exists(
            #     database.get("id")
            # ):
            #     kgdb = None  # 声明变量用于finally访问
            #     try:
            #         kgdb = AsyncGraphDatabase(database.get("id"))
            #         await kgdb.delete_entity(file_id)

            #         # 判空删除逻辑
            #         # if await kgdb.is_database_empty():
            #         #     logger.info(f"检测到数据库 {database['id']} 已空，执行删除操作")
            #         #     await system_graphbase_manager.delete_graph_database(
            #         #         database["id"]
            #         #     )

            #     finally:
            #         if kgdb:
            #             await kgdb.close()  # 关闭连接

        # 把file中的is_deleted设置为True
        await manage_db.files.update_one(
            {"id": file_id}, {"$set": {"is_deleted": True}}
        )

    except Exception as e:
        logger.error(f"删除数据库文件失败: {str(e)}")
        return False
