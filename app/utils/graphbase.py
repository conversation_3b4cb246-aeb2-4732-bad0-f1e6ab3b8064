import asyncio
import json
import traceback
import warnings

from neo4j import AsyncGraphDatabase as AGD

from ..config import settings
from .log import logger
from .remote_model import embedding_model, reranker_model

warnings.filterwarnings("ignore", category=UserWarning)


UIE_MODEL = None


class AsyncGraphDatabase:
    def __init__(self, database_id):
        self.status = "closed"
        self.kgdb_name = f"kgdb{database_id.replace('-', '')}"
        self.embed_model_name = settings.EMBEDDING_MODEL_NAME

        self.driver = AGD.driver(
            f"{settings.NEO4J_URL}",
            auth=(settings.NEO4J_USERNAME, settings.NEO4J_PASSWORD),
            database=self.kgdb_name,
        )
        logger.info(f"Connecting to Neo4j {self.kgdb_name}")
        self.status = "open"

    async def close(self):
        """关闭数据库连接"""
        await self.driver.close()
        logger.info(f"Closed connection to Neo4j {self.kgdb_name}")

    async def query_by_pattern(self, pattern_triples, top_k=5):
        """通过图结构模式检索子图
        
        Args:
            pattern_triples (list): 模式三元组列表，每个元素为dict包含h,r,t键
                                 其中可以包含具体实体名或用?代表通配符
            top_k (int): 返回的最大匹配子图数量
            
        Returns:
            list: 匹配的子图信息列表
        """
        async def _build_match_query(pattern):
            """构建匹配查询语句"""
            match_clauses = []
            where_clauses = []
            return_items = []
            
            for i, triple in enumerate(pattern):
                # 构建节点和关系的变量名
                h_var = f"h{i}"
                t_var = f"t{i}"
                r_var = f"r{i}"
                
                # 构建MATCH子句
                match_clause = f"({h_var}:Entity)"
                match_clause += f"-[{r_var}:RELATION]->"
                match_clause += f"({t_var}:Entity)"
                match_clauses.append(match_clause)
                
                # 添加具体实体的WHERE条件
                if triple['h'] != '?':
                    where_clauses.append(f"{h_var}.name = '{triple['h']}'")
                if triple['t'] != '?':
                    where_clauses.append(f"{t_var}.name = '{triple['t']}'")
                if triple['r'] != '?':
                    where_clauses.append(f"{r_var}.type = '{triple['r']}'")
                    
                # 收集返回项
                return_items.extend([h_var, r_var, t_var])
            
            # 组装完整查询
            query = "MATCH " + ", ".join(match_clauses)
            if where_clauses:
                query += " WHERE " + " AND ".join(where_clauses)
            query += " RETURN " + ", ".join(return_items)
            query += f" LIMIT {top_k}"
            
            return query

        async def execute_pattern_query(tx, query):
            """执行模式查询"""
            result = await tx.run(query)
            return [record.values() async for record in result]

        try:
            async with self.driver.session() as session:
                # 构建并执行查询
                query = await _build_match_query(pattern_triples)
                results = await session.execute_read(execute_pattern_query, query)
                
                # 处理结果为更易理解的格式
                structured_results = []
                for result in results:
                    subgraph = []
                    # 每三个值组成一个三元组(头实体,关系,尾实体)
                    for i in range(0, len(result), 3):
                        head = result[i]
                        relation = result[i+1]
                        tail = result[i+2]
                        subgraph.append({
                            'h': head['name'],
                            'r': relation['type'],
                            't': tail['name']
                        })
                    structured_results.append(subgraph)
                
                return structured_results
                
        except Exception as e:
            logger.error(f"图结构检索失败: {str(e)}")
            return []

    async def txt_add_vector_entity(self, triples, doc_uuid):
        """添加实体三元组"""

        async def _index_exists(tx, index_name):
            """检查索引是否存在"""
            result = await tx.run("SHOW INDEXES")
            async for record in result:
                if record["name"] == index_name:
                    return True
            return False

        async def _create_graph(tx, data, doc_uuid):
            """添加一个三元组"""
            for entry in data:
                await tx.run(
                    """
                    MERGE (h:Entity {name: $h, doc_uuid: $doc_uuid})
                    MERGE (t:Entity {name: $t, doc_uuid: $doc_uuid})
                    MERGE (h)-[r:RELATION {type: $r, doc_uuid: $doc_uuid, doc_sequence:$doc_sequence}]->(t)
                    """,
                    h=entry["h"],
                    t=entry["t"],
                    r=entry["r"],
                    doc_uuid=doc_uuid,
                    doc_sequence = entry['sequence']
                )

        async def _create_vector_index(tx, dim):
            """创建节点和关系的向量索引"""
            # 节点索引
            node_index = "entityEmbeddings"
            if not await _index_exists(tx, node_index):
                await tx.run(
                    f"""CREATE VECTOR INDEX {node_index}
                    FOR (n: Entity) ON (n.embedding)
                    OPTIONS {{indexConfig: {{
                        `vector.dimensions`: {dim},
                        `vector.similarity_function`: 'cosine'
                    }}}}"""
                )

            # 新增关系类型索引
            rel_index = "relationTypeEmbeddings"
            if not await _index_exists(tx, rel_index):
                await tx.run(
                    f"""CREATE VECTOR INDEX {rel_index}
                    FOR ()-[r:RELATION]-() ON (r.embedding)
                    OPTIONS {{indexConfig: {{
                        `vector.dimensions`: {dim},
                        `vector.similarity_function`: 'cosine'
                    }}}}"""
                )

        async with self.driver.session() as session:
            logger.info(f"Adding entity to {self.kgdb_name}")
            await session.execute_write(_create_graph, triples, doc_uuid)
            logger.info(
                f"Creating vector index for {self.kgdb_name} with {self.embed_model_name}"
            )
            await session.execute_write(
                _create_vector_index, 768
            )  # TODO: 根据嵌入模型配置确定维度数

            semaphore = asyncio.Semaphore(20)  # 并发数限制
            write_lock = asyncio.Lock()  # 写入临界锁

            # 这是一个计数器和锁，用于跟踪进度，仅在测试环境中使用
            # total_entries = len(triples)
            # counter = [0]  # 使用列表包装计数器以绕过nonlocal限制
            # progress_lock = asyncio.Lock()  # 进度计数器锁

            async def process_entry(entry):
                async with semaphore:
                    # 并行获取嵌入向量
                    embedding_h, embedding_t, embedding_r = await asyncio.gather(
                        self.get_embedding(entry["h"]),
                        self.get_embedding(entry["t"]),
                        self.get_embedding(entry["r"]),
                    )
                    async with write_lock:
                        await session.execute_write(
                            self.set_node_embedding,
                            entry["h"],
                            embedding_h,
                        )
                        await session.execute_write(
                            self.set_node_embedding,
                            entry["t"],
                            embedding_t,
                        )
                        await session.execute_write(
                            self.set_relation_embedding,
                            entry["h"],
                            entry["t"],
                            entry["r"],
                            embedding_r,
                            doc_uuid,
                            entry['sequence']
                        )

                    # 更新进度计数器（仅在测试环境开启）
                    # async with progress_lock:
                    #     counter[0] += 1
                    #     current = counter[0]
                    #     logger.info(
                    #         f"实体插入进度: {current}/{total_entries} ({current/total_entries:.0%})"
                    #     )

            # 创建并执行所有任务
            tasks = [process_entry(entry) for entry in triples]
            await asyncio.gather(*tasks)

    async def jsonl_file_add_entity(self, jsonl_content, doc_uuid):
        """添加JSONL内容中的实体"""
        self.status = "processing"
        logger.info(f"Start adding entity to {self.kgdb_name}")

        # 解析JSONL内容
        triples = [
            json.loads(line.strip())
            for line in jsonl_content.split("\n")
            if line.strip()
        ]

        if not triples:
            logger.warning("传入的JSONL内容为空")
            return self.kgdb_name

        await self.txt_add_vector_entity(triples, doc_uuid)

        self.status = "open"
        return self.kgdb_name

    async def delete_entity(self, doc_uuid):
        """删除数据库中的指定实体三元组, 参数entity_name为空则删除全部实体"""
        async with self.driver.session() as session:
            await session.execute_write(self._delete_by_doc_uuid, doc_uuid)

    async def _delete_by_doc_uuid(self, tx, doc_uuid):
        """根据doc_uuid删除所有相关节点和关系"""
        query = """
        MATCH (n:Entity {doc_uuid: $doc_uuid})-[r:RELATION {doc_uuid: $doc_uuid}]-()
        DETACH DELETE n
        """
        await tx.run(query, doc_uuid=doc_uuid)

    async def query_by_vector(self, question, hops=2, num_of_res=5):
        """查询与指定实体最相似的实体信息"""

        async def _index_exists(tx, index_name):
            """检查索引是否存在"""
            result = await tx.run("SHOW INDEXES")
            async for record in result:
                if record["name"] == index_name:
                    return True
            return False

        async def query_node(tx, text):
            # 首先检查索引是否存在
            if not await _index_exists(tx, "entityEmbeddings"):
                raise Exception("节点向量索引不存在，请先创建索引")

            embedding = await self.get_embedding(text)
            result = await tx.run(
                """
                CALL db.index.vector.queryNodes('entityEmbeddings', 10, $embedding)
                YIELD node AS similarEntity, score
                RETURN similarEntity.name AS name, score
                """,
                embedding=embedding,
            )
            return [record.values() async for record in result]

        async def query_relations(tx, text):
            """关系向量查询"""
            # 检查关系类型索引是否存在
            if not await _index_exists(tx, "relationTypeEmbeddings"):
                raise Exception("关系类型索引不存在，请先创建索引")

            rel_embedding = await self.get_embedding(text)
            result = await tx.run(
                """
                CALL db.index.vector.queryRelationships('relationTypeEmbeddings', 10, $embedding)
                YIELD relationship AS rel, score
                WITH rel, score
                MATCH (h)-[rel]->(t)
                RETURN h.name AS head, rel.type AS relation, t.name AS tail, score
                """,
                embedding=rel_embedding,
            )
            return [record.values() async for record in result]

        try:
            async with self.driver.session() as session:
                results = await session.execute_read(query_node, question)
        except Exception as e:
            if "向量索引不存在" in str(e):
                logger.error(
                    f"向量索引不存在，请先创建索引: {e}, {traceback.format_exc()}"
                )
                return []
            raise e
        # 筛选出分数高于阈值的实体
        # 先取前10个最高分实体
        top_entities = [
            result[0]
            for result in sorted(results, key=lambda x: x[1], reverse=True)[
                :10
            ]  # 取前10个最高分
        ]

        # 使用重排模型进行二次排序
        if top_entities:
            scores = await self.get_rerank_scores(question, top_entities)
            # 组合分数并排序
            reranked = sorted(
                zip(top_entities, scores), key=lambda x: x[1], reverse=True
            )[
                :num_of_res
            ]  # 取前N个重排结果
            qualified_entities = [entity for entity, _ in reranked]
        else:
            qualified_entities = []

        # 对每个合格的实体进行查询
        all_query_results = []
        for entity in qualified_entities:
            query_result = await self.query_specific_entity(
                entity_name=entity, hops=hops
            )
            all_query_results.extend(query_result)

        # 新增关系查询部分
        try:
            async with self.driver.session() as session:
                rel_results = await session.execute_read(query_relations, question)
        except Exception as e:
            if "关系类型索引不存在" in str(e):
                logger.error(f"关系索引不存在: {e}")
                rel_results = []
            else:
                raise e

        # 处理关系结果
        if rel_results:
            # 构建关系描述用于重排
            rel_descriptions = [f"{h} - {r} -> {t}" for h, r, t, _ in rel_results]
            rel_scores = await self.get_rerank_scores(question, rel_descriptions)

            # 组合关系分数
            combined_scores = [
                (rel_entry, rel_score)
                for rel_entry, rel_score in zip(rel_results, rel_scores)
            ]

            # 取前3个最佳关系
            top_relations = sorted(combined_scores, key=lambda x: x[1], reverse=True)[
                :3
            ]

            # 查询关系两端节点的子图
            for rel_entry in top_relations:
                h, r, t, _ = rel_entry[0]
                # 查询头节点和尾节点的子图（hops-1保证整体深度）
                for node in [h, t]:
                    query_result = await self.query_specific_entity(
                        entity_name=node, hops=max(hops - 1, 1)  # 至少查询1跳
                    )
                    all_query_results.extend(query_result)

        return all_query_results

    async def query_specific_entity(self, entity_name, hops=2):
        """查询指定实体三元组信息（无向关系）"""

        async def query(tx, entity_name, hops):
            result = await tx.run(
                f"""
                MATCH (n {{name: $entity_name}})-[r*1..{hops}]-(m)
                RETURN n, r, m
                """,
                entity_name=entity_name,
            )
            return [record.values() async for record in result]

        async with self.driver.session() as session:
            return await session.execute_read(query, entity_name, hops)

    async def get_embedding(self, text):
        try:
            # 使用嵌入模型生成嵌入向量
            outputs = await asyncio.to_thread(embedding_model.embed_query, text)
            return outputs
        except Exception as e:
            logger.error(f"生成嵌入向量时出错: {e}")
            return []

    async def set_node_embedding(self, tx, entity_name, embedding):
        await tx.run(
            """
            MATCH (e:Entity {name: $name})
            CALL db.create.setNodeVectorProperty(e, 'embedding', $embedding)
            """,
            name=entity_name,
            embedding=embedding,
        )

    async def get_rerank_scores(self, question, entities):
        """使用重排模型计算实体相关性分数"""
        try:
            return await asyncio.to_thread(
                reranker_model.compute_score, question, entities
            )
        except Exception as e:
            logger.error(f"重排模型计算分数失败: {e}")
            return []

    async def set_relation_embedding(self, tx, h, t, r, embedding, doc_uuid, doc_sequence):
        """设置关系类型嵌入"""
        await tx.run(
            """
            MATCH (h:Entity {name: $h, doc_uuid: $doc_uuid})
            MATCH (t:Entity {name: $t, doc_uuid: $doc_uuid})
            MATCH (h)-[r:RELATION {type: $r, doc_uuid: $doc_uuid, doc_sequence:$doc_sequence}]->(t)
            CALL db.create.setRelationshipVectorProperty(r, 'embedding', $embedding)
            """,
            h=h,
            t=t,
            r=r,
            embedding=embedding,
            doc_uuid=doc_uuid,
            doc_sequence=doc_sequence
        )

    async def is_database_empty(self) -> bool:
        """检查当前数据库是否为空（是否存在任何实体节点）"""

        async def check_empty(tx):
            result = await tx.run(
                "MATCH (n:Entity) RETURN count(n) AS node_count LIMIT 1"
            )
            record = await result.single()
            return record["node_count"] == 0 if record else True

        try:
            async with self.driver.session() as session:
                return await session.execute_read(check_empty)
        except Exception as e:
            logger.error(f"检查数据库状态失败: {str(e)}")
            return True  # 当出现异常时默认视为空库
