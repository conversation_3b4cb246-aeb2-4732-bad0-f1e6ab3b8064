import logging
import os
import sys
from logging.handlers import RotatingFileHandler
import warnings

# 忽略unstructured的警告 - 多层次抑制
warnings.filterwarnings("ignore", message=".*CropBox missing from /Page.*")
warnings.filterwarnings("ignore", message=".*defaulting to MediaBox.*")
warnings.filterwarnings("ignore", category=UserWarning, module=".*unstructured.*")

class PDFWarningFilter:
    """
    专门用于过滤PDF解析相关警告的上下文管理器
    """
    def __init__(self):
        self.original_stderr = None
        self.filtered_stderr = None

    def __enter__(self):
        # 保存原始stderr
        self.original_stderr = sys.stderr
        # 创建一个过滤器来捕获和过滤stderr输出
        self.filtered_stderr = FilteredStringIO(self.original_stderr)
        sys.stderr = self.filtered_stderr
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        # 恢复原始stderr
        sys.stderr = self.original_stderr
        # 忽略未使用的参数
        _ = exc_type, exc_val, exc_tb

class FilteredStringIO:
    """
    过滤特定警告信息的StringIO包装器
    """
    def __init__(self, original_stream):
        self.original_stream = original_stream
        self.pdf_warning_patterns = [
            "CropBox missing from /Page",
            "defaulting to MediaBox",
            "MediaBox missing from /Page",
            "TrimBox missing from /Page",
            "BleedBox missing from /Page"
        ]

    def write(self, text):
        # 检查是否包含需要过滤的PDF警告
        if any(pattern in text for pattern in self.pdf_warning_patterns):
            # 如果是PDF相关警告，不输出到stderr
            return
        # 否则正常输出
        self.original_stream.write(text)

    def flush(self):
        self.original_stream.flush()

    def __getattr__(self, name):
        # 代理其他方法到原始流
        return getattr(self.original_stream, name)

class Logger:
    def __init__(self, name, level=logging.INFO):
        self.logger = logging.getLogger(name)
        self.logger.setLevel(level)
        self.setup_handlers()

    def setup_handlers(self):
        # Create logs directory if it doesn't exist
        log_dir = "logs"
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)

        # File handler with rotation
        file_handler = RotatingFileHandler(
            os.path.join(log_dir, "app.log"),
            maxBytes=10 * 1024 * 1024,  # 10MB
            backupCount=5,
            encoding="utf-8",
        )

        # Console handler
        console_handler = logging.StreamHandler()

        # Format
        formatter = logging.Formatter(
            "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)

        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)

    def debug(self, message):
        self.logger.debug(message)

    def info(self, message):
        self.logger.info(message)

    def warning(self, message):
        self.logger.warning(message)

    def error(self, message, exc_info=True):
        self.logger.error(message, exc_info=exc_info)

    def critical(self, message):
        self.logger.critical(message)


# Create default logger instance
logger = Logger("DeepTalk",level=logging.DEBUG)

# 创建全局PDF警告过滤器实例
pdf_warning_filter = PDFWarningFilter()

def suppress_pdf_warnings(func):
    """
    装饰器：用于抑制PDF解析过程中的警告信息
    使用方法：
    @suppress_pdf_warnings
    def your_pdf_parsing_function():
        # PDF解析代码
        pass
    """
    def wrapper(*args, **kwargs):
        with pdf_warning_filter:
            return func(*args, **kwargs)
    return wrapper