import asyncio

from fastapi import WebSocket

from ..models.user import User
from .log import logger


class ConnectionManager:
    def __init__(self):
        self.active_connections: dict = {}
        self.lock = asyncio.Lock()

    async def connect(self, user_data: User, websocket: WebSocket):
        await websocket.accept()
        async with self.lock:
            user_id = user_data.id
            if user_id not in self.active_connections:
                self.active_connections[user_id] = [websocket]
            else:
                self.active_connections[user_id].append(websocket)

    async def disconnect(self, user_id: str, websocket: WebSocket):
        async with self.lock:
            logger.critical(f"{user_id}的{websocket}对象断开连接")
            self.active_connections[user_id].remove(websocket)

    async def send_personal_message(self, message: str, websocket: WebSocket):
        await websocket.send_text(message)

    async def broadcast(self, user_list: list, message: str):
        sended_list = []
        for user_id in user_list:
            async with self.lock:
                user_conn = self.active_connections.get(user_id, None)
                if user_conn:
                    for conn in user_conn:
                        try:
                            await conn.send_text(message)
                        except Exception as e:
                            continue
                    sended_list.append(user_id)
        return sended_list


class TopManager:
    def __init__(self):
        self.manager_dict = {}

    def add_manager(self, manager_id, manager):
        self.manager_dict[manager_id] = manager

    def get_manager(self, manager_id):
        return self.manager_dict.get(manager_id)

    def remove_manager(self, manager_id):
        if manager_id in self.manager_dict:
            del self.manager_dict[manager_id]


connection_dic = ConnectionManager()
