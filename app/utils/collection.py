import os

from ..utils.file_handler import identify_and_load, identify_and_load_unstructured_doc
from ..utils.log import logger
from ..utils.milvusdb import store_embeddings_in_milvus
from ..config import settings


async def save_collection_to_database(
    collection_id: str,
    messages: list,
    database_v_name: str,
) -> None:
    try:
        txt_file_path = f"{settings.DOCUMENT_SAVE_PATH}/{collection_id}.txt"
        with open(txt_file_path, "w", encoding="utf-8") as f:
            f.write("\n".join(messages))
        # 将txt文件内容上传到向量数据库
        chunks = await identify_and_load_unstructured_doc(txt_file_path)
        await store_embeddings_in_milvus(database_v_name, chunks, collection_id,file_name=os.path.basename(txt_file_path))
        os.remove(txt_file_path)
    except Exception as e:
        logger.error(f"保存收藏{collection_id}内容到知识库失败: {e}")
