import base64
import hashlib
import hmac
import json
import time
import urllib.parse

import requests

from ..config import settings


class VoiceTranscriber:
    def __init__(self, language="cn"):
        self.appid = settings.xf_APPID
        self.secret_key = settings.xf_ASR_SECRET_KEY
        self.lfasr_host = "https://raasr.xfyun.cn/v2/api"
        self.api_upload = "/upload"
        self.api_get_result = "/getResult"
        self.ts = str(int(time.time()))
        self.signa = self.get_signa()

    def get_signa(self):
        m2 = hashlib.md5()
        m2.update((self.appid + self.ts).encode("utf-8"))
        md5 = m2.hexdigest()
        md5 = bytes(md5, encoding="utf-8")
        signa = hmac.new(self.secret_key.encode("utf-8"), md5, hashlib.sha1).digest()
        signa = base64.b64encode(signa)
        return str(signa, "utf-8")

    def upload(self, audio_data):
        print("上传部分：")

        param_dict = {
            "appId": self.appid,
            "signa": self.signa,
            "ts": self.ts,
            "fileSize": len(audio_data),
            "fileName": "file_name",
            "duration": "200",
        }
        print("upload参数：", param_dict)

        response = requests.post(
            url=self.lfasr_host
            + self.api_upload
            + "?"
            + urllib.parse.urlencode(param_dict),
            headers={"Content-type": "application/json"},
            data=audio_data,
        )
        result = json.loads(response.text)
        print("upload resp:", result)

        return result

    def transcribe_audio(self, audio_data):
        uploadresp = self.upload(audio_data)
        orderId = uploadresp["content"]["orderId"]
        param_dict = {
            "appId": self.appid,
            "signa": self.signa,
            "ts": self.ts,
            "orderId": orderId,
            "resultType": "transfer",
        }
        print("查询部分：")
        # print("get result参数：", param_dict)
        status = 3
        attempt = 0
        base_wait_time = 5
        while status == 3:
            wait_time = base_wait_time * attempt
            time.sleep(wait_time)
            attempt += 1
            response = requests.post(
                url=self.lfasr_host
                + self.api_get_result
                + "?"
                + urllib.parse.urlencode(param_dict),
                headers={"Content-type": "application/json"},
            )
            result = json.loads(response.text)
            status = result["content"]["orderInfo"]["status"]
            print("status=", status)
            if status == 4:
                break

        # 数据清洗逻辑
        order_result = result.get("content", {}).get("orderResult", "")
        parsed_result = json.loads(order_result) if order_result else {}

        recognized_text = []
        for lattice in parsed_result.get("lattice", []):
            json_1best = json.loads(lattice.get("json_1best", "{}"))
            words = json_1best.get("st", {}).get("rt", [{}])[0].get("ws", [])
            recognized_text.extend([word["cw"][0]["w"] for word in words if word["cw"]])

        # 将识别结果拼接成字符串
        cleaned_result = "".join(recognized_text)
        print("Cleaned IfasrRecognizer Result:", cleaned_result)
        return cleaned_result  # 返回清洗后的结果
