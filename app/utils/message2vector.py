import asyncio
import json
import os
import time
import uuid
from datetime import datetime

from bson.objectid import ObjectId

from ..config import settings
from ..database import get_chat_database, get_database  # 引入数据库连接
from ..utils.file_handler import identify_and_load, identify_and_load_unstructured_doc
from ..utils.log import logger
from ..utils.milvusdb import store_embeddings_in_milvus

group_global_info = (
    {}
)  # 用于记录每个群组信息（包括对话数据库、向量数据库、最后读取位置）

async def store_messages_group_by_user(group_id, group_milvus_database, messages):
    # 把消息按sender_id分组，每个sender_id一个列表
    messages_by_user = {}
    for msg in messages:
        sender_id = msg["sender_id"]
        if sender_id not in messages_by_user:
            messages_by_user[sender_id] = []
        messages_by_user[sender_id].append(msg)
    
    # 遍历每个用户的消息
    for sender_id, user_messages in messages_by_user.items():
        # 保存所有消息的内容，准备切分和写入向量数据库
        original_file = f"{settings.DOCUMENT_SAVE_PATH}/{settings.HWC_BUCKET_NAME}/{group_id}/message-{sender_id}.txt"
        with open(original_file, "w", encoding="utf-8") as f:
            for msg in user_messages:
                f.write(f"【{msg['created_at']}】【{msg['sender_username']}】{msg['content']}\n")

        # 加载原文文件并识别文档内容 并切割
        chunks = await identify_and_load_unstructured_doc(original_file)
        # 写入向量数据库
        logger.info(f"群组：{group_id} {sender_id} 写入数据库: {group_milvus_database}, 分片数量: {len(chunks)}")
        try:
            ts = msg['created_at'].timestamp()
            await store_embeddings_in_milvus(group_milvus_database, chunks, str(uuid.uuid4()), sender_id, file_name=os.path.basename(original_file), timestamp=ts)
        except Exception as e:
            logger.error(f"群组：{group_id} {sender_id} 写入数据库: {group_milvus_database} 失败，错误信息: {e}")

async def store_messages(group_info, messages):
    # 获取群组id
    group_id = group_info["id"]

    # 生成文件名和路径
    start_time_str = messages[0]["created_at"].strftime("%Y%m%d%H%M%S")
    end_time_str = messages[-1]["created_at"].strftime("%Y%m%d%H%M%S")
    filename = f"message-{start_time_str}-{end_time_str}.json"
    filepath = f"{settings.DOCUMENT_SAVE_PATH}/{settings.HWC_BUCKET_NAME}/{group_id}"
    fullpath = f"{filepath}/{filename}"

    # 如果不存在filepath，则创建
    if not os.path.exists(filepath):
        os.makedirs(filepath)

    # 写入文件
    with open(fullpath, "w", encoding="utf-8") as f:
        for msg in messages:
            json_line = json.dumps(msg, default=str)
            f.write(json_line + "\n")  # 每行写入一个 JSON 对象
    # logger.info(f"写入文件: {fullpath}, 消息数量: {len(messages)}")

    # 获取向量数据库名称
    group_milvus_database = group_info["milvus_database_name"]

    # 按sender_id分组，写入向量数据库
    await store_messages_group_by_user(group_id, group_milvus_database, messages)

async def process_group_chat_collection(chat_database, group_info):
    # 获取群组id
    group_id = group_info["id"]
    group_chat_collection_name = group_info["chat_collection_name"]

    # 从当前集合中读取消息
    query = {}
    if "last_read_position" in group_info:
        query = {
            "_id": {"$gt": group_info["last_read_position"]}
        }  # 根据最后读取位置查询

    # 尝试获取对话数据库
    chat_collection_names = await chat_database.list_collection_names()
    if group_chat_collection_name not in chat_collection_names:
        # logger.error(f"群组: {group_id} 对话数据库不存在")
        return

    # 读取50条消息
    chat_collection = chat_database[group_chat_collection_name]
    messages = (
        await chat_collection.find(query).sort("created_at", 1).limit(50).to_list(None)
    )
    # logger.info(f"群组: {group_id}, 查询条件: {query}，消息数量: {len(messages)}")

    # 如果消息数量为0，则跳过
    if len(messages) == 0:
        return

    # 如果最早一条消息的时间与当前时间小于300秒并且消息数量小于50条则跳过
    now_time = datetime.now()
    earliest_message_time = messages[0]["created_at"]
    if len(messages) < 50 and (now_time - earliest_message_time).total_seconds() < 300:
        return

    # logger.info(f"更新最后读取位置: {group_id}, 长度：{len(messages)}，_id: {messages[-1].get('_id')}")

    # 更新最后读取位置，写入向量数据库
    group_info["last_read_position"] = messages[-1].get("_id")

    # 存入文件和 Milvus
    await store_messages(group_info, messages)  # 传递集合名称在前

async def load_group_global_info(manager_database, chat_database):
    # 清空group_global_info
    group_global_info.clear()

    # 获取所有群组信息
    group_info_list = await manager_database.groups.find(
        {"is_dissolved": False}
    ).to_list(None)

    # 获取群组对应的对话数据库和向量数据库
    for group_info in group_info_list:
        group_id = group_info["id"]

        # 初始化group_global_info
        if group_id not in group_global_info:
            group_global_info[group_id] = {
                "id": group_id,
                "name": group_info["name"],
                "chat_collection_name": group_id,
                "milvus_database_name": f"_{group_id.replace('-', '')}_chat",
            }

async def get_last_message_id_from_file(group_info):
    # 获取群组id
    group_id = group_info["id"]

    # 获取文件路径
    filepath = f"{settings.DOCUMENT_SAVE_PATH}/{settings.HWC_BUCKET_NAME}/{group_id}"
    if not os.path.exists(filepath):
        return None

    # 获取文件列表
    file_list = [f for f in os.listdir(filepath) if f.startswith("message-") and f.endswith(".json")]
    if not file_list:
        return None

    # 获取最新的文件
    latest_file = max(
        file_list,
        key=lambda x: os.path.getmtime(os.path.join(filepath, x)),
    )
    fullpath = f"{filepath}/{latest_file}"

    # 读取文件，获取最后一行，并解析为JSON对象，获取_id
    with open(fullpath, "r", encoding="utf-8") as f:
        last_line = f.readlines()[-1]  # 读取最后一行
        json_line = json.loads(last_line)  # 将最后一行解析为JSON对象
        if "_id" in json_line:
            # 根据_id创建bson.objectid.ObjectId
            bson_object_id = ObjectId(json_line["_id"])
            group_global_info[group_id]["last_read_position"] = bson_object_id

async def flush_all_collections_to_file_and_milvus():
    manager_database = await get_database()  # 获取管理数据库连接
    chat_database = await get_chat_database()  # 获取对话数据库连接

    # 加载群组信息
    await load_group_global_info(manager_database, chat_database)

    # 获取最后一条已经写入文件的消息的_id
    for group_id, group_info in group_global_info.items():
        await get_last_message_id_from_file(group_info)

    # 最后加载群组信息的时间
    last_reload_time = time.time()

    while True:
        # 遍历所有群组，处理每个群组的消息
        for group_id, group_info in group_global_info.items():
            await process_group_chat_collection(chat_database, group_info)
        
        # 每隔60秒检查一次
        await asyncio.sleep(60)

        # 每隔5分钟重新加载群组信息
        if time.time() - last_reload_time > 300:
            await load_group_global_info(manager_database, chat_database)
            # 获取最后一条已经写入文件的消息的_id
            for group_id, group_info in group_global_info.items():
                await get_last_message_id_from_file(group_info)
            last_reload_time = time.time()
