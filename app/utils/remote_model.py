import asyncio
import json
from datetime import datetime
from collections import defaultdict
from typing import List
from ..utils.log import logger

import requests
from neo4j import AsyncGraphDatabase as AGD

from ..config import settings
from ..utils.log import logger


class RemoteEmbeddings:
    def __init__(self, url: str, model: str ,key: str):
        self.url = url
        self.model = model
        self.key = key
        # 创建一个 Session 对象以复用连接
        self.session = requests.Session()
        self.no_v1_url = url.replace("/v1", "")

    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        # 批量发送请求
        response = self.session.post(
            f"{self.url}/embeddings", json={"input": texts, "model": f"{self.model}"},
            headers={"Content-Type": "application/json","Authorization":self.key},
        )
        response.raise_for_status()
        embeddings = [item["embedding"] for item in response.json()["data"]]
        return embeddings

    def embed_query(self, text: str) -> List[float]:
        # 单条文本请求
        response = self.session.post(
            f"{self.url}/embeddings", json={"input": [text], "model": f"{self.model}"},
            headers={"Content-Type": "application/json","Authorization":self.key},
        )
        response.raise_for_status()
        return response.json()["data"][0]["embedding"]

    def tokenize(self, text: str):
        # 单条文本解析为多个token
        response = self.session.post(
            f"{self.no_v1_url}/tokenize",
            json={"prompt": text, "model": f"{self.model}"},
            headers={"Content-Type": "application/json","Authorization":self.key},
        )
        response.raise_for_status()
        return response.json()


class RemoteReranker:
    def __init__(self, url: str, model: str, key: str):
        self.url = url
        self.model = model
        self.key = key
        # 创建一个 Session 对象以复用连接
        self.session = requests.Session()
        self.no_v1_url = url.replace("/v1", "")

    def compute_score(self, question: str, contexts: List[str]) -> List[float]:
        # 批量发送请求
        response = self.session.post(
            f"{self.url}",
            json={"text_1": question, "text_2": contexts, "model": f"{self.model}"},
            headers={"Content-Type": "application/json","Authorization":self.key},
        )
        response.raise_for_status()
        score_list = [item["score"] for item in response.json()["data"]]
        return score_list


class RemoteChat:
    def __init__(self, url: str, model: str,key: str):
        self.url = url
        self.model = model
        self.key = key
        # 创建一个 Session 对象以复用连接
        self.session = requests.Session()
        self.no_v1_url = url.replace("/v1", "")

    def get_excel_headers_from_llm(self, sample_text: str) -> List[str]:
        # 构造get_excel_headers_prompt
        prompt = {
            "messages": [
                {
                    "content": "你是一个专业的数据分析师，请根据提供的表格数据内容，推测并返回最合适的表头名称。"
                    "只需要返回表头列表，不需要任何解释。表头数量应与数据列数一致。",
                    "role": "system",
                },
                {
                    "content": f"请为以下表格数据推荐合适的表头名称:\n{sample_text}\n"
                    "**只返回表头列表**，例如: ['姓名', '年龄', '性别']",
                    "role": "user",
                },
            ],
            "model": self.model,
            "chat_template_kwargs": {"enable_thinking": False},
            "stream": False,
        }

        # 调用API
        response = requests.post(
            f"{self.url}/chat/completions",
            headers={"Content-Type": "application/json","Authorization":self.key},
            data=json.dumps(prompt),
        )

        if response.status_code == 200:
            try:
                result = response.json()
                # 假设API返回的答案可以直接解析为列表
                headers = eval(result["choices"][0]["message"]["content"])
                return headers
            except Exception as e:
                logger.info(f"解析API响应时出错: {e}")
                return None  # 失败时返回None
        else:
            logger.info(f"API请求失败，状态码: {response.status_code}")
            return None  # 失败时返回None
    def convert_relative_times_in_text(self, text: str) -> str:
        """
        让大模型直接处理完整文本，自动在相对时间词后添加时间范围
        
        示例输入: "统计过去3天的数据"
        示例输出: "统计过去3天(2025-05-30 21:41:12,2025-06-02 21:41:12)的数据"
        
        参数:
            text: 需要处理的原始文本
            
        返回:
            处理后的文本（相对时间词后自动添加时间范围），或None（API失败时）
        """
        prompt = {
            "messages": [
                {
                    "content": "你是一个专业的时间处理助手，请执行以下操作：\n"
                    "1. 识别文本中的所有相对时间词（如：昨天、上周、过去3个月）\n"
                    "2. 根据当前时间计算每个相对时间词的精确范围\n"
                    "3. 在每个相对时间词后添加括号，括号内写入起止时间\n"
                    "4. 保持原文其他内容不变\n\n"
                    "规则：\n"
                    "- 时间格式：YYYY-MM-DD HH:MM:SS\n"
                    "- 范围格式：开始时间~结束时间\n"
                    "- 只处理过去的时间（不要计算未来的时间范围）\n"
                    "- 模糊时间（如'早上'）按该时间段起始时刻计算"
                    "示例: \n"
                    "1. 输入:统计过去3天的数据\n"
                    "输出: 统计过去3天(2025-05-30 21:41:12,2025-06-02 21:41:12)的数据\n"
                    "2. 输入: ? \n"
                    "输出：?",
                    "role": "system",
                },
                {
                    "content": f"当前基准时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
                    f"需要处理的文本：{text}\n\n"
                    "请返回处理后的完整文本（只需返回文本本身，不要解释）",
                    "role": "user",
                },
            ],
            "model": self.model,
            "chat_template_kwargs": {"enable_thinking": False},
            "stream": False,
        }
    
        response = requests.post(
            f"{self.url}/chat/completions",
            headers={"Content-Type": "application/json","Authorization":self.key},
            data=json.dumps(prompt),
        )
    
        if response.status_code == 200:
            try:
                result = response.json()["choices"][0]["message"]["content"].strip()
                logger.info(f"文本`{text}`时间转换结果为:\n{result}")
                return result
            except Exception as e:
                logger.info(f"解析API响应时出错: {e}")
                return text
        else:
            logger.info(f"API请求失败，状态码: {response.status_code}")
            return text



class SystemGraphbaseManager:
    def __init__(self, url: str, username: str, password: str):
        # 连接到系统数据库
        self.driver = AGD.driver(
            f"{url}",
            auth=(username, password),
            database="system",
        )
        logger.info("连接到neo4j系统数据库成功")
        self.db_locks = defaultdict(asyncio.Lock)  # 每个数据库ID的独立锁

    async def create_graph_database(self, database_id):
        """创建新的数据库，如果已存在则返回已有数据库的名称"""
        async with self.db_locks[database_id]:
            kgdb_name = f"kgdb{database_id.replace('-', '')}"
            async with self.driver.session() as session:
                existing_databases = await session.run("SHOW DATABASES")
                records = await existing_databases.data()
                existing_db_names = [db["name"] for db in records]

                # 修改为严格匹配当前数据库名称
                if kgdb_name in existing_db_names:
                    logger.info(f"neo4j数据库已存在: {database_id}")
                    return kgdb_name

                # 异常处理
                try:
                    await session.run(f"CREATE DATABASE {kgdb_name}")
                    logger.info(f"neo4j数据库 '{database_id}' 创建成功")
                    return kgdb_name
                except Exception as e:
                    logger.error(f"neo4j数据库创建失败: {e}")
                    raise

    async def get_graph_database_list(self):
        """获取数据库列表"""
        async with self.driver.session() as session:
            result = await session.run("SHOW DATABASES")
            return [record["name"] for record in result]

    async def delete_graph_database(self, database_id):
        """删除数据库"""
        async with self.db_locks[database_id]:  # 获取当前database_id的锁
            kgdb_name = f"kgdb{database_id.replace('-', '')}"

            if not await self.check_graph_database_exists(database_id):
                logger.warning(f"neo4j数据库 '{database_id}' 不存在")
                return

            async with self.driver.session() as session:
                await session.run(f"DROP DATABASE {kgdb_name}")
                logger.info(f"neo4j数据库 '{database_id}' 删除成功")

    async def check_graph_database_exists(self, database_id):
        """检查数据库是否存在"""
        async with self.db_locks[database_id]:  # 获取当前database_id的锁
            kgdb_name = f"kgdb{database_id.replace('-', '')}"
            async with self.driver.session() as session:
                existing_databases = await session.run("SHOW DATABASES")
                records = await existing_databases.data()
                existing_db_names = [db["name"] for db in records]
                return kgdb_name in existing_db_names

    async def close(self):
        """关闭数据库连接"""
        await self.driver.close()
        logger.info("关闭neo4j系统数据库连接")


# 嵌入模型
embedding_model = RemoteEmbeddings(
    url=f"{settings.EMBEDDING_MODEL_URL}", model=f"{settings.EMBEDDING_MODEL_NAME}", key=f"{settings.EMBEDDING_MODEL_KEY}"
)

# 重排模型
reranker_model = RemoteReranker(
    url=f"{settings.RERANKER_MODEL_URL}", model=f"{settings.RERANKER_MODEL_NAME}", key=f"{settings.RERANKER_MODEL_KEY}"
)

# 聊天模型
chat_model = RemoteChat(
    url=f"{settings.LOCAL_MODEL_URL}", model=f"{settings.LOCAL_MODEL_NAME}",key=f"{settings.LOCAL_MODEL_KEY}"
)

# neo4j系统数据库管理
system_graphbase_manager = SystemGraphbaseManager(
    url=settings.NEO4J_URL,
    username=settings.NEO4J_USERNAME,
    password=settings.NEO4J_PASSWORD,
)
