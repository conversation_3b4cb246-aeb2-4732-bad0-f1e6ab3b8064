# from ..utils.file_handler import text_rank_summary
import re
import time

from langchain_core.documents import Document
from langchain_milvus import Milvus
from pymilvus import MilvusClient

from ..config import settings
from ..database import get_database
from ..utils.log import logger
from ..utils.time_utils import resolve_relative_time
from ..utils.remote_model import embedding_model

# 创建新的collection
async def create_collection(collection_name):
    try:
        # 初始化向量存储
        vector_store = Milvus(
            auto_id=False,  # 自动Id数据无法编辑修改
            collection_name=collection_name,
            embedding_function=embedding_model,
            connection_args={
                "host": settings.MILVUS_HOST,
                "port": settings.MILVUS_PORT,
                "uri": f"http://{settings.MILVUS_HOST}:{settings.MILVUS_PORT}",
            },
        )
        logger.info(f"Collection {collection_name} 已成功创建！")
    except Exception as e:
        logger.info(f"向量数据库创建异常: {e}")


# 删除collection
async def delete_collection(collection_name):
    try:
        client = MilvusClient(
            uri=f"http://{settings.MILVUS_HOST}:{settings.MILVUS_PORT}"
        )
        client.drop_collection(collection_name=collection_name)
    except Exception as e:
        logger.info(f"向量数据库删除异常: {e}")


# 修改collection
# async def modify_collection(collection_name, new_collection_name):
#     try:
#         client = MilvusClient(
#             uri=f"http://{settings.MILVUS_HOST}:{settings.MILVUS_PORT}"
#         )
#         client.rename_collection(
#             old_name=collection_name,
#             new_name=new_collection_name,
#         )
#     except Exception as e:
#         logger.info(f"向量数据库修改异常: {e}")


# 嵌入文档并存入 Milvus
async def store_embeddings_in_milvus(collection_name, chunks, doc_uuid, user_id=None,file_name=None, timestamp=None):
    try:
        # 初始化向量存储
        vector_store = Milvus(
            auto_id=False,  # 自动Id数据无法编辑修改
            collection_name=collection_name,
            embedding_function=embedding_model,
            connection_args={
                "host": settings.MILVUS_HOST,
                "port": settings.MILVUS_PORT,
                "uri": f"http://{settings.MILVUS_HOST}:{settings.MILVUS_PORT}",
            },
        )
        index = 0
        id_list = []
        logger.info(f"{doc_uuid}开始写入Milvus共{len(chunks)}个片段")
        for chunk in chunks:
            if not chunk.page_content:
                continue
            chunk.metadata["doc_uuid"] = doc_uuid
            chunk.metadata["timestamp"] = timestamp if timestamp != None else time.time()
            chunk.metadata["file_name"] = file_name
            if user_id != None:
                chunk.metadata["user_id"] = user_id
            id_list.append(f"{doc_uuid}_{str(index)}")
            index += 1
        if not chunks:
            logger.warning(f"{doc_uuid}没有有效的文本内容可处理")
            return
        # 将分割后的文档存入 Milvus
        await vector_store.aadd_documents(chunks, ids=id_list)
        logger.info(
            f"{doc_uuid}文档内容已成功写入 Milvus 向量数据库{collection_name}！"
        )
    except Exception as e:
        logger.error(f"{doc_uuid}处理过程中出错: {e}", exc_info=True)
        raise Exception("处理过程中出错，请稍后重试")


async def delete_items_by_doc_uuid(collection_name, doc_uuid):
    try:
        client = MilvusClient(
            uri=f"http://{settings.MILVUS_HOST}:{settings.MILVUS_PORT}"
        )
        # 新增集合存在性检查
        if not client.has_collection(collection_name):
            logger.warning(f"集合 {collection_name} 不存在，跳过删除操作")
            return
        # 假设文档存储时，每个文档的 metadata 包含了 doc_uuid 字段
        expr = f"doc_uuid in ['{doc_uuid}']"
        client.delete(collection_name=collection_name, filter=expr)
        logger.info(f"成功从 {collection_name} 中删除 doc_uuid 为 {doc_uuid} 的项")
    except Exception as e:
        logger.error(
            f"从 {collection_name} 中删除 doc_uuid 为 {doc_uuid} 的项时出错: {e}",
            exc_info=True,
        )


def merge_docs(docs: list[Document]) -> list[Document]:
    """
    ### 将docs中pk连续的Document对象进行page_content合并
    """
    merge_res = []
    merge_dict = {}
    # 构造{文档id:{"序号":文本}}映射
    for doc in docs:
        split_id = doc.metadata["pk"].split("_")
        if split_id[0] in merge_dict.keys():
            merge_dict[split_id[0]].update({split_id[1]: doc.page_content})
        else:
            merge_dict.update({split_id[0]: {split_id[1]: doc.page_content}})
    for item, texts in merge_dict.items():
        # 先将序号转换为整型并排序
        sorted_keys = sorted([int(k) for k in texts.keys()])
        segments = []  # 用于存储拼接后的各段
        current_segment = []  # 当前段的文本列表
        previous_key = None  # 记录上一个序号

        # 遍历所有的序号
        for key in sorted_keys:
            # 如果是第一项或当前序号和前一个序号连续（即相差1）并且拼接长度小于4段
            if (previous_key is None or key == previous_key + 1) and len(
                current_segment
            ) < 4:
                current_segment.append(texts[str(key)])
            else:
                # 序号不连续，则将前面收集的文本拼接成一段，并重置 current_segment
                segments.append(
                    Document(
                        page_content="".join(current_segment),
                        metadata={"doc_uuid": item},
                    )
                )
                current_segment = [texts[str(key)]]
            previous_key = key

        # 循环结束后，不要忘记将最后一段加入到 segments 中
        if current_segment:
            segments.append(
                Document(
                    page_content="".join(current_segment), metadata={"doc_uuid": item}
                )
            )

        merge_res.extend(segments)
    return merge_res


def extend_search_docs(
    slot: int, collection_name: str, docs: list[Document]
) -> list[Document]:
    """### 文档知识库对检索结果进行扩大查询拼接，以保障段落的完整性
    :param slot: 前后段落检索数量
    :param collection_name: 集合名称
    :param docs: 当前检索结果
    """
    search_ids = []
    doc_ids = [doc.metadata["pk"] for doc in docs]
    for doc_id in doc_ids:
        split_id = doc_id.split("_")
        for i in range(1, slot + 1):
            add_doc_id = f"{split_id[0]}_{int(split_id[1]) + i}"
            if add_doc_id not in doc_ids and add_doc_id not in search_ids:
                search_ids.append(add_doc_id)
            reduce_doc_id = f"{split_id[0]}_{int(split_id[1]) - i}"
            if reduce_doc_id not in doc_ids and reduce_doc_id not in search_ids:
                search_ids.append(reduce_doc_id)
    if len(search_ids) > 0:
        client = MilvusClient(
            uri=f"http://{settings.MILVUS_HOST}:{settings.MILVUS_PORT}"
        )
        # 通过前后片段id进行查找
        search_result = client.query(
            collection_name=collection_name,
            filter=f"pk in {str(search_ids)}",
            k=len(search_ids),
        )
        add_docs = [
            Document(
                page_content=result["text"],
                metadata={"pk": result["pk"]},
            )
            for result in search_result
        ]
        docs.extend(add_docs)
        # 合并连续段落
        docs = merge_docs(docs)
    return docs


# 收藏库查询
async def search_collection_in_milvus(message, current_user):
    try:
        manage_db = await get_database()

        # 构建查询条件，模糊匹配message
        escaped_name = re.escape(message)
        regex_pattern = f".*{escaped_name}.*"
        query = {"doc_uuid": {"$regex": regex_pattern, "$options": "i"}}

        # 初始化 MilvusClient
        client = MilvusClient(
            uri=f"http://{settings.MILVUS_HOST}:{settings.MILVUS_PORT}"
        )
        # 查找所有manage_db.databases中owner_id为current_user.id且is_deleted为False且v_name以_col结尾的记录
        databases = await manage_db.databases.find(
            {
                "owner_id": current_user.id,
                "is_deleted": False,
                "v_name": {"$regex": ".*_col$"},
            }
        ).to_list(None)
        # 获取所有collection_name
        collection_names = [database["v_name"] for database in databases]
        # 遍历collection_names，找到collection_name对应的client目录，查询query对应的text，返回所有符合结果的doc_uuid
        result = []
        for collection_name in collection_names:
            # 检查collection是否存在
            if not client.has_collection(collection_name):
                logger.warning(f"Collection {collection_name} 不存在")
                continue
            # 使用正则表达式过滤
            res = client.query(
                collection_name=collection_name,
                # filter=f'text like "{query}"',
                filter=f'text like "%{message}%"',
                output_fields=["doc_uuid"],
                # limit=100,
                # consistency_level="Strong",
            )
            for item in res:
                if doc_uuid := item.get("doc_uuid"):
                    result.append(doc_uuid)

        # 去重处理
        return list(set(result))

    except Exception as e:
        logger.error(f"查询过程中出错: {e}", exc_info=True)
        raise Exception("查询过程中出错，请稍后重试")


# 搜索文档
async def search_documents_in_milvus(content, current_user):
    try:
        manage_db = await get_database()

        # 初始化 MilvusClient
        client = MilvusClient(
            uri=f"http://{settings.MILVUS_HOST}:{settings.MILVUS_PORT}"
        )

        databases = await manage_db.databases.find(
            {
                "owner_id": current_user.id,
                "is_deleted": False,
                "type": "file",
            }
        ).to_list(None)

        # collection_names = [database["v_name"] for database in databases]
        # 遍历collection_names，找到collection_name对应的client目录，查询query对应的text，返回所有符合结果的doc_uuid
        result = []
        for database in databases:
            # 检查collection是否存在
            if not client.has_collection(database["v_name"]):
                logger.warning(f"Collection {database['v_name']}不存在")
                continue
            # 使用正则表达式过滤
            res = client.query(
                collection_name=database["v_name"],
                filter=f'text like "%{content}%"',
                output_fields=["doc_uuid", "text"],
            )
            for item in res:
                if (doc_uuid := item.get("doc_uuid")) and (text := item.get("text")):
                    result.append(
                        {
                            "doc_uuid": doc_uuid,
                            "text": text,
                            "database_name": database["name"],
                        }
                    )

        # 去重处理
        return result

    except Exception as e:
        logger.error(f"查询过程中出错: {e}", exc_info=True)
        raise Exception("查询过程中出错，请稍后重试")
