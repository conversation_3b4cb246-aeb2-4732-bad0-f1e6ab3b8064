import asyncio
import base64
import json
import os
import time
from datetime import datetime
from enum import Enum
from typing import Optional
from uuid import uuid4

import tiktoken
from langchain_community.callbacks.manager import get_openai_callback
from langchain_core.utils.function_calling import convert_to_openai_tool
from langchain_milvus import Milvus
from langchain_openai import ChatOpenAI

from ..config import settings
from ..database import get_database, get_eval_database
from ..models.message import Message
from ..schemas.message import MessageData
from .asr import VoiceTranscriber
from .bm25 import BM25_MANAGER
from .file_handler import download_file, identify_file, tokenizer, identify_unstructured_file
from .log import logger
from .milvusdb import extend_search_docs
from .prompt import GraphQueryHandler
from .remote_model import embedding_model
from .time_utils import resolve_relative_time
from .tools import (
    available_tools,
    execute_tool,
    get_file_contents,
    get_group_recent_chat_history,
    log_tool_usage,
    rerank_context,
)
from .web_search import Bo<PERSON>ha<PERSON>earch,<PERSON><PERSON><PERSON>earch
from .websocket_manager import connection_dic
from .locales import get_translator


class SupportedModels(Enum):
    """Enum for supported large language models."""
    RAG = settings.DEFAULT_GROUP_ASSISTANT_NAME + "（推荐）"
    TONGYI_QIANWEN = "通义千问（收费）"
    TONGYI_QIANWEN_VL = "通义千问-视觉（收费）"
    TONGYI_QIANWEN_LONG = "通义千问-长文本（收费）"
    # TONGYI_QIANWEN_WANX_T2I = "通义千问-文生图（收费）"
    # TONGYI_QIANWEN_WANX_T2V = "通义千问-文生视频（收费）"
    # ZHIPU_QINGYAN = "zhipu_qingyan"
    # BAIDU_QIANFAN = "baidu_qianfan"
    # TENCENT_HUNYUAN = "tencent_hunyuan"
    DEEPSEEK = "DeepSeek（收费）"
    GEMINI_PRO = "Gemini_Pro（收费）"
    LOCAL_MODEL = "通义千问-32B（免费）"
    # 群助手务必放在最后一个,以便前端过滤


def get_model_config(_=None):
    """Get model configuration with translated keys"""
    if _ is None:
        _ = lambda s: s  # Fallback function if no translator provided

    return {
        _("通义千问（收费）"): {
            "model_name": f"{settings.ALITONGYI_MODEL_NAME}",
            "max_token": 100000,
        },
        _("通义千问-视觉（收费）"): {
            "model_name": f"{settings.ALITONGYI_VL_MODEL_NAME}",
            "max_token": 30720,
        },
        _("通义千问-长文本（收费）"): {
            "model_name": f"{settings.ALITONGYI_LONG_MODEL_NAME}",
            "max_token": 10000000,
        },
        _("通义千问-文生图（收费）"): {
            "model_name": f"{settings.ALITONGYI_WANX_T2I_MODEL_NAME}",
            "max_token": 30720,
        },
        _("通义千问-文生视频（收费）"): {
            "model_name": f"{settings.ALITONGYI_WANX_T2V_MODEL_NAME}",
            "max_token": 30720,
        },
        _("DeepSeek（收费）"): {
            "model_name": f"{settings.DEEPSEEK_MODEL_NAME}",
            "max_token": 60000,
        },
        _("Gemini_Pro（收费）"): {
            "model_name": f"{settings.GEMINI_MODEL_NAME}",
            "max_token": 60000,
        },
        _("通义千问-32B（免费）"): {
            "model_name": f"{settings.LOCAL_MODEL_NAME}",
            "max_token": 6000,
        },
    }

# Keep the original for backward compatibility
model_config = {
    "通义千问（收费）": {
        "model_name": f"{settings.ALITONGYI_MODEL_NAME}",
        "max_token": 60000,
    },
    "通义千问-视觉（收费）": {
        "model_name": f"{settings.ALITONGYI_VL_MODEL_NAME}",
        "max_token": 30720,
    },
    "通义千问-长文本（收费）": {
        "model_name": f"{settings.ALITONGYI_LONG_MODEL_NAME}",
        "max_token": 10000000,
    },
    "通义千问-文生图（收费）": {
        "model_name": f"{settings.ALITONGYI_WANX_T2I_MODEL_NAME}",
        "max_token": 30720,
    },
    "通义千问-文生视频（收费）": {
        "model_name": f"{settings.ALITONGYI_WANX_T2V_MODEL_NAME}",
        "max_token": 30720,
    },
    "DeepSeek（收费）": {
        "model_name": f"{settings.DEEPSEEK_MODEL_NAME}",
        "max_token": 60000,
    },
    "Gemini_Pro（收费）": {
        "model_name": f"{settings.GEMINI_MODEL_NAME}",
        "max_token": 60000,
    },
    "通义千问-32B（免费）": {
        "model_name": f"{settings.LOCAL_MODEL_NAME}",
        "max_token": 6000,
    },
}

def get_model_with_lang(model_name, _=None):
    """Get model configuration with translated model name"""
    if _ is None:
        _ = lambda s: s  # Fallback function if no translator provided

    # First try to find the model in the translated config
    model_cfg = get_model_config(_)
    if model_name in model_cfg:
        return model_cfg[model_name]

    # Fallback to the original config for backward compatibility
    logger.error(f"find model: {model_name}")
    logger.error(f"models: {model_config}")
    for key, value in model_config.items():
        if None == _ and key == model_name:
            return value
        elif None != _ and _(key) == model_name:
            return value

def count_tokens(prompt: str, model: str = "qwen") -> int:
    """
    根据指定模型获取对应的编码器,并计算输入 prompt 的 token 数量。
    """
    try:
        encoding = tiktoken.encoding_for_model(model)
    except Exception:
        # 如果模型不支持,默认使用常见的编码(例如 cl100k_base)
        encoding = tiktoken.get_encoding("cl100k_base")
    tokens = encoding.encode(prompt)
    return len(tokens)


def context_handler(context: list, content_key: str, get_role: bool = False, **kwargs):
    final_context = []
    for i in context:
        if get_role:
            model_list = kwargs.get("model_list") or []
            # 从上下文拼接
            final_context.append(
                {
                    "role": (
                        "assistant"
                        if "sender_id" in i and i["sender_id"] in model_list
                        else "user"
                    ),
                    "content": i[content_key],
                }
            )
        else:
            # 从文档拼接
            final_context.append(
                {
                    "role": "user",
                    "content": (
                        getattr(i, content_key)
                        if hasattr(i, content_key)
                        else i.get(content_key, "")
                    ),
                }
            )
    return final_context


async def function_call_prompt_handler(
    role: Optional[str],
    integrate: bool,
    context: list,
    question: str,
    quality_check: bool,
    abbrs: list,
    insert_default_message: bool,
    RAG: bool,
    base_prompt: str = "",
    _=None
):
    try:
        """
        ### 上下文拼接(对话补全版本)
        :param role: 角色
        :param prompt_text: 提示词
        :param context: 上下文
        :param question: 问题
        :param quality_check: 是否进行质量检查
        :return: 拼接后的上下文
        """
        if not role:
            role = _("你是一个智能助手,你需要根据用户的问题,整理上下文的内容输出回复,如果上下文不足以回复用户的问题,请你自行补充。另外这是在多人群聊里,会有很多消息。每条消息都以【时间】【用户名】为前缀,请根据【】中内容识别时间和发言者")
        messages = [
            {"role": "system", "content": role},
        ]
        setting_content = _("{}\n\
                            {}\n\
                            注意：\
                            1.除非用户在问题中有明确要求,否则query及prompt只作为参考内容内容,;\
                            2.若因上下文不完整,相关性不足,或有矛盾而影响解答,你可自行解答,或补充后解答;\
                            3.遇到指代不明时可合理推断;\
                            4.由于是多人参与的对话,所以用户问题如果与之前的对话内容无关,请忽略之前的对话内容;\
                            {}\n\
                            {}").format(
                            _('当前查询时间:{}').format(datetime.now()) if not RAG else '',
                            _('`用户的问题`:{}').format(question) if not RAG else question,
                            _('**只回答结论**') if not integrate else _('**回答完整内容**'),
                            _('**如果你接收到的问题存在影响回答质量的干扰因素,请提示用户。情况严重时可不必强行输出答案。**') if quality_check else ''
                            )
        # messages.extend(context)
        # about = "\n".join([message["content"] for message in context])
        messages.extend(context)
        # messages.append(
        #     {"role": "user", "content": rf"""\n{base_prompt if RAG else ''}\n"""}
        # )
        messages.insert(1, {"role": "user", "content": setting_content})
        if len(messages) <= 2 and insert_default_message:
            # 解决qwen-32B 的bug
            messages.insert(1, {"role": "user", "content": "你好"})
            messages.insert(
                2,
                {
                    "role": "assistant",
                    "content": "!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!",
                },
            )
        if len(abbrs) > 0:
            # 替换缩略词
            for message in messages:
                if message["role"] == "tool":
                    continue
                for abbr in abbrs:
                    message["content"].replace(abbr["abbr"], abbr["origin"])

        return messages
    except Exception as e:
        logger.error(f"prompt_handler error: {e}")


async def prompt_handler(
    role: Optional[str],
    integrate: bool,
    context: list,
    question: str,
    quality_check: bool,
    abbrs: list,
    insert_default_message: bool,
    RAG: bool,
    base_prompt: str = "",
    _=None,
):
    try:
        """
        ### 上下文拼接(对话补全版本)
        :param role: 角色
        :param prompt_text: 提示词
        :param context: 上下文
        :param question: 问题
        :param quality_check: 是否进行质量检查
        :return: 拼接后的上下文
        """
        base_prompt = _("我是一个基于本地RAG的多人参与的对话Agent,因此,我发送给你的内容中会包括`上下文context结构`,请你根据`用户的问题`,整理`上下文context结构`的内容输出回复,如果`上下文context结构`不足以回复`用户的问题`,请你自行补充")
        if not role:
            role = _("你是一个智能助手,你需要根据用户的问题,整理上下文的内容输出回复,如果上下文不足以回复用户的问题,请你自行补充。")
        messages = [
            {"role": "system", "content": role},
        ]
        setting_content = _("{}\n\
                            {}\n\
                            注意：\
                            1.除非用户在问题中有明确要求,否则query及prompt只作为参考内容内容,;\
                            2.若因上下文不完整,相关性不足,或有矛盾而影响解答,你可自行解答,或补充后解答;\
                            3.遇到指代不明时可合理推断;\
                            4.由于是多人参与的对话,所以用户问题如果与之前的对话内容无关,请忽略之前的对话内容;\
                            {}\n\
                            {}").format(
                            _('当前查询时间:{}').format(datetime.now()) if not RAG else '',
                            _('`用户的问题`:{}').format(question) if not RAG else question,
                            _('**只回答结论**') if not integrate else _('**回答完整内容**'),
                            _('**如果你接收到的问题存在影响回答质量的干扰因素,请提示用户。情况严重时可不必强行输出答案。**') if quality_check else ''
                            )
        #
        # messages.extend(context)
        about = "\n".join([message["content"] for message in context])
        messages.append(
            {
                "role": "user",
                "content": _("""{}\n上下文context结构:{}\n""")
                .format(
                base_prompt if RAG else '',
                about
                ),
            }
        )
        messages.append({"role": "user", "content": setting_content})
        if len(messages) <= 2 and insert_default_message:
            # 解决qwen-32B 的bug
            messages.insert(1, {"role": "user", "content": _("你好")})
            messages.insert(
                2,
                {
                    "role": "assistant",
                    "content": "!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!",
                },
            )
        if len(abbrs) > 0:
            # 替换缩略词
            for message in messages:
                for abbr in abbrs:
                    message["content"].replace(abbr["abbr"], abbr["origin"])

        return messages
    except Exception as e:
        logger.error(f"prompt_handler error: {e}")


# def prompt_handler(
#     role: Optional[str],
#     integrate: bool,
#     context: list,
#     question: str,
#     quality_check: bool,
#     abbrs: list,
#     insert_default_message: bool,
#     RAG: bool,
#     base_prompt: str = "我是一个基于本地RAG的多人参与的对话Agent,因此,我发送给你的内容中会包括`上下文context结构`,请你根据`用户的问题`,整理`上下文context结构`的内容输出回复,\
#                         如果`上下文context结构`不足以回复`用户的问题`,请你自行补充",
# ):
#     try:
#         """
#         ### 上下文拼接(对话补全版本)
#         :param role: 角色
#         :param prompt_text: 提示词
#         :param context: 上下文
#         :param question: 问题
#         :param quality_check: 是否进行质量检查
#         :return: 拼接后的上下文
#         """
#         if not role:
#             role = "你是一个智能助手,你需要根据用户的问题,整理上下文的内容输出回复,如果上下文不足以回复用户的问题,请你自行补充。"
#         messages = [
#             {"role": "system", "content": role},
#         ]
#         #
#         # messages.extend(context)
#         about = "\n".join([message["content"] for message in context])
#         messages.append(
#             {
#                 "role": "user",
#                 "content": rf"""{base_prompt if RAG else ''}\n上下文context结构:{about}\n{f'当前查询时间:{datetime.now()}' if not RAG else ''}\n{f'`用户的问题`:{question}' if not RAG else ''}\n{'**只回答结论**' if not integrate else '**回答完整内容**'}\n{'**如果你接收到的问题存在影响回答质量的干扰因素,请提示用户。情况严重时可不必强行输出答案。**' if quality_check else ''}""",
#             }
#         )
#         if len(messages) <= 2 and insert_default_message:
#             # 解决qwen-32B 的bug
#             messages.insert(1, {"role": "user", "content": "你好"})
#             messages.insert(
#                 2,
#                 {
#                     "role": "assistant",
#                     "content": "!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!",
#                 },
#             )
#         if len(abbrs) > 0:
#             # 替换缩略词
#             messages = [
#                 {
#                     "role": message["role"],
#                     "content": message["content"].replace(abbr["abbr"], abbr["origin"]),
#                 }
#                 for abbr in abbrs
#                 for message in messages
#             ]
#         return messages
#     except Exception as e:
#         logger.error(f"prompt_handler error: {e}")


async def vl_prompt_handler(
    role: Optional[str],
    question: str,
    group_collection,
    cited_msg,
):
    """
    ### 视觉模型专用的上下文拼接
    :param role: 角色
    :param question: 用户问题
    :param group_collection: 群组消息集合
    :param cited_msg: 引用的消息
    :return: 处理后的消息列表
    """
    try:
        _ = await get_translator()
        manage_db = await get_database()
        cut_off = False

        if not role:
            role = _("你是图片识别模型,请根据图片内容和问题,输出回答。")

        # 查找当前用户在cited_msg之前最后一条非图片消息
        last_non_image_msg = await group_collection.find_one(
            {
                "group_id": cited_msg["group_id"],
                "sender_id": cited_msg["sender_id"],
                "created_at": {"$lt": cited_msg["created_at"]},
                "type": {"$not": {"$regex": "^image/"}},  # 排除图片类型消息
            },
            sort=[("created_at", -1)],  # 按时间降序排序,获取最近的一条
        )

        # 构建图片查询条件
        image_query = {
            "group_id": cited_msg["group_id"],
            "created_at": {"$lt": cited_msg["created_at"]},
            "type": {"$regex": "^image/"},
        }
        # 如果有最后一条非图片消息,添加时间范围条件
        if last_non_image_msg:
            image_query["created_at"]["$gt"] = last_non_image_msg["created_at"]
        # 查找符合条件的图片消息
        image_messages = (
            await group_collection.find(image_query)
            .sort("created_at", -1)
            .to_list(None)
        )
        # 处理被引用消息中的图片消息
        if cited_msg.get("cite"):
            cited_image_msg = await group_collection.find_one(
                {"id": cited_msg["cite"]["id"], "type": {"$regex": "^image/"}}
            )
            if cited_image_msg:
                # 去重并添加到最前面
                image_messages = [
                    msg for msg in image_messages if msg["id"] != cited_image_msg["id"]
                ]
                image_messages.insert(0, cited_image_msg)

        image_contents = []
        total_tokens = 0
        max_tokens = 3000000  # 最大token限制
        question_token = count_tokens(question)
        total_tokens += question_token

        # 处理每张图片
        for img_msg in image_messages:
            try:
                # 解析图片消息内容
                img_content = json.loads(img_msg["content"])
                file_id = img_content["id"]

                # 查询文件记录
                file_record = await manage_db.files.find_one(
                    {"id": file_id, "is_deleted": False}
                )
                if not file_record:
                    continue

                if file_record["file_size"] > 10 * 1024 * 1024:
                    logger.warning(f"图片 {file_id} 超过10MB,跳过处理")
                    continue

                # 下载文件
                if not os.path.exists(file_record["local_file_path"]):
                    local_path = await download_file(file_record["cloud_file_path"])
                else:
                    local_path = file_record["local_file_path"]

                # 读取文件内容并编码为base64
                with open(local_path, "rb") as f:
                    file_content = f.read()
                base64_image = base64.b64encode(file_content).decode("utf-8")

                # 计算当前图片的token
                image_token = count_tokens(base64_image)
                if total_tokens + image_token > max_tokens:
                    logger.warning(f"已达到token限制({max_tokens}),跳过剩余图片")
                    cut_off = True
                    break

                # 获取图片类型
                file_type = file_record["file_type"].split("/")[-1]
                if file_type == "jpeg":
                    file_type = "jpg"

                # 添加到图片内容列表
                image_contents.append(
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/{file_type};base64,{base64_image}"
                        },
                    }
                )
                total_tokens += image_token

            except Exception as e:
                logger.error(f"处理图片消息 {img_msg['id']} 失败: {e}")
                continue

        whole_question = image_contents + [{"type": "text", "text": question}]

        messages = [
            {
                "role": "system",
                "content": [{"type": "text", "text": f"{role}"}],
            },
            {
                "role": "user",
                "content": whole_question,
            },
        ]

        return messages, cut_off

    except Exception as e:
        logger.error(f"prompt_handler error: {e}")


async def get_collection_data(group_id):
    # 获取群组数据
    database_db = await get_database()
    group_data = await database_db.groups.find_one({"id": group_id})
    if not group_data:
        logger.error(f"群组 {group_id} 不存在")
        return []

    # 获取数据库ID列表
    database_ids = group_data.get("database_ids", [])
    if not database_ids:
        logger.error(f"群组 {group_id} 未绑定知识库")
        return []

    # 查询所有关联的数据库记录获取v_name
    collection = []
    for db_id in database_ids:
        database = await database_db.databases.find_one({"id": db_id})
        if database:
            collection.append(database)

    return collection


async def get_group_data(group_id):
    # 获取群组数据
    database_db = await get_database()
    return await database_db.groups.find_one({"id": group_id})


async def update_streaming_msg(
    group_collection,
    group_data,
    message_id: str,
    chunk: str,
    full_content: str,
    is_streaming: bool,
    created_at: str,
    llm_model:str,
    is_AI: bool = False,
):
    """更新流式消息内容"""
    await group_collection.update_one(
        {"id": message_id},
        {
            "$set": {
                "content": full_content,
                # "is_streaming": True  # 保持流式状态
            }
        },
    )
    await connection_dic.broadcast(
        group_data["member_ids"],
        json.dumps(
            {
                "type": "model",
                "group_id": group_data["id"],
                "message_id": message_id,
                "content": chunk,
                "is_streaming": is_streaming,
                "is_AI": is_AI,
                "created_at": created_at,
                "sender_username":llm_model
            }
        ),
    )


async def finalize_streaming_msg(
    group_collection,
    manage_db,
    message_id: str,
    full_content: str,
    group_data,
    is_AI,
):
    """完成流式消息"""
    await group_collection.update_one(
        {"id": message_id},
        {
            "$set": {
                "content": full_content,
                "is_AI": is_AI,
                "is_streaming": False,  # 结束流式状态
            }
        },
    )
    group_data = await manage_db.groups.find_one({"id": group_data["id"]})
    if group_data["last_message"]["id"] == message_id:
        await manage_db.groups.update_one(
            {"id": group_data["id"]}, {"$set": {"last_message.content": full_content}}
        )


async def background_answer(
    group_data,
    question,
    llm_model,
    group_collection,
    user_data,
    web_search=False,
    cite_id=None,
    recent_files=[],
    _=None,
    lang=None
):
    try:
        initial_msg = None
        full_response = ""
        manage_db = await get_database()
        # 创建初始空消息
        initial_msg = await broadcast_and_save_msg(
            group_collection,
            manage_db,
            content="",  # 初始为空内容
            type="text",
            group_id=group_data["id"],
            sender_id=llm_model,
            cite_id=cite_id,
            is_streaming=True,
        )

        # 流式处理每个chunk
        if _(settings.DEFAULT_GROUP_ASSISTANT_NAME) not in llm_model and not recent_files:
            answer_generator = answer(
                initial_msg.id,
                group_data,
                question,
                llm_model,
                group_collection,
                cite_id,
                web_search=web_search,
                _=_,
                lang=lang,
                user_data=user_data
            )
        else:
            # recent_files = group_data.get("recent_files", [])
            if not recent_files:
                answer_generator = function_call_answer(
                    initial_msg.id,
                    question,
                    llm_model,
                    group_collection,
                    group_data,
                    cite_id,
                    web_search,
                    _,
                    lang,
                    user_data
                )
            else:
                last_file = recent_files[-1]
                if last_file.get("type", "").startswith("image/"):
                    answer_generator = vl_answer(
                        initial_msg.id,
                        group_data,
                        question,
                        llm_model,
                        group_collection,
                        cite_id,
                        user_data,
                        recent_files=recent_files,
                        web_search=web_search,
                        _=_,
                    )
                else:
                    answer_generator = long_answer(
                        initial_msg.id,
                        group_data,
                        question,
                        llm_model,
                        group_collection,
                        cite_id,
                        user_data,
                        recent_files=recent_files,
                        web_search=web_search,
                        _=_,
                    )
            # if group_data["agent_setting"]["model"] == "通义千问-视觉（收费）":
            #     answer_generator = vl_answer(
            #         initial_msg.id,
            #         group_data,
            #         question,
            #         llm_model,
            #         group_collection,
            #         cite_id,
            #         user_data,
            #         web_search=web_search,
            #     )
            # elif group_data["agent_setting"]["model"] == "通义千问-长文本（收费）":
            #     answer_generator = long_answer(
            #         initial_msg.id,
            #         group_data,
            #         question,
            #         llm_model,
            #         group_collection,
            #         cite_id,
            #         user_data,
            #         web_search=web_search,
            #     )
            # else:
            #     answer_generator = function_call_answer(
            #         initial_msg.id,
            #         group_data,
            #         question,
            #         llm_model,
            #         group_collection,
            #         group_data,
            #         cite_id,
            #         web_search,
            #     )
        async for chunk in answer_generator:
            # status = await group_collection.find_one({"id":initial_msg.id})
            # if status.get("stop",False) is True:
            #     break
            full_response += chunk
            # 实时广播每个片段
            await update_streaming_msg(
                group_collection,
                group_data,
                message_id=initial_msg.id,
                chunk=chunk,
                full_content=full_response,
                is_streaming=True,
                is_AI=False,
                created_at=initial_msg.created_at.strftime("%Y-%m-%d %H:%M:%S"),
                llm_model=llm_model
            )
        else:
            await update_streaming_msg(
                group_collection,
                group_data,
                message_id=initial_msg.id,
                chunk=full_response,
                full_content=full_response,
                is_streaming=False,
                is_AI=True,
                created_at=initial_msg.created_at.strftime("%Y-%m-%d %H:%M:%S"),
                llm_model=llm_model
            )
        # 最终更新完整消息
        await finalize_streaming_msg(
            group_collection,
            manage_db,
            message_id=initial_msg.id,
            full_content=full_response,
            group_data=group_data,
            is_AI=True,
        )
    except Exception as e:
        logger.error(f"Answer Error occurred: {e}", exc_info=True)
        if initial_msg:
            # 最终更新完整消息
            await finalize_streaming_msg(
                group_collection,
                manage_db,
                message_id=initial_msg.id,
                full_content=full_response,
                group_data=group_data,
                is_AI=True,
            )


async def query_graph(group_data, question):
    """
    ### 从知识库中查询问题(类封装版)
    """
    handler = GraphQueryHandler(group_data, question)
    triplets = await handler.execute()
    context = [{"sender_id": "user", "content": str(triplets)}]
    if triplets:
        logger.info(f"知识图谱查询结果: {triplets}")
    return context


async def get_group_chat_history(slot: int, group_collection, cited_msg, group_data):
    """
    ## 获取历史间隔小于slot分钟的聊天记录
    :param slot: 对话间隔
    """
    now_time = cited_msg["created_at"]
    history_chat_content = []
    chat_content = (
        await group_collection.find(
            {
                "id": {"$ne": cited_msg["id"]},  # 过滤用户当前发起的消息
                "content": {"$ne": ""},  # 过滤空消息
                "is_revoke": {"$ne": True},  # 过滤撤回消息
                "created_at": {"$lte": cited_msg["created_at"]},
            }
        )
        .sort("created_at", -1)
        .to_list(None)
    )
    i = 0
    while True:
        # 越界或新话题进行截断
        if i >= len(chat_content) or chat_content[i]["type"] == "new":
            break
        last_message_time = chat_content[i]["created_at"]
        # 计算时间差
        time_difference = now_time - last_message_time
        # 将时间差转换为分钟
        minutes_difference = time_difference.total_seconds() / 60
        if minutes_difference < slot:
            history_chat_content.insert(0, chat_content[i])
            now_time = chat_content[i]["created_at"]
            i += 1
        else:
            break
        # 解析上下文或引用内容中的文档
    # history_chat_content = await get_file_contents(
    #     history_chat_content, group_data, group_collection
    # )

    answer_cite_id = (
        cited_msg["cite"]["id"] if cited_msg and cited_msg.get("cite") else None
    )
    if answer_cite_id:
        # 拼接当前提问引用链内容
        cite_chain = await get_cite_chain_contents(
            group_collection,
            answer_cite_id,
        )
        # 去除引用和上下文重复的内容
        for cite in cite_chain:
            if cite not in history_chat_content:
                history_chat_content.insert(0, cite)
        # cite_chain = await get_file_contents(cite_chain, group_data, group_collection)
    # logger.debug('''历史对话内容: {}'''.format('\n\n'.join([f"{chat['sender_username']}({chat['created_at'].strftime('%Y-%m-%d %H:%M:%S')}){chat['content'][:10]}..." for chat in history_chat_content])))
    for item in history_chat_content:
        item["content"] = (
            f"发送者:{item.get('sender_username','')},发送时间:{item['created_at'].strftime('%Y-%m-%d %H:%M:%S')},发送内容:{item['content']}"
        )
    return history_chat_content


async def function_call_answer(
    model_message_id, question, model, group_collection, group_data, cite_id, web_search, _,lang,user_data
):
    """
    ## 函数调用回答
    cite_id: 用户发送的消息的id
    web_search: 是否启用联网搜索
    question: 用户发送的查询消息
    """
    try:
        start_time = time.time()
        question_content = ""
        prompt = ""
        cloud_llm = None
        tool_responses = []
        system_prompt = _("你是一个多function的Agent系统的路由中枢,你需要根据如下要求解答用户问题\n\
                        (1)对于常识性或公共信息的问题,你可直接回答,无需调用其他function;\n\
                        (2)对于典型的用户私域问题,调用本地信息混合检索函数function A;\n\
                        (3)对于时效性问题(例如新闻,天气等),调用联网查询function B;\n\
                        (4)如果你无法判断应该调用function A或B,可以同时调用这两个function;\n\
                        注意:\n\
                        (1)你作为路由中枢,不能自造function,也不能自行修改function A和B函数名;\n\
                        (2)你调用function时必须严格遵循格式:{'name':'','arguments':''}输出json\n\
                        ")
        messages = [{"role": "system", "content": system_prompt}]
        eval_db = await get_eval_database()
        manage_db = await get_database()
        logger.debug(f"{cite_id}开始检索内容")
        # 添加默认初始化
        cite_msg_with_file = []

        # 查询用户发起的消息
        cited_msg = await group_collection.find_one({"id": cite_id})
        chat_content = await get_group_recent_chat_history(
            slot=10,
            group_collection=group_collection,
            cited_msg=cited_msg,
            group_data=group_data,
            sender_id=cited_msg["sender_id"],
            time_and_sender=False,
        )
        logger.debug(f"{cite_id}完成聊天记录检索")
        answer_cite_id = (
            cited_msg["cite"]["id"] if cited_msg and cited_msg.get("cite") else None
        )
        cite_msg_with_file = None
        if answer_cite_id:
            # 拼接当前提问引用链内容
            answer_cite_msg = await get_cite_chain_contents(
                group_collection, answer_cite_id, recursion_deepth=10
            )
            logger.debug(f"{cite_id}完成引用聊天记录检索")
            # cite_ids = [cite["id"] for cite in answer_cite_msg]
            # 去除引用内容与上下文重复的内容(优先保留引用内容)
            # chat_content = [chat for chat in chat_content if chat["id"] not in cite_ids]
            cite_msg_with_file = await get_file_contents(
                answer_cite_msg, group_data, group_collection, _
            )
            logger.debug(f"{cite_id}完成引用聊天文件检索")
        chat_content = [
            {
                "role": "assistant" if chat["is_AI"] else "user",
                "content": f"{chat['content']}",
            }
            for chat in chat_content
        ][:5]
        messages.extend(chat_content[::-1])
        # 获取用户数据
        # user_data = await manage_db.users.find_one({"id": cited_msg["sender_id"]})
        local_llm, max_token = get_llm(
            model_name=_("通义千问-32B（免费）"),
            user_setting=user_data.get("user_setting", {}),
            tools=available_tools,
            web_search=web_search,
            structured_output=True,
            _=_,
        )
        if _(settings.DEFAULT_GROUP_ASSISTANT_NAME) in model:
            # 查询群设置model
            model = _(group_data["agent_setting"]["model"])
            if not model:
                NO_GROUP_MODEL_TEXT = _("当前群聊未设置默认AI服务,请联系群主设置后再试。")
                for text in NO_GROUP_MODEL_TEXT:
                    yield text
                return
        if _(" (共享)") in model:
            # 共享模型调用时，获取群主自定义模型列表
            group_owner_data = await manage_db.users.find_one(
                {"id": group_data.get("owner_id")}
            )
            user_setting = group_owner_data.get("user_setting", {})
        else:
            user_setting = user_data.get("user_setting", {})
        cloud_llm, max_token = get_llm(
            model_name=model,
            user_setting=user_setting,
            _=_,
        )
        question_content = _("# query - 引用：{}\n- 问题：{} \n").format(
                            ';'.join([cite['content'] for cite in cite_msg_with_file]) if answer_cite_id else '',
                            resolve_relative_time(question)
                            )
        logger.debug(f"{cite_id}完成时间改写")
        messages.append({"role": "user", "content": question_content})
        if len(messages) <= 2:
            # 解决qwen-32B 的bug
            messages.insert(1, {"role": "user", "content": _("你好")})
            messages.insert(
                2,
                {
                    "role": "assistant",
                    "content": "!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!",
                },
            )
        with get_openai_callback() as callback:
            max_tool_call_retry = 5
            tool_call_retry = 0
            while tool_call_retry < max_tool_call_retry:
                if answer_cite_id and answer_cite_msg != cite_msg_with_file:
                    # 引用链存在文档时不调用function
                    break
                try:
                    # messages, is_truncated = await truncate_tool_messages(
                    #     messages, 8000
                    # )
                    # if is_truncated:
                    #     logger.warning(f"消息{cite_id}已截断tool角色内容")
                    gathered = await local_llm.ainvoke(messages)
                    logger.debug(f"{cite_id}进行第{tool_call_retry+1}轮工具选择")                
                    if "arguments" in gathered:
                        tool_id = str(uuid4())
                        gathered["args"] = gathered["arguments"]
                        gathered.pop("arguments")
                        gathered["id"] = tool_id
                        tool_call_chunks = [gathered]
                        logger.debug(
                            f"{cite_id}第{tool_call_retry+1}轮调用function {tool_call_chunks}"
                        )
                        tool_call_responses = await handle_tool_calls(
                            tool_call_chunks,
                            group_data=group_data,
                            user_data=user_data,
                            message_id=cite_id,
                            cited_msg=cited_msg,
                            group_collection=group_collection,
                            _ = _,
                            lang = lang,
                        )
                        logger.debug(
                            f"{cite_id}第{tool_call_retry+1}轮调用结果 {tool_call_responses}"
                        )
                        for tool_call,call_response in zip(tool_call_chunks,tool_call_responses):
                            if "error" not in  call_response['content']:
                                messages.append(
                                {
                                    "role": "assistant",
                                    "content": "",
                                    "tool_calls": [tool_call],
                                }
                                )
                                messages.append(call_response)
                        tool_responses.extend(tool_call_responses)
                        tool_call_retry += 1
                        break
                    elif isinstance(gathered,dict):
                        # 当模型输出不符合工具调用格式时进行记录
                        await log_tool_usage(
                            user_id=user_data["id"],
                            group_id=group_data["id"],
                            message_id=cite_id,
                            tool_name=None,
                            args=gathered,
                            result=gathered,
                            ) 
                        if tool_call_retry >= 1:
                            break
                        messages.append(
                                {
                                    "role": "assistant",
                                    "content": json.dumps(gathered),
                                })
                        # tool_choice_prompt = """请你根据数据对象及检索方式,选择对应的函数及入参值。"""
                        # messages[0] = {"role": "system", "content": tool_choice_prompt}
                    elif isinstance(gathered,list) and len(gathered) > 0:
                        for call in gathered:
                            if "arguments" in gathered:
                                tool_id = str(uuid4())
                                gathered["args"] = gathered["arguments"]
                                gathered.pop("arguments")
                                gathered["id"] = tool_id
                                tool_call_chunks = [gathered]
                                logger.debug(
                                    f"{cite_id}第{tool_call_retry+1}轮调用function {tool_call_chunks}"
                                )
                                tool_call_responses = await handle_tool_calls(
                                    tool_call_chunks,
                                    group_data=group_data,
                                    user_data=user_data,
                                    message_id=cite_id,
                                    cited_msg=cited_msg,
                                    group_collection=group_collection,
                                )
                                logger.debug(
                                    f"{cite_id}第{tool_call_retry+1}轮调用结果 {tool_call_responses}"
                                )
                                for tool_call,call_response in zip(tool_call_chunks,tool_call_responses):
                                    if "error" not in  call_response['content']:
                                        messages.append(
                                        {
                                            "role": "assistant",
                                            "content": "",
                                            "tool_calls": [tool_call],
                                        }
                                        )
                                        messages.append(call_response)
                                tool_responses.extend(tool_call_responses)
                                tool_call_retry += 1
                            else:
                                # 当模型输出不符合工具调用格式时进行记录
                                await log_tool_usage(
                                    user_id=user_data["id"],
                                    group_id=group_data["id"],
                                    message_id=cite_id,
                                    tool_name="call",
                                    args=call,
                                    result=call,
                                    )  
                    else:
                        # 当模型输出不符合工具调用格式时进行记录
                        await log_tool_usage(
                            user_id=user_data["id"],
                            group_id=group_data["id"],
                            message_id=cite_id,
                            tool_name=None,
                            args=gathered,
                            result=gathered,
                            )  
                        logger.info(f"{cite_id} 检索完成,进行总结回复")
                        break
                except Exception as e:
                    logger.error(
                        f"{cite_id}第{tool_call_retry+1}轮调用function失败: {e}"
                    )
                    break
            final_prompt = _("""此消息来自一个多人(含AI)多群组,具备RAG能力的 Chat Agent;role为tool的内容为function calls返回的内容,包括：\n\
                            1."search_group_chat_history"：是群组对话历史查询记录;\n\
                            2."web_search":是来自互联网查询的结果;\n\
                            3."search_graph":知识图谱搜索结果;\n\
                            注意：\n\
                            1.以上function calls的返回内容,不分优先级;\n\
                            2.function calls的返回内容,如果可以完整且准确的回答用户问题,你可以直接基于这些内容,组织语言回答;\n\
                            3.function calls的返回内容,如果无效,或为空,或不充分,或有问题,你需要根据自己的知识进行完善并回答;\n\
                            4.由于是多人参与的Chat Agent,所以,user会指代多人,而真实用户名称,以user的content里面【】中的用户名为准;且不同用户之间的问题可能不相关;\n\
                            5.回答时间相关的问题时,请你根据用户消息里所携带的绝对时间,进行换算。\n\
                            """)
            messages[0] = {"role": "system", "content": final_prompt}
            prompt, is_truncated = await truncate_tool_messages(
                messages, max_token - 2000
            )
            # 修改为流式输出
            if is_truncated:
                # 当上下文截断时,输出提示
                yield _(f"由于上下文过长,已截断部分上下文。\n")
            logger.debug(f"{cite_id}总结prompt长度:{len(str(prompt))}")
            logger.debug(f"{cloud_llm} {max_token}")
            response = ""
            logger.info(f"提示词:{prompt}")
            end_time = time.time()
            async for chunk in cloud_llm.astream(prompt):
                status = await group_collection.find_one({"id": model_message_id})
                if status.get("stop", False) is True:
                    break
                chunk_content = (
                    chunk.content if hasattr(chunk, "content") else str(chunk)
                )
                response += chunk_content
                yield chunk_content  # 改为生成器模式
            logger.info(f"{cite_id}对话token消耗情况:{callback}")
            # 记录用户消耗token数
            await manage_db.users.update_one(
                {"id": cited_msg["sender_id"]},
                {"$inc": {"user_service.token_usage": callback.total_tokens}},
            )
    except ValueError as e:
        end_time = time.time()
        logger.error(f"Answer Error occurred: {e}", exc_info=True)
        response = _("{error},请重新选择模型").format(error=str(e))
        for text in response:
            yield text
    except Exception as e:
        end_time = time.time()
        logger.error(f"Answer Error occurred: {e}", exc_info=True)
        if "not enough quota" in str(e) or "Access denied" in str(e):
            response = _("公有云账号余额不足，请联系管理员充值。")
        elif "too long" in str(e):
            response = _("上下文过长，模型暂无法处理，正在优化中。")
        elif "authorize" in str(e):
            response = _("模型密钥认证失败，请检查密钥是否正确。")
        elif "invalid model" in str(e):
            response = _("模型名称错误，请检查模型名称是否正确。")
        else:
            response = _("获取大模型回复异常，请稍后再试。")
        for text in response:
            yield text
    finally:
        await eval_db.records.insert_one(
            {
                "group_id": group_data["id"],
                "prompt": prompt,
                "message_id": cite_id,
                "model": cloud_llm.model_name if cloud_llm is not None else cloud_llm,
                "question": question,
                "question_content": question_content,
                "chat_content": chat_content,
                "answer": response,
                "tool_responses": tool_responses,
                "time_usage": end_time - start_time,
            }
        )


async def handle_tool_calls(
    tool_calls, group_data, user_data, message_id, cited_msg, group_collection,_,lang
):
    """处理多个工具调用"""
    tool_responses = []

    for tool_call in tool_calls:
        tool_name = tool_call["name"]
        try:
            tool_args = json.loads(tool_call["args"])
        except Exception as e:
            tool_args = tool_call["args"]
        data_args = {
            "group_data": group_data,
            "cited_msg": cited_msg,
            "group_collection": group_collection,
        }
        # 执行工具
        tool_type, result = await execute_tool(tool_name, tool_args, data_args, _, lang)

        # 记录审计日志
        await log_tool_usage(
            user_id=user_data["id"],
            group_id=group_data["id"],
            message_id=message_id,
            tool_name=tool_name,
            args=tool_args,
            result=result,
        )

        tool_responses.append(
            {
                "role": "tool",
                "name": tool_name,
                "content": result,
                "tool_call_id": tool_call["id"],
            }
        )

    return tool_responses


async def answer(
    model_message_id,
    group_data,
    question,
    llm_model,
    group_collection,
    cite_id,
    user_data,
    web_search=False,
    _=None,
    lang="zh",
):
    try:
        # 设置函数中全局变量,判断上下文是否截断
        question = resolve_relative_time(question)
        cut_off = False
        max_token = 8000
        context = []
        manage_db = await get_database()
        eval_db = await get_eval_database()
        cited_msg = await group_collection.find_one({"id": cite_id})
        answer_cite_id = (
            cited_msg["cite"]["id"] if cited_msg and cited_msg.get("cite") else None
        )
        if answer_cite_id:
            # 拼接当前提问引用链内容
            answer_cite_msg = await get_cite_chain_contents(
                group_collection, answer_cite_id, recursion_deepth=10
            )
            cite_msg_with_file = await get_file_contents(
                answer_cite_msg, group_data, group_collection, _
            )
            context.extend(
                context_handler(
                    context=answer_cite_msg,
                    content_key="content",
                    get_role=True,
                )
            )
            context.extend(
                context_handler(
                    context=cite_msg_with_file,
                    content_key="content",
                    get_role=True,
                )
            )
        # 获取用户数据
        user_data = await manage_db.users.find_one({"id": cited_msg["sender_id"]})
        # 判断是否走RAG,选择模型
        RAG = False
        if _(settings.DEFAULT_GROUP_ASSISTANT_NAME) in llm_model:
            # 查询群设置model
            model = group_data["agent_setting"]["model"]
            if not model:
                NO_GROUP_MODEL_TEXT = _("当前群聊未设置默认AI服务,请联系群主设置后再试。")
                for text in NO_GROUP_MODEL_TEXT:
                    yield text
                return
            RAG = True
        else:
            model = llm_model
        if _(" (共享)") in model:
            # 共享模型调用时，获取群主自定义模型列表
            group_owner_data = await manage_db.users.find_one(
                {"id": group_data.get("owner_id")}
            )
            user_setting = group_owner_data.get("user_setting", {})
        else:
            user_setting = user_data.get("user_setting", {})
        # 修改后的模型调用
        llm, max_token = get_llm(
            model_name=model,
            user_setting=user_setting,
            _=_,
        )

        custom_models = [
            m["name"] for m in user_data.get("user_setting", {}).get("llm_models", [])
        ]
        preset_models = [_(str(model.value)) for model in SupportedModels]
        model_list = preset_models + custom_models
        if model == _("通义千问-视觉（收费）"):
            prompt, cut_off = await vl_prompt_handler(
                group_data["agent_setting"]["role"],
                question,
                group_collection,
                cited_msg,
            )
        else:
            # 非图片识别的逻辑
            # # 判断是否启用web搜索
            if web_search:
                if lang == "en":
                    searcher = TavilySearch()
                else:
                    searcher = BoChaSearch()
                results = await searcher.search(question)
                search_content = await searcher.pretty_search_results(results)
                context.extend(
                    context_handler(
                        context=search_content, content_key="summary", get_role=True
                    )
                )
            # 查询群历史聊天记录,最大间隔10分钟
            chat_content = await get_group_recent_chat_history(
                slot=10,
                group_collection=group_collection,
                cited_msg=cited_msg,
                group_data=group_data,
                sender_id=cited_msg["sender_id"],
            )
            chat_context = context_handler(
                context=chat_content,
                content_key="content",
                get_role=True,
                model_list=model_list,
            )
            context.extend(chat_context)
            if RAG:
                # 当启用RAG时,进行RAG检索
                collection = await get_collection_data(group_data["id"])
                all_docs = []
                # 循环遍历数据库,找到相似度最高的5个数据
                for collection_data in collection:
                    if not BM25_MANAGER.have_index(collection_data["v_name"]):
                        BM25_MANAGER.build_index(collection_data["v_name"])
                    bm25_docs = BM25_MANAGER.search(
                        collection_data["v_name"], question, top_k=5
                    )
                    all_docs.extend(bm25_docs)
                    vector_store = Milvus(
                        embedding_function=embedding_model,
                        collection_name=collection_data["v_name"],
                        connection_args={
                            "host": settings.MILVUS_HOST,
                            "port": settings.MILVUS_PORT,
                            "uri": f"http://{settings.MILVUS_HOST}:{settings.MILVUS_PORT}",
                        },
                    )
                    try:
                        docs = await vector_store.asimilarity_search(question, k=5)
                    except Exception as e:
                        logger.error(
                            f"{group_data['id']}中{collection_data['v_name']}检索失败:{e}"
                        )
                        docs = []
                    if "type" in collection_data and collection_data["type"] == "file":
                        slot = 1
                        docs = await asyncio.to_thread(
                            extend_search_docs, slot, collection_data["v_name"], docs
                        )
                    all_docs.extend(docs)
                doc_context = context_handler(
                    context=all_docs,
                    content_key="page_content",
                    get_role=False,
                )
                context.extend(doc_context)
                logger.critical(f"消息{cite_id}检索开始重排")
                if len(context) > 0:
                    context = await asyncio.to_thread(
                        rerank_context, question, context, 10
                    )
                logger.critical(f"消息{cite_id}检索结束重排")
                # # 知识图谱结果不参与外层重排
                # context.extend(
                #     context_handler(
                #         context=await query_graph(
                #             group_data,
                #             question,
                #         ),
                #         content_key="content",
                #         get_role=True,
                #         model_list=model_list,
                #     )
                # )
                logger.critical(f"消息{cite_id}最终检索出{len(context)}条数据")
            else:
                logger.critical(f"消息{cite_id}最终检索出{len(context)}条数据")
            prompt = await prompt_handler(
                role=group_data["agent_setting"]["role"],
                integrate=group_data["agent_setting"]["integrate"],
                context=context,
                question=question,
                quality_check=group_data["agent_setting"]["quality_check"],
                abbrs=group_data["agent_setting"]["abbr"],
                insert_default_message=True if model == _("通义千问-32B(免费)") else False,
                RAG=RAG,
                _=_,
            )
            prompt, cut_off = await cut_off_context(prompt, max_token)
            logger.info(f"prompt:{prompt}")
        # 修改为流式输出
        response = ""
        if cut_off:
            # 当上下文截断时,输出提示
            yield _("由于上下文过长,已截断部分上下文。") + "\n"
        if (
            cited_msg["type"].startswith("audio/")
            and cited_msg.get("duration", None)
            and (cited_msg["duration"] < 60 or cited_msg["duration"] == 60)
        ):
            # 短语音输出识别结果
            yield _("我理解你的问题是:{question}。").format(question=question) + "\n"
        with get_openai_callback() as callback:
            async for chunk in llm.astream(prompt):
                status = await group_collection.find_one({"id": model_message_id})
                if status.get("stop", False) is True:
                    break
                chunk_content = (
                    chunk.content if hasattr(chunk, "content") else str(chunk)
                )
                response += chunk_content
                yield chunk_content  # 改为生成器模式
            logger.info(f"{cite_id}对话token消耗情况:{callback}")
            # 记录用户消耗token数
            await manage_db.users.update_one(
                {"id": cited_msg["sender_id"]},
                {"$inc": {"user_service.token_usage": callback.total_tokens}},
            )
        content_list = [item["content"] for item in context]
        await eval_db.records.insert_one(
            {
                "message_id": cite_id,
                "model": llm.model_name if isinstance(llm,ChatOpenAI) else llm_model,
                "question": question,
                "context": content_list[:-1],
                "answer": response,
            }
        )
        # logger.info(f"最终完整响应: {response}")
        # res = await llm.ainvoke(prompt)
        # response = res if isinstance(res, str) else res.content
        # return response
    except ValueError as e:
        logger.error(f"Answer Error occurred: {e}", exc_info=True)
        for text in _("{error},请重新选择模型").format(error=str(e)):
            yield text
    except Exception as e:
        logger.error(f"Answer Error occurred: {e}", exc_info=True)
        if "not enough quota" in str(e):
            response = _("公有云账号余额不足，请联系管理员充值。")
        elif "too long" in str(e):
            response = _("上下文过长，模型暂无法处理，正在优化中。")
        elif "authorize" in str(e):
            response = _("模型密钥认证失败，请检查密钥是否正确。")
        elif "invalid model" in str(e):
            response = _("模型名称错误，请检查模型名称是否正确。")
        else:
            response = _("获取大模型回复异常，请稍后再试。")
        for text in response:
            yield text


async def broadcast_and_save_msg(
    group_collection,
    manage_db,
    content,
    type,
    group_id,
    sender_id,
    cite_id=None,
    sound_file_id=None,
    duration=None,
    save_message=True,
    at_list=[],
    is_AI: bool = False,
    is_streaming=False,
):
    """
    ### 广播并保存消息内容
    """
    try:
        user_data = await manage_db.users.find_one({"id": sender_id})
    except:
        user_data = {"avatar": None, "username": sender_id}
    if not user_data:
        user_data = {"avatar": None, "username": sender_id}
    if isinstance(content, dict):
        content = json.dumps(content)
    if cite_id:
        cite_data = await group_collection.find_one({"id": cite_id})
    else:
        cite_data = None
    group_data = await manage_db.groups.find_one({"id": group_id})
    # 保存用户消息
    message = Message(
        type=type,
        content=content,
        sender_id=sender_id,
        sender_avatar=user_data["avatar"],
        sender_username=user_data["username"],
        group_id=group_data["id"],
        receiver=[],
        vector_status="pending",
        transfer=None,
        duration=duration,
        cite=MessageData(**cite_data).model_dump() if cite_data else None,
        sound_file_id=sound_file_id,  # 语音文件ID
        at_list=at_list,
        read_list=[sender_id] if isinstance(sender_id, str) else [],
        is_AI=is_AI,
    )
    message_dict = message.model_dump()
    # 更新群聊最后一条对话记录
    message_dict["is_streaming"] = is_streaming
    receiver_list = await connection_dic.broadcast(
        group_data["member_ids"],
        json.dumps(MessageData(**message_dict).model_dump()),
    )
    if save_message:
        if type != "new":
            await manage_db.groups.update_one(
                {"id": group_data["id"]},
                {"$set": {"last_message": message_dict}},
            )
        # 记录发送给了哪些用户方便排查websocket管理问题
        message_dict["receiver"] = receiver_list
        await group_collection.insert_one(message_dict)

    return message

async def generate_faq(content:str) -> list:
    """
    从文档内容抽取FAQ
    :param content: 文档内容
    :return: FAQ列表
    """
    # 用长文本模型生成FAQ框架
    faq_frame,cut_off = await get_segments_frame_from_llm(content)
    if  not faq_frame: 
        raise Exception("生成FAQ框架失败")
    # 从文本中提取FAQ框架各要点内容
    parsed_faq_frame = parse_segments(content,faq_frame)
    # 用标准模型将要根据要点内容生成QA列表
    semaphore = asyncio.Semaphore(5)  # 并发限制
    async def generate_faq_task(segment):
        async with semaphore:
            content = segment.pop("content","")
            if content:
                qa_set,cut_off = await get_FAQ_from_llm(content)
                segment["qa_set"] = qa_set
  
    task_list = [generate_faq_task(segment) for segment in parsed_faq_frame if segment["first_class_title"] != segment["content"]]
    await asyncio.gather(*task_list)
    result = [segment for segment in parsed_faq_frame if segment["first_class_title"] != segment.get("content","")]
    return result

def parse_segments(content:str, segment_frame:list) -> list:
    """
    按照faq框架,从文档内容中提取faq片段
    :param content: 文档内容
    :param segment_frame: faq框架
    :return: 补充了片段的faq框架
    """
     # 预处理每个一级标题在文档中的起始位置
    positions = [content.find(seg['first_class_title']) for seg in segment_frame]
    
    for i in range(len(segment_frame)):
        current_pos = positions[i]
        if current_pos == -1:
            # 如果当前标题不存在于文档中，content字段设为空
            segment_frame[i]['content'] = ''
            continue
        
        # 寻找下一个存在的标题的起始位置
        end_pos = None
        j = i + 1
        while j < len(segment_frame):
            if positions[j] != -1:
                end_pos = positions[j]
                break
            j += 1
        
        # 如果后续没有找到存在的标题，则截取到文档末尾
        if end_pos is None:
            end_pos = len(content)
        
        # 截取当前标题到下一个标题之间的内容，并去除前后空白
        segment_frame[i]['content'] = content[current_pos:end_pos].strip()
    
    return segment_frame

async def get_segments_frame_from_llm(content:str) -> list:
    """
    用自然语言模型从文档内容抽取FAQ结构
    :param content: 文档内容
    :return: FAQ框架
    """
    try:
        # Get translator from context
        _ = await get_translator()

        llm,max_token = get_llm(_("通义千问-长文本（收费）"), {})
        SEPARATE_PROMPT = _("""
                        你是一个文档分析专家,请根据文档内容,提取出文档的结构化内容,并输出结构化内容;
                        一、结构化内容要求:
                        1.你需要仔细阅读并理解文档内容,按照文档知识结构提取知识要点;
                        2.每个要点的标题,必须是文章原始内容;\n\
                        二、输出要求
                        [
                            {
                                "first_class_title":str #一级要点标题
                            },
                            {
                                "first_class_title":str #一级要点标题
                            },
                            ....# 数量不限
                        ]
                        """)
        messages = [{"role":"system","content":SEPARATE_PROMPT}]
        messages.append({"role":"user","content":f"文档内容:{content}"})
        messages.append({"role":"user","content":""})
        # 文档超长时进行切割
        messages, cut_off = await cut_off_context(messages, max_token)
        with get_openai_callback() as callback:
            response = await llm.ainvoke(messages)
            if isinstance(response.content, str):
                response = json.loads(response.content.replace("```","").replace("json",""))
            logger.info(f"FAQ框架生成token消耗情况:{callback}")
            return response, cut_off
    except Exception as e:
        logger.error(f"FAQ框架生成失败: {str(e)}", exc_info=True)
        return [], cut_off

async def get_FAQ_from_llm(content:str,_=None) -> list:
    """
    用自然语言模型从文档内容生成FAQ
    :param content: 文档内容
    :return: FAQ列表
    """
    try:
        # Get translator from context
        llm,max_token = get_llm("通义千问（收费）", {})
        FAQ_PROMPT = _("""
                    你需要按照如下要求，对文档内容进行重构和输出；
                    一、重构要求
                    1.你需要首先通读并理解文档,提取文档每个主要章节的标题,再以每个章节里的最小段落为基准,从每个段落里提取全部原子化的知识要点；
                    2.要求每个最小化段落至少一个知识要点,且每个知识要点至少提出一个问题,不能有遗漏！都需要输出；
                    3.针对每个问题的标准答案，不能引入任何此文档外的知识或数据，仅能来自于此文档；
                    4.需确保以FAQ形式解析,输出文档的全部内容,严禁自行删减或忽略任何内容;
                    5.对于表格内容，需完整输出，以便于后期检索；
                    6.如果文档内容中,有代码行、或Linux命令、或表格,请无遗漏的提出相应问题,并在答案中包含这些内容,即使可能有相似或重复。
                    二、输出要求
                    ```json
                    [
                        {
                            "first_class_title": str # 知识要点
                            "qa_set" : [    # 覆盖知识要点所有内容的问题集
                                            {
                                                "question": str , # 针对知识要点的问题
                                                "answer": str , # 针对该问题的标准答案
                                            },
                                            {
                                                "question": str , # 针对知识要点的问题
                                                "answer": str , # 针对该问题的标准答案
                                            },
                                            ...# 数量不限
                                        ],
                        },
                    ]
                    ```
                    """)
        messages = [{"role":"system","content":FAQ_PROMPT}]
        messages.append({"role":"user","content":_("文档内容:{}").format(content)})
        messages.append({"role":"user","content":""})
        # 文档超长时进行切割
        messages, cut_off = await cut_off_context(messages, max_token)
        with get_openai_callback() as callback:
            for retry in range(3):
                try:
                    response = await llm.ainvoke(messages)
                    if isinstance(response.content, str):
                        response = json.loads(response.content.replace("```","").replace("json",""))
                        break
                    else:
                        logger.debug(f"文档：{content[:20]}生成FAQ响应类型异常:{type(response.content)}")
                except Exception as e:
                    logger.warning(f"文档：{content[:20]}生成FAQ异常:{str(e)}")
                    continue
            # logger.info(f"FAQ生成token消耗情况:{callback}")
        return response,cut_off
    except Exception as e:
        logger.error(f"FAQ生成失败:{e}")
        return [],cut_off

def get_llm(model_name, user_setting: dict, tools=[], web_search=False,structured_output=False, _=None):
    if _ is None:
        _ = lambda s: s  # Fallback function if no translator provided
    # 优先在自定义模型中查找
    tool_list = []
    for tool in tools:
        tool_list.append(convert_to_openai_tool(tool))
    common_params = {
        "model_kwargs": {"tools": tool_list, "tool_choice": "auto" if tools else None}
    }
    if model_name == _("通义千问（收费）"):
        common_params["model_kwargs"]["response_format"] = {"type":"json_object"}
    if None != _: 
        trans_model_name = _(model_name)
        trans_tongyi_qianwen = _(SupportedModels.TONGYI_QIANWEN.value)
        trans_tongyi_qianwen_vl = _(SupportedModels.TONGYI_QIANWEN_VL.value)
        trans_tongyi_qianwen_long = _(SupportedModels.TONGYI_QIANWEN_LONG.value)
        trans_local_model = _(SupportedModels.LOCAL_MODEL.value)
        trans_deepseek = _(SupportedModels.DEEPSEEK.value)
        trans_gemini = _(SupportedModels.GEMINI_PRO.value)
    else:
        trans_model_name = model_name
        trans_tongyi_qianwen = SupportedModels.TONGYI_QIANWEN.value
        trans_tongyi_qianwen_vl = SupportedModels.TONGYI_QIANWEN_VL.value
        trans_tongyi_qianwen_long = SupportedModels.TONGYI_QIANWEN_LONG.value
        trans_local_model = SupportedModels.LOCAL_MODEL.value
        trans_deepseek = SupportedModels.DEEPSEEK.value
        trans_gemini = SupportedModels.GEMINI_PRO.value

    for custom_model in [model for model in user_setting.get("llm_models", []) if model["is_deleted"] is False]:
        if _(custom_model["name"]) == trans_model_name.replace(_(" (共享)"),"").replace(_(" (自定义)"),""):
            return (
                ChatOpenAI(
                    api_key=custom_model["key"],
                    model=custom_model["model_name"],
                    base_url=custom_model["url"],
                    **common_params["model_kwargs"] if len(tool_list) > 0 else {},
                ),
                custom_model["max_token"],
            )
    # 在现有模型配置基础上添加工具参数
    if model_name == _("通义千问（收费）"):
        model = ChatOpenAI(
                api_key=settings.ALITONGYI_SK,
                model=get_model_with_lang(trans_model_name, _=_)["model_name"],
                base_url=settings.ALITONGYI_URL,
                **common_params if len(tool_list) > 0 else {},
            )
        if structured_output:
            model = model.with_structured_output(method="json_mode")
        return (
            model,
            get_model_with_lang(trans_model_name, _=_)["max_token"],
        )
    elif model_name == _("通义千问-视觉（收费）"):
        return (
            ChatOpenAI(
                api_key=settings.ALITONGYI_SK,
                model=get_model_with_lang(trans_model_name, _=_)["model_name"],
                base_url=settings.ALITONGYI_URL,
            ),
            get_model_with_lang(trans_model_name, _=_)["max_token"],
        )
    elif model_name == _("通义千问-长文本（收费）"):
        model = ChatOpenAI(
                api_key=settings.ALITONGYI_SK,
                model=get_model_with_lang(trans_model_name, _=_)["model_name"],
                base_url=settings.ALITONGYI_URL,
            )
        if structured_output:
            model = model.with_structured_output(method="json_mode")
        return (
            model,
            get_model_with_lang(trans_model_name, _=_)["max_token"],
        )
    # elif model_name == SupportedModels.TONGYI_QIANWEN_WANX_T2I.value:
    #     return (
    #         ChatOpenAI(
    #             api_key=settings.ALITONGYI_SK,
    #             model=model_config[model_name]["model_name"],
    #             base_url=settings.ALITONGYI_URL,
    #         ),
    #         model_config[model_name]["max_token"],
    #     )
    # elif model_name == SupportedModels.TONGYI_QIANWEN_WANX_T2V.value:
    #     return (
    #         ChatOpenAI(
    #             api_key=settings.ALITONGYI_SK,
    #             model=model_config[model_name]["model_name"],
    #             base_url=settings.ALITONGYI_URL,
    #         ),
    #         model_config[model_name]["max_token"],
    #     )
    elif model_name == _("通义千问-32B（免费）"):
        model =  ChatOpenAI(
                api_key=settings.LOCAL_MODEL_KEY,
                # Replace with the actual model identifier
                model=get_model_with_lang(trans_model_name, _=_)["model_name"],
                base_url=settings.LOCAL_MODEL_URL,
                **common_params["model_kwargs"] if len(tool_list) > 0 else {},
                temperature=0.3,
            )
        if structured_output:
            model = model.with_structured_output(method="json_mode")
        return (
            model,
            get_model_with_lang(trans_model_name, _=_)["max_token"],
        )
    elif model_name == _("DeepSeek（收费）"):
        return (
            ChatOpenAI(
                model=get_model_with_lang(trans_model_name, _=_)["model_name"],
                api_key=settings.DEEPSEEK_KEY,
                base_url=settings.DEEPSEEK_URL,
                # **common_params["model_kwargs"] if len(tool_list) > 0 else {},
            ),
            get_model_with_lang(trans_model_name, _=_)["max_token"],
        )
    elif model_name == _("Gemini_Pro（收费）"):
        os.environ["HTTP_PROXY"] = settings.HTTP_PROXY
        os.environ["HTTPS_PROXY"] = settings.HTTPS_PROXY
        return (
            ChatOpenAI(
                model=get_model_with_lang(trans_model_name, _=_)["model_name"],
                api_key=settings.GEMINI_KEY,
                base_url=settings.GEMINI_URL,
                # **common_params["model_kwargs"] if len(tool_list) > 0 else {},
            ),
            get_model_with_lang(trans_model_name, _=_)["max_token"],
        )
    # else:
    #     # This block should never be reached if the enum is used correctly
    #     return None

    raise ValueError(_("未找到模型配置: {model_name}").format(model_name=model_name))


async def get_cite_chain_contents(
    group_collection, initial_cite_id, recursion_deepth: int = 10
):
    """递归获取引用链内容"""
    contents = []
    current_cite_id = initial_cite_id
    recursion_count = 0

    while current_cite_id and recursion_count < recursion_deepth:
        msg = await group_collection.find_one({"id": current_cite_id})
        if not msg:
            break
        contents.append(msg)
        current_cite_id = msg.get("cite", {}).get("id") if msg.get("cite") else None
        recursion_count += 1

    return contents


async def cut_off_context(context: list, max_token: int):
    """当上下文过长时切除上下文"""
    # 新增:判断上下文总字符数
    # total_chars = sum(len(item.get("content", "")) for item in context)
    full_context_text = "\n".join(
        [
            f"role: {item['role']}\ncontent: {item.get('content', '')}"
            for item in context
        ]
    )
    tokens = len(tokenizer.encode(full_context_text))
    question_text = "\n".join(
        f"role: {context[-1]['role']}\ncontent: {context[-1].get('content', '')}"
    )
    question_token = len(tokenizer.encode(question_text))
    if tokens + question_token > max_token:
        logger.warning(f"上下文过长({tokens}token),已进行截断")
        # 截断逻辑
        truncated = []
        current_tokens = 0
        for item in context:
            if item["role"] in ["system", "tool"]:
                truncated.append(item)
                # 工具、系统级上下文不切割
                continue
            item_text = f"role: {item['role']}\ncontent: {item.get('content', '')}"
            item_tokens = len(tokenizer.encode(item_text))
            if item == context[-1]:
                # 当来到最后一项时(即问题),单独添加
                break
            if current_tokens + item_tokens > max_token - question_token:
                remaining = max_token - current_tokens - question_token
                if remaining > 0:  # 截断当前项内容
                    truncated_content = tokenizer.decode(
                        tokenizer.encode(item.get("content", ""))[:remaining]
                    )
                    truncated.append({**item, "content": truncated_content})
                    current_tokens += remaining
                break
            current_tokens += item_tokens
            truncated.insert(0, item)
            # truncated.append(item)
        truncated.append(context[-1])
        return truncated, True
    else:
        return context, False


# async def handle_tool_calls(tool_calls, group_data, user_data,message_id):
#     """处理多个工具调用"""
#     tool_responses = []

#     for tool_call in tool_calls:
#         tool_name = tool_call['name']
#         args = tool_call['args']
#         # 执行工具
#         result = await execute_tool(tool_name, args)

#         # 记录审计日志
#         await log_tool_usage(
#             user_id=user_data['id'],
#             group_id=group_data['id'],
#             message_id=message_id,
#             tool_name=tool_name,
#             args=args,
#             result=result
#         )

#         tool_responses.append({
#             "role": "tool",
#             "name": tool_name,
#             "content": json.dumps(result),
#             "tool_call_id":tool_call['id']
#         })

#     return tool_responses


async def long_answer(
    model_message_id,
    group_data,
    question,
    llm_model,
    group_collection,
    cite_id,
    user_data,
    recent_files,
    web_search=False,
    _=None
):
    try:
        question = resolve_relative_time(question)
        manage_db = await get_database()
        eval_db = await get_eval_database()
        # 获取模型并生成响应
        if _(settings.DEFAULT_GROUP_ASSISTANT_NAME) in llm_model:
            llm_model = _("通义千问-长文本（收费）")

        llm, max_token = get_llm(llm_model, {},_=_)
        # 首先获取群的recent_files列表
        # recent_files = group_data.get("recent_files", [])
        # 清理recent_files中类型为图片的文件
        recent_files = [
            file for file in recent_files if not file["type"].startswith("image")
        ]

        # 处理多种类型文件
        document_context = []
        for recent_file in recent_files:
            file_record = await manage_db.files.find_one(
                {"id": recent_file["id"], "is_deleted": False}
            )
            if not file_record:
                continue

            # 下载并识别文件
            local_path = file_record.get("local_file_path")
            if not local_path or not os.path.exists(local_path):
                local_path = await download_file(file_record["cloud_file_path"])

            if recent_file["type"].startswith("audio/"):
                # 音频文件处理分支
                try:
                    # 读取音频文件内容
                    with open(local_path, "rb") as f:
                        audio_data = f.read()

                    # 调用语音识别
                    transcriber = VoiceTranscriber()
                    content = await asyncio.to_thread(
                        transcriber.transcribe_audio, audio_data
                    )
                    document_context.append(_("【语音内容】{content}").format(content=content))
                except Exception as e:
                    logger.warning(f"语音识别失败: {str(e)}")
                    continue
            else:
                # 原有文件识别逻辑
                content = await identify_unstructured_file(local_path, _)
                document_context.append(f"{content}")

        # 从群的agent_setting的role项获取系统提示词
        role = group_data["agent_setting"]["role"]
        if not role:
            role = _("你是多模态分析模型,请根据提供的文档进行分析回答。")

        # 构建消息结构
        prompt = [
            {
                "role": "system",
                "content": role,
            },
            {
                "role": "user",
                "content": _("文档内容:{}").format(' '.join(document_context)),
            },
            {
                "role": "user",
                "content": _("`用户的问题`:{}").format(question),
            },
        ]
        # 切割上下文
        messages, cut_off = await cut_off_context(prompt, max_token)
        if cut_off:
            # 当上下文截断时,输出提示
            yield _("由于上下文过长,已截断部分上下文。") + "\n"
        # logger.info(f"{messages}")

        response = ""
        with get_openai_callback() as callback:
            async for chunk in llm.astream(messages):
                status = await group_collection.find_one({"id": model_message_id})
                if status.get("stop", False):
                    break
                yield chunk.content if hasattr(chunk, "content") else str(chunk)

            # 记录用户消耗token数
            logger.info(f"{cite_id or '无引用消息'}对话token消耗情况:{callback}")
            await manage_db.users.update_one(
                {"id": user_data.id},
                {"$inc": {"user_service.token_usage": callback.total_tokens}},
            )

    except ValueError as e:
        logger.error(f"Answer Error occurred: {e}", exc_info=True)
        for text in _("{error},请重新选择模型").format(error=str(e)):
            yield text
    except Exception as e:
        logger.error(f"Answer Error occurred: {e}", exc_info=True)
        if "not enough quota" in str(e):
            response = _("公有云账号余额不足，请联系管理员充值。")
        elif "too long" in str(e):
            response = _("上下文过长，模型暂无法处理，正在优化中。")
        elif "authorize" in str(e):
            response = _("模型密钥认证失败，请检查密钥是否正确。")
        elif "invalid model" in str(e):
            response = _("模型名称错误，请检查模型名称是否正确。")
        else:
            response = _("获取大模型回复异常，请稍后再试。")
        for text in response:
            yield text

    finally:
        await eval_db.records.insert_one(
            {
                "message_id": cite_id,
                "model": llm.model_name,
                "question": question,
                "context": document_context,  # 添加上下文内容
                "answer": response,
            }
        )


async def vl_answer(
    model_message_id,
    group_data,
    question,
    llm_model,
    group_collection,
    cite_id,
    user_data,
    recent_files,
    web_search=False,
    _=None
):
    try:
        question = resolve_relative_time(question)
        manage_db = await get_database()
        eval_db = await get_eval_database()

        # 首先获取群的recent_files列表
        # recent_files = group_data.get("recent_files", [])
        # 清理recent_files中类型不为图片的文件
        recent_files = [
            file for file in recent_files if file["type"].startswith("image")
        ]

        # 处理多张图片
        image_contents = []
        skipped_images = []  # 存储被跳过的图片序号
        for idx, recent_file in enumerate(recent_files, 1):
            file_record = await manage_db.files.find_one(
                {"id": recent_file["id"], "is_deleted": False}
            )
            if not file_record:
                continue

            if file_record["file_size"] > 10 * 1024 * 1024:
                logger.warning(f"图片 {recent_file['id']} 超过10MB,跳过处理")
                skipped_images.append(str(idx))
                continue

            # 下载并编码图片
            local_path = file_record["local_file_path"]
            if not os.path.exists(local_path):
                local_path = await download_file(file_record["cloud_file_path"])

            with open(local_path, "rb") as f:
                base64_image = base64.b64encode(f.read()).decode("utf-8")
                file_type = (
                    file_record["file_type"].split("/")[-1].replace("jpeg", "jpg")
                )

                image_contents.append(
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/{file_type};base64,{base64_image}"
                        },
                    }
                )

        # 添加跳过提示到问题中
        if skipped_images:
            skip_notice = _("（系统提示：第{images}张图片因超过10MB限制已跳过）").format(images=','.join(skipped_images)) + "\n"
            question = skip_notice + question

        # 从群的agent_setting的role项获取系统提示词
        role = group_data["agent_setting"]["role"]
        if not role:
            role = _("你是图片识别模型,请根据图片内容和问题,输出回答。")

        # 构建消息结构
        messages = [
            {
                "role": "system",
                "content": [
                    {
                        "type": "text",
                        "text": role,
                    }
                ],
            },
            {
                "role": "user",
                "content": image_contents + [{"type": "text", "text": question}],
            },
        ]
        # 获取模型并生成响应
        llm, extra = get_llm(_("通义千问-视觉（收费）"), {})
        logger.info(f"{messages}")

        response = ""
        with get_openai_callback() as callback:
            async for chunk in llm.astream(messages):
                status = await group_collection.find_one({"id": model_message_id})
                if status.get("stop", False):
                    break
                yield chunk.content if hasattr(chunk, "content") else str(chunk)

            # 记录用户消耗token数
            logger.info(f"{cite_id or '无引用消息'}对话token消耗情况:{callback}")
            await manage_db.users.update_one(
                {"id": user_data.id},
                {"$inc": {"user_service.token_usage": callback.total_tokens}},
            )

    except ValueError as e:
        logger.error(f"Answer Error occurred: {e}", exc_info=True)
        for text in _("{error},请重新选择模型").format(error=str(e)):
            yield text
    except Exception as e:
        logger.error(f"Answer Error occurred: {e}", exc_info=True)
        if "not enough quota" in str(e):
            response = _("公有云账号余额不足,请联系管理员充值。")
        elif "too long" in str(e):
            response = _("上下文过长,模型暂无法处理,正在优化中。")
        elif "authorize" in str(e):
            response = _("模型密钥认证失败，请检查密钥是否正确。")
        elif "invalid model" in str(e):
            response = _("模型名称错误，请检查模型名称是否正确。")
        else:
            response = _("获取大模型回复异常，请稍后再试。")
        for text in response:
            yield text

    finally:
        await eval_db.records.insert_one(
            {
                "message_id": cite_id,
                "model": llm.model_name,
                "question": question,
                "context": image_contents,  # 添加上下文内容
                "answer": response,
            }
        )


async def truncate_tool_messages(messages, max_token):
    """截断tool角色的内容，优先保留最近的消息"""
    total_tokens = 0
    tool_messages = []
    other_messages = []
    latest_user_message = []
    def messages_count_tokens(content):
        """处理不同类型内容的token计算"""
        if isinstance(content, (dict, list)):
            try:
                processed_content = json.dumps(content, ensure_ascii=False)
            except:
                processed_content = str(content)
        elif isinstance(content, str):
            processed_content = content
        else:
            processed_content = str(content)
        return len(tokenizer.encode(processed_content))

    # 分离tool消息和其他消息
    for msg in messages:
        tokens = messages_count_tokens(msg["content"])
        if msg["role"] == "tool":
            tool_messages.append({"msg": msg, "tokens": tokens})
        else:
            if msg["role"] == "user":
                user_message = msg
            other_messages.append(msg)
            total_tokens += tokens
    else:
        latest_user_message.append(user_message)
        other_messages.remove(user_message)

    # 按时间倒序处理（最新消息最后）
    tool_messages = tool_messages[::-1]

    remaining_tokens = max_token - total_tokens
    final_tools = []

    # 新版截断逻辑
    for tool in tool_messages:
        if remaining_tokens <= 0:
            break

        if tool["tokens"] <= remaining_tokens:
            # 完整保留该tool
            final_tools.append(tool["msg"])
            remaining_tokens -= tool["tokens"]
        else:
            # 只截断该tool内容
            content_str = (
                json.dumps(tool["msg"]["content"], ensure_ascii=False)
                if isinstance(tool["msg"]["content"], (dict, list))
                else str(tool["msg"]["content"])
            )
            content_tokens = tokenizer.encode(content_str)

            # 计算可保留的token数
            keep_tokens = min(len(content_tokens), remaining_tokens)
            truncated_content = tokenizer.decode(content_tokens[:keep_tokens])

            # 创建截断后的消息
            truncated_msg = {**tool["msg"], "content": f"(截断){truncated_content}..."}
            final_tools.append(truncated_msg)
            remaining_tokens = 0  # 用完剩余token

    # 重组消息列表，保持时间顺序
    truncated_messages = other_messages + final_tools[::-1] + latest_user_message

    return truncated_messages, (len(truncated_messages) < len(messages))
