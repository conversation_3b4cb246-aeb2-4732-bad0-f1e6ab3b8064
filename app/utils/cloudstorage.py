import traceback


from minio import <PERSON>o

from ..config import settings
from ..utils.log import logger


class MinIOClient:
    def __init__(self, ak, sk, server, bucket_name, **kwargs):
        self.MinIOClient = Minio(
            access_key=ak, secret_key=sk, endpoint=server, **kwargs
        )
        self.bucket_name = bucket_name
        if not self.MinIOClient.bucket_exists(self.bucket_name):
            self.MinIOClient.make_bucket(self.bucket_name)

    def upload_file(self, local_file_path, cloud_file_path):
        """
        :param local_file_path: 本地文件路径
        :param cloud_file_path: 云储存路径(用户uuid/文档id)
        :return:
        """
        try:
            result = self.MinIOClient.fput_object(
                self.bucket_name, cloud_file_path, local_file_path
            )
            logger.info(f"Put File {cloud_file_path} Succeeded")
        except Exception as e:
            logger.error(
                f"Put File {cloud_file_path} Failed({str(e)}): {traceback.format_exc()}"
            )

    def download_file(self, cloud_file_path, local_file_path):
        """
        :param cloud_file_path: 云储存路径(用户uuid/文档id)
        :param local_file_path: 本地文件路径
        :return:
        """
        try:
            result = self.MinIOClient.fget_object(
                self.bucket_name, cloud_file_path, local_file_path
            )
            logger.info(f"Get File {cloud_file_path} Succeeded")
            return True
        except Exception as e:
            logger.error(
                f"Get File {cloud_file_path} Failed({str(e)}): {traceback.format_exc()}"
            )
            return False

    def delete_file(self, cloud_file_path):
        """
        :param cloud_file_path: 云储存路径(用户uuid/文档id)
        :return: 是否删除成功
        """
        try:
            self.MinIOClient.remove_object(self.bucket_name, cloud_file_path)
            logger.info(f"Delete File {cloud_file_path} Succeeded")
            return True
        except Exception as e:
            logger.error(
                f"Delete File {cloud_file_path} Failed({str(e)}): {traceback.format_exc()}"
            )
            return False


qicaioss = MinIOClient(
    settings.HWC_ID,
    settings.HWC_KEY,
    settings.HWC_BUCKET_ENDPOINT,
    settings.HWC_BUCKET_NAME,
    secure=False,
)
