import re

from ..database import get_chat_database, get_database
from ..schemas.message import MessageData


async def name_group_search(group_name: str, current_user):
    # 通过群名查找群
    manage_db = await get_database()

    escaped_name = re.escape(group_name)
    regex_pattern = f".*{escaped_name}.*"
    query = {
        "member_ids": current_user.id,
        "name": {"$regex": regex_pattern, "$options": "i"},
        "is_dissolved": False,
    }

    groups = await manage_db.groups.find(query).to_list(None)

    return groups


async def user_group_search(user_name: str, current_user):
    # 通过用户名查找群
    manage_db = await get_database()

    # 首先从manage_db.users中找到符合user_name的模糊搜索信息
    escaped_name = re.escape(user_name)
    regex_pattern = f".*{escaped_name}.*"
    query_user = {
        "username": {"$regex": regex_pattern, "$options": "i"},
        "is_active": True,
    }

    users = await manage_db.users.find(query_user).to_list(None)

    query_group = {
        "$and": [
            {"member_ids": current_user.id},  # 当前用户必须在群成员中
            {
                "member_ids": {"$in": [user["id"] for user in users]}
            },  # 群成员包含搜索结果用户
        ],
        "is_dissolved": False,
    }

    groups = await manage_db.groups.find(query_group).to_list(None)

    return groups


async def message_group_search(message: str, current_user):
    # 通过聊天记录查找群
    manage_db = await get_database()
    chat_db = await get_chat_database()

    escaped_name = re.escape(message)
    regex_pattern = f".*{escaped_name}.*"
    query = {"content": {"$regex": regex_pattern, "$options": "i"}}

    # 首先，找到所有用户在的群组
    query_my_group = {
        "member_ids": current_user.id,
        "is_dissolved": False,
    }
    my_groups = await manage_db.groups.find(query_my_group).to_list(None)

    groups = []
    # 最后，在chat_collections中查找符合message的模糊搜索信息
    for my_group in my_groups:
        chat_collection = getattr(chat_db, my_group.get("id"))
        messages = await chat_collection.find(query).to_list(None)
        if messages:
            groups.append(my_group)

    return groups


async def group_message_search(message_content: str, group_id: str):
    # 在群内查找聊天记录
    chat_db = await get_chat_database()

    escaped_name = re.escape(message_content)
    regex_pattern = f".*{escaped_name}.*"
    query = {"content": {"$regex": regex_pattern, "$options": "i"}}

    chat_collection = getattr(chat_db, group_id)
    messages = await chat_collection.find(query).sort("created_at", -1).to_list(None)

    return messages


async def database_file_search(file_name: str, database_id: str):
    # 在数据库中查找文件
    manage_db = await get_database()

    escaped_name = re.escape(file_name)
    regex_pattern = f".*{escaped_name}.*"
    # 寻找vectordb_id符合database_id的，并且name符合regex_pattern的文件
    query = {
        "vectordb_id": database_id,
        "file_name": {"$regex": regex_pattern, "$options": "i"},
    }

    files = await manage_db.files.find(query).to_list(None)

    return files


async def name_database_search(database_name: str, current_user):
    # 通过数据库名查找数据库
    manage_db = await get_database()

    escaped_name = re.escape(database_name)
    regex_pattern = f".*{escaped_name}.*"
    query = {
        "member_ids": current_user.id,
        "name": {"$regex": regex_pattern, "$options": "i"},
    }

    databases = await manage_db.databases.find(query).to_list(None)

    return databases


async def message_search(message_content: str, current_user):
    # 在所有群内查找聊天记录
    chat_db = await get_chat_database()
    manage_db = await get_database()

    escaped_name = re.escape(message_content)
    regex_pattern = f".*{escaped_name}.*"
    query = {"content": {"$regex": regex_pattern, "$options": "i"}}

    all_messages = []
    groups = await manage_db.groups.find(
        {"member_ids": current_user.id, "is_dissolved": False}
    ).to_list(None)
    for group in groups:
        chat_collection = getattr(chat_db, group.get("id"))
        messages = [
            MessageData(**message).model_dump()
            for message in await chat_collection.find(query).to_list(None)
        ]
        for message in messages:
            message['group_name'] = group.get('name')
        all_messages.extend(messages)

    return all_messages
