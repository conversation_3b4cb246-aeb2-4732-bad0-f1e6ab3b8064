import asyncio
from enum import Enum

from langchain.prompts import PromptTemplate

from ..utils.log import logger
from ..utils.remote_model import system_graphbase_manager
from .graphbase import AsyncGraphDatabase
from .remote_model import reranker_model
import jieba
from collections import OrderedDict
from .to_jsonl import get_triplet_from_text

# 定义枚举类
class PromptType(Enum):
    BASIC = "basic"
    DETAILED = "detailed"
    TECHNICAL = "technical"


# 定义不同的Prompt模板
def get_prompt_template(prompt_type: PromptType) -> PromptTemplate:
    if prompt_type == PromptType.BASIC:
        return PromptTemplate(
            template="""
            系统：你是专业的知识库专家。

            参考资料：
            {context}

            用户问题：
            {question}

            输出要求：
            1. 内容完整详实
            2. 表述精简准确
            3. 结构清晰有序
            4. 使用专业术语

            专家回答：
            """,
            input_variables=["context", "question"],
        )
    elif prompt_type == PromptType.DETAILED:
        return PromptTemplate(
            template="""
            系统：你是专业的方案撰写专家。

            参考资料：
            {context}

            用户问题：
            {question}

            输出要求：
            1. 内容完整详实
            2. 字数不少于500字
            3. 结构清晰有序
            4. 使用专业术语

            专家回答：
            """,
            input_variables=["context", "question"],
        )
    elif prompt_type == PromptType.TECHNICAL:
        return PromptTemplate(
            template="""
            角色：技术专家

            背景资料：
            {context}

            技术问题：
            {question}

            输出要求：
            1. 技术细节明确
            2. 使用行业术语
            3. 提供解决方案

            技术回答：
            """,
            input_variables=["context", "question"],
        )
    else:
        raise ValueError(f"未定义的Prompt类型: {prompt_type}")


class GraphQueryHandler:
    """图数据库查询处理器"""

    def __init__(self, group_data, question):
        self.group_data = group_data
        self.question = question
        self.results = []
        self.doc_sequences = {}

    async def process_database(self, database_id: str) -> list:
        """处理单个图数据库查询"""

        def extract_pattern(pattern):
            """
            提取三元组
            """
            triplets = []
            if len(pattern) == 0:
                return triplets
            if len(pattern) == 3:
                h = pattern[0]
                r = pattern[1]
                t = pattern[2]
                if h and r and t:
                    triplets.append({"h": h, "r": r, "t": t})
            else:
                for p in pattern:
                    if isinstance(p,dict):
                        h = p.get("h")
                        r = p.get("r")
                        t = p.get("t")
                        if h and r and t:
                            triplets.append({"h": h, "r": r, "t": t})
                    elif isinstance(p, list) and len(p) == 3:
                        h = p[0]
                        r = p[1]
                        t = p[2]
                        if h and r and t:
                            triplets.append({"h": h, "r": r, "t": t})
            return triplets


        if not await system_graphbase_manager.check_graph_database_exists(database_id):
            return []

        graphbase = None  # 声明变量用于finally访问
        try:
            graphbase = AsyncGraphDatabase(database_id)
            # 当数据库为空时，返回空列表
            if await graphbase.is_database_empty():
                return []
            prompt = [
                        {
                            "role": "system",
                            "content": """你是一个专业的问题分析器，请将用户问题转换为知识图谱查询模式。
                            查询模式由三元组构成，使用?表示未知项。
                            示例：
                            问题："张三的父亲是谁？"
                            模式：[["张三", "父亲", "?"]]

                            问题："谁是李四的学生？"  
                            模式：[["?", "学生", "李四"]]"""
                        },
                        {
                            "role": "user",
                            "content": f"请将下面的问题转换为知识图谱查询模式：\n{self.question}"
                        }
                    ]
            # pattern = await get_triplet_from_text(self.question,prompt)
            # pattern = extract_pattern(pattern)
            # if not pattern:
            #     logger.error(f"问题 {self.question} 无法转换为三元组模式")
            #     return []
            logger.debug(f"开始查询数据库 {database_id}...")
            # result = await graphbase.query_by_pattern(pattern)
            result = await graphbase.query_by_vector(self.question)
            logger.debug(f"查询数据库 {database_id}结束")
            return result
        except Exception as e:
            logger.error(f"数据库 {database_id} 查询失败: {e}")
            return []
        finally:
            if graphbase:
                await graphbase.close()  # 新增关闭连接

    
    def extract_triplets_from_record(self,record,db_id) -> list:
        """改进的三元组提取方法"""
        triplets = []
        seen = set()

        try:
            # 获取路径中的所有节点和关系
            relationships = record[1]

            # 遍历关系构建完整路径
            for rel in relationships:
                # 获取关系的实际两端节点
                source = rel.start_node
                target = rel.end_node

                # 提取准确的关系方向
                h_value = source._properties.get("name", "Unknown")
                h = str(h_value).strip()
                t_value = target._properties.get("name", "Unknown")
                t = str(t_value).strip()
                r_value = rel._properties.get("type", rel.type)
                r = str(r_value).strip()
                doc_sequence = rel._properties.get("doc_sequence", "")
                if doc_sequence and doc_sequence not in self.doc_sequences.get(db_id,[]):
                    self.doc_sequences[db_id].append(doc_sequence)

                # 有效性校验
                if len(r) == 0:
                    continue

                # 创建唯一标识并去重
                identifier = (h, r, t)
                if identifier not in seen:
                    seen.add(identifier)
                    triplets.append({"h": h, "r": r, "t": t})

        except Exception as e:
            logger.error(f"记录解析失败: {e}")

        return triplets

    async def get_rerank_scores(self, question, entities):
        """使用重排模型计算实体相关性分数"""
        try:
            return await asyncio.to_thread(
                reranker_model.compute_score, question, entities
            )
        except Exception as e:
            logger.error(f"重排模型计算分数失败: {e}")
            return []

    async def execute(self,top_k):
        """优化后的执行流程，包含新的评分系统"""
        try:
            database_ids = self.group_data["database_ids"]
            semaphore = asyncio.Semaphore(20)

            # 存储每个分片的评分信息
            chunk_scores = {}  # doc_sequence -> final_score
            # 存储关键词，用于相关性计算
            # keywords = self.question.split()  # 简单分词，实际应使用更好的分词器
            keywords = list(jieba.cut(self.question))
            async def limited_task(db_id):
                async with semaphore:
                    return await self.process_database(db_id)

            tasks = [limited_task(db_id) for db_id in database_ids]
            database_results = await asyncio.gather(*tasks)
            db_chunks = {}
            # 处理查询结果
            for db_id, result in zip(database_ids, database_results):
                self.doc_sequences[db_id] = {}
                db_chunks[db_id] = []
                for record in result:
                    
                    # 获取路径中的所有节点和关系
                    relationships = record[1]
                    
                    for rel in relationships:
                        # 获取节点和关系信息
                        source = rel.start_node
                        target = rel.end_node
                        doc_sequence = rel._properties.get("doc_sequence", "")
                        
                        if not doc_sequence:
                            continue
                        db_chunks[db_id].append(doc_sequence)
                        # 1. 计算节点相关性分数 (N)
                        source_text = str(source._properties.get("name", "")) + " " + \
                                    str(source._properties.get("description", ""))
                        target_text = str(target._properties.get("name", "")) + " " + \
                                    str(target._properties.get("description", ""))
                        node_text = f"{source_text} {target_text}".lower()
                        
                        matched_keywords = sum(1 for k in keywords if k.lower() in node_text)
                        node_score = matched_keywords / len(keywords)  # N score

                        # 2. 计算边相关性分数 (E)
                        edge_text = str(rel._properties.get("type", "")) + " " + \
                                str(rel._properties.get("description", "")).lower()
                        edge_matched = sum(1 for k in keywords if k.lower() in edge_text)
                        edge_score = edge_matched / len(keywords)  # E score

                        # 3. 确定边权重 (W) - 可以基于边类型设置不同权重
                        edge_weight = self._get_edge_weight(rel._properties.get("type", ""))

                        # 4. 计算单条路径得分
                        path_score = (0.5 * node_score + 0.5 * edge_score) * edge_weight

                        # 5. 累积分片得分（多路径聚合）
                        if doc_sequence not in chunk_scores:
                            chunk_scores[doc_sequence] = 0.0
                        chunk_scores[doc_sequence] = min(1.0, chunk_scores[doc_sequence] + path_score)
                        
            # 按分数排序分片
            sorted_chunks = OrderedDict(sorted(chunk_scores.items(), key=lambda x: x[1], reverse=True)[:top_k])            
            for k,v in db_chunks.items():
                for chunk_id in v:
                    if chunk_id in sorted_chunks:
                        self.doc_sequences[k][chunk_id] = sorted_chunks[chunk_id]

            # 构建返回结果
            result = [{
                    "doc_sequence": chunk_id,
                    "score": score,
                } for chunk_id, score in sorted_chunks.items()]
            # result  = []
            return result

        except Exception as e:
            logger.error(f"知识图谱查询失败：{e}", exc_info=True)
            return {"chunks": [], "total_chunks": 0, "max_score": 0}

    def _get_edge_weight(self, edge_type: str) -> float:
        """根据边类型返回权重"""
        # 这里可以根据业务需求定制权重
        weights = {
            "创建": 1.0,
            "拥有": 1.0,
            "参与": 0.9,
            "相关": 0.8,
            "引用": 0.7,
            # 可以添加更多边类型的权重
        }
        return weights.get(edge_type, 0.7)  # 默认权重为0.7
