import json
import aiohttp
from ..utils.log import logger
from ..config import settings
from tavily import TavilyClient

class BoChaSearch:
    """
    BoChaSearch 类用于调用 BoCha AI 的网络搜索 API
    """

    def __init__(self, api_key=settings.WEB_API_KEY):
        """
        初始化 BoChaSearch 类

        Args:
            api_key (str): BoCha AI API 密钥
            log_level (int): 日志级别，默认为 INFO
        """
        self.api_url = settings.WEB_API_URL
        self.api_key = api_key
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

    async def search(self, query, freshness="noLimit", summary=True, count=10):
        """
        执行网络搜索

        Args:
            query (str): 搜索查询语句
            freshness (str): 搜索结果的时间范围，默认为 "noLimit"
            summary (bool): 是否生成摘要，默认为 True
            count (int): 返回结果数量，默认为 10

        Returns:
            dict: 搜索结果
        """
        logger.info(f"开始搜索: '{query}', 参数: freshness={freshness}, summary={summary}, count={count}")

        payload = {
            "query": query,
            "freshness": freshness,
            "summary": summary,
            "count": count
        }

        try:
            logger.debug(f"发送请求到 {self.api_url}")
            async with aiohttp.ClientSession() as session:
                async with session.post(
                        self.api_url,
                        headers=self.headers,
                        data=json.dumps(payload)
                ) as response:
                    if response.status != 200:
                        error_msg = f"HTTP错误: {response.status}"
                        # logger.error(error_msg)
                        return {"error": error_msg}

                    logger.debug("请求成功，正在解析响应")
                    result = await response.json()
                    logger.info(f"搜索完成，状态码: {result.get('code', 'unknown')}")
                    return result
        except Exception as e:
            error_msg = f"搜索过程中发生错误: {str(e)}"
            logger.error(error_msg)
            return {"error": str(e)}

    async def pretty_search_results(self, results):
        """
        将搜索结果处理为结构化的 JSON 数据并返回

        Args:
            results (dict): 搜索结果

        Returns:
            dict: 处理后的结构化数据
        """
        logger.info("开始处理搜索结果")

        processed_results = {
            "status": {},
            "query_info": {},
            "search_results": []
        }

        # 处理错误情况
        if "error" in results:
            error_msg = f"处理结果时发现错误: {results['error']}"
            logger.error(error_msg)
            processed_results["status"] = {
                "success": False,
                "error_message": results["error"]
            }
            return processed_results

        # 检查响应状态
        if results.get("code") != 200:
            error_msg = f"API返回非成功状态码: {results.get('code')}, 消息: {results.get('msg')}"
            logger.warning(error_msg)
            processed_results["status"] = {
                "success": False,
                "code": results.get("code"),
                "message": results.get("msg")
            }
            return processed_results

        # 设置成功状态
        processed_results["status"] = {
            "success": True,
            "code": results.get("code")
        }

        # 获取数据部分
        data = results.get("data", {})

        # 处理查询信息
        query_context = data.get("queryContext", {})
        processed_results["query_info"] = {
            "original_query": query_context.get("originalQuery", ""),
        }

        # 处理网页搜索结果
        web_pages = data.get("webPages", {})
        total_matches = web_pages.get("totalEstimatedMatches", 0)
        processed_results["query_info"].update({
            "total_matches": total_matches,
            "search_url": web_pages.get("webSearchUrl", "")
        })

        logger.info(f"找到约 {total_matches} 个匹配结果")

        # 处理搜索结果列表
        web_results = web_pages.get("value", [])
        logger.info(f"处理 {len(web_results)} 条搜索结果")

        for result in web_results:
            processed_results["search_results"].append({
                "title": result.get("name", ""),
                "url": result.get("url", ""),
                "snippet": result.get("snippet", ""),
                "summary": result.get("summary", ""),
                "site_name": result.get("siteName", ""),
                "site_icon": result.get("siteIcon", ""),
                "date_crawled": result.get("dateLastCrawled", "")
            })

        logger.info("搜索结果处理完成")
        return processed_results['search_results']



class TavilySearch:
    """
    TavilySearch 类，用于调用 Tavily 搜索 API，兼容 BoChaSearch 的原接口
    """

    def __init__(self, api_key=settings.EN_WEB_API_KEY):
        self.client = TavilyClient(api_key=api_key)

    async def search(self, query, freshness="noLimit", summary=True, count=10):
        """
        执行 Tavily 搜索（兼容原参数）

        Args:
            query (str): 搜索语句
            freshness (str): 时间限制（目前 Tavily 不支持，可忽略或扩展）
            summary (bool): 是否启用总结
            count (int): 返回结果条数

        Returns:
            dict: 与原 BoChaSearch 返回结构类似
        """
        logger.info(f"开始搜索: '{query}', 参数: freshness={freshness}, summary={summary}, count={count}")
        try:
            result = self.client.search(
                query=query,
                search_depth="advanced" if summary else "basic",
                max_results=count,
                include_answer=summary
            )
            logger.info("搜索完成")
            return {"code": 200, "data": result}
        except Exception as e:
            error_msg = f"Tavily 搜索出错: {str(e)}"
            logger.error(error_msg)
            return {"error": error_msg}

    async def pretty_search_results(self, results):
        """
        将搜索结果处理为结构化 JSON 列表

        Args:
            results (dict): 搜索结果

        Returns:
            list: 搜索结果结构
        """
        logger.info("开始处理搜索结果")

        processed_results = {
            "status": {},
            "query_info": {},
            "search_results": []
        }

        if "error" in results:
            processed_results["status"] = {
                "success": False,
                "error_message": results["error"]
            }
            return processed_results

        data = results.get("data", {})
        web_results = data.get("results", [])

        processed_results["status"] = {"success": True, "code": 200}
        processed_results["query_info"] = {
            "original_query": data.get("query", ""),
            "answer": data.get("answer", ""),
            "total_matches": len(web_results),
            "search_url": ""  # Tavily 无此字段，可补充为 "" 或构造链接
        }

        for item in web_results:
            processed_results["search_results"].append({
                "title": item.get("title", ""),
                "url": item.get("url", ""),
                "snippet": item.get("content", ""),
                "summary": data.get("answer", "") if data.get("answer") else "",
                "site_name": item.get("source", ""),
                "site_icon": "",
                "date_crawled": item.get("published_date", "")
            })

        logger.info(f"处理完成，共 {len(web_results)} 条结果")
        return processed_results["search_results"]


# # 使用示例
# async def main():
#     searcher = BoChaSearch()
#     query = "五一假期安排"
#     # logger.info(f"执行搜索: {query}")
#
#     results = await searcher.search(query)
#     processed_results = await searcher.pretty_search_results(results)
#
#     # logger.info("输出处理后的结果")
#     print(json.dumps(processed_results, ensure_ascii=False, indent=2))
#
#     # logger.info("程序执行完毕")
#
#
# if __name__ == "__main__":
#     asyncio.run(main())
