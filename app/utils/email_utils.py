from email.mime.multipart import MIME<PERSON>ultipart
from email.mime.text import MIMEText

from aiosmtplib import SMTP

from ..config import settings
from ..utils.log import logger


async def send_verification_email(to_email: str, code: str):
    try:
        # 创建邮件内容
        message = MIMEMultipart()
        message["From"] = settings.MAIL_FROM
        message["To"] = to_email
        message["Subject"] = "验证码"

        # 邮件正文
        body = f"""
        <html>
            <body>
                <h2>您的验证码</h2>
                <p>您的验证码是: <strong>{code}</strong></p>
                <!-- <p>验证码有效期为5分钟，请尽快使用。</p> -->
            </body>
        </html>
        """
        message.attach(MIMEText(body, "html"))

        # 连接SMTP服务器
        async with SMTP(hostname=settings.MAIL_SERVER, port=settings.MAIL_PORT, use_tls=True) as smtp:
            await smtp.login(settings.MAIL_USERNAME, settings.MAIL_PASSWORD)
            res = await smtp.send_message(message)
        logger.info(f"发送{to_email}邮件结果: {res}")
        return True
    except Exception as e:
        logger.error(f"发送邮件时出错: {str(e)}", exc_info=True)
        raise Exception("发送验证码失败，请稍后重试")
