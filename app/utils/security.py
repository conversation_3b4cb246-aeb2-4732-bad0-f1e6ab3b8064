import base64
from datetime import datetime, timedelta

import bcrypt
from cryptography.hazmat.primitives import padding
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from fastapi import Depends, HTTPException, Request, Response
from jose import jwt
from pydantic import BaseModel, ValidationError

from ..config import settings

ALGORITHM = "HS256"


def verify_password(plain_password: str, hashed_password: str):
    # 将明文密码转换为字节
    password_bytes = plain_password.encode("utf-8")
    # 将哈希密码从字符串转换为字节
    hashed_bytes = hashed_password.encode("utf-8")
    try:
        # 使用bcrypt直接验证
        return bcrypt.checkpw(password_bytes, hashed_bytes)
    except Exception:
        return False


def get_password_hash(password: str):
    # 生成salt并对密码进行哈希
    password_bytes = password.encode("utf-8")
    salt = bcrypt.gensalt()
    hashed = bcrypt.hashpw(password_bytes, salt)
    # 将字节转换为字符串存储
    return hashed.decode("utf-8")


def create_access_token(data: dict):
    to_encode = data.copy()
    expire = datetime.utcnow() + timedelta(days=7)
    to_encode.update({"exp": expire})
    return jwt.encode(to_encode, settings.SECRET_KEY, algorithm=ALGORITHM)


def encrypt_data(data: str) -> str:
    """
    AES-256-CBC加密请求数据
    """
    key = settings.SECRET_ENCRYPTION_KEY.encode()  # 从配置获取32字节密钥
    iv = settings.SECRET_IV.encode()  # 16字节初始化向量

    padder = padding.PKCS7(128).padder()
    padded_data = padder.update(data.encode()) + padder.finalize()

    cipher = Cipher(algorithms.AES(key), modes.CBC(iv))
    encryptor = cipher.encryptor()
    ct = encryptor.update(padded_data) + encryptor.finalize()
    return base64.b64encode(ct).decode()


def decrypt_data(encrypted_data: str) -> str:
    """
    AES-256-CBC解密请求数据
    """
    key = settings.SECRET_ENCRYPTION_KEY.encode()
    iv = settings.SECRET_IV.encode()

    cipher = Cipher(algorithms.AES(key), modes.CBC(iv))
    decryptor = cipher.decryptor()
    decrypted_padded = (
        decryptor.update(base64.b64decode(encrypted_data)) + decryptor.finalize()
    )

    unpadder = padding.PKCS7(128).unpadder()
    data = unpadder.update(decrypted_padded) + unpadder.finalize()
    return data.decode()


def DecryptedBody(model: type[BaseModel]):
    async def decrypt_dependency(request: Request):
        try:
            encrypted_data = await request.body()
            decrypted_str = decrypt_data(encrypted_data)
            return model.model_validate_json(decrypted_str)
        except (base64.binascii.Error, ValueError, UnicodeDecodeError) as e:
            raise HTTPException(400, detail="解密失败") from e
        except ValidationError as e:
            raise HTTPException(422, detail=e.errors()) from e

    return Depends(decrypt_dependency)


from fastapi.routing import APIRoute


class EncryptedAPIRoute(APIRoute):
    def get_route_handler(self):
        original_route_handler = super().get_route_handler()

        async def custom_route_handler(request: Request):
            # 仅当接口有加密标记时才加密响应
            if getattr(self.endpoint, "__encrypt_response__", False):
                response = await original_route_handler(request)
                content = (
                    b"".join([chunk async for chunk in response.body_iterator])
                    if hasattr(response, "body_iterator")
                    else response.body
                )

                # 加密处理
                encrypted = encrypt_data(content.decode())
                return Response(
                    content=encrypted,
                    media_type="application/octet-stream",
                    status_code=response.status_code,
                )
            # 无加密标记的直接返回原始响应
            return await original_route_handler(request)

        return custom_route_handler


# 在需要加密的路由添加装饰器
def encrypt_response(func):
    func.__encrypt_response__ = True
    return func
