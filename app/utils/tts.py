import os

import dashscope
from dashscope.audio.tts_v2 import *

from ..config import settings
from ..utils.log import logger

dashscope.api_key = settings.ALITONGYI_SK


class TextToSpeech:
    def __init__(self, model="cosyvoice-v1", voice="longxiaochun"):
        self.synthesizer = SpeechSynthesizer(model=model, voice=voice)

    def generate_sound_file(self, text: str, save_path: str):
        try:
            audio = self.synthesizer.call(text)
            if not os.path.exists(os.path.dirname(save_path)):
                os.makedirs(os.path.dirname(save_path))
            with open(save_path, "wb") as f:
                f.write(audio)
        except Exception as e:
            logger.error(f"生成语音文件失败: {str(e)}", exc_info=True)


# tts_model = TextToSpeech()

# if __name__ == "__main__":
#     tts_model.generate_sound_file("你好，我是大Scope", "/data/deeptalk/8683ac60-92d6-4e5a-b864-762545a61c6b/eecaa563-9e5e-4320-82e1-8de7ff707c6b/1234.wav")
