import re
from datetime import datetime
from typing import Any, Dict, List

from ..database import get_database,get_chat_database
from ..utils.log import logger
from ..utils.web_search import Bo<PERSON>haSearch,TavilySearch  # 已有网络搜索模块
from ..utils.locales import get_translator
from ..utils.bm25 import BM25_MANAGER,RealTimeBM25Sreacher,BM25_FAQ_MANAGER
from datetime import datetime, timedelta
from langchain_milvus import <PERSON><PERSON><PERSON><PERSON>
from ..config import settings
from ..utils.remote_model import embedding_model, reranker_model
from .prompt import GraphQueryHandler
import asyncio
from langchain_core.documents import Document
from pymilvus import MilvusClient
import json
import ast
import os
import time
from ..models.message import Message
from ..schemas.message import MessageData
from .websocket_manager import connection_dic
from ..utils.file_handler import download_file, identify_file, identify_unstructured_file


def get_available_tools(_=None):
    """Get tool definitions with translated descriptions"""
    if _ is None:
        _ = lambda s: s  # Fallback function if no translator provided

    # This is a simplified version - in a real implementation, you would translate all tool descriptions
    # For now, we're just translating the web_search tool as an example
    return [
        {
            "type": "function",
            "function": {
                "name": "web_search",
                "description": _("当你想知道通过搜索引擎查询信息时非常有用。"),
                "strict": True,
                "parameters": {
                    "type": "object",
                    "properties": {
                        "search_content": {
                            "type": "string",
                            "description": _("搜索的信息"),
                        },
                    },
                    "required": ["search_content"],
                    "additionalProperties": False
                },
            },
        },
    ]

# 工具定义（需符合OpenAI Function Calling规范）
available_tools = [
    {
        "type": "function",
        "function": {
            "name": "web_search",
            "description": "当你想知道通过搜索引擎查询信息时非常有用。",
            "strict": True,
            "parameters": {
                "type": "object",
                "properties": {
                    "search_content": {
                        "type": "string",
                        "description": "搜索的信息",
                    },
                },
                "required": ["search_content"],
                "additionalProperties": False
            },
        },
    },
    # {
    #     "type": "function",
    #     "function": {
    #         "name": "search_database_file",
    #         "description": "搜索文档知识库。",
    #         "strict": True,
    #         "parameters": {
    #             "type": "object",
    #             "properties": {
    #                 "search_content": {
    #                     "type": "string",
    #                     "description": "文档知识库搜索的内容",
    #                 },
    #             },
    #             "required": ["search_content"],
    #             "additionalProperties": False
    #         },
    #     },
    # },
    # {
    #     "type": "function",
    #     "function": {
    #         "name": "get_group_recent_chat_history",
    #         "description": "当你想获取最近3分钟的聊天记录时非常有用。",
    #         "parameters": {
    #             "type": "object",
    #             "properties": {
    #             },
    #             "required": [],
    #         },
    #     },
    # },
    # {
    #     "type": "function",
    #     "function": {
    #         "name": "get_group_old_chat_history",
    #         "description": "搜索历史聊天记录。",
    #         "parameters": {
    #             "type": "object",
    #             "properties": {
    #                 "search_content": {
    #                     "type": "string",
    #                     "description": "聊天记录搜索的内容",
    #                 },
    #             },
    #             "required": ["search_content"],
    #         },
    #     },
    # },
    # {
    #     "type": "function",
    #     "function": {
    #         "name": "search_graph",
    #         "description": "搜索知识图谱，根据输入的搜索内容，返回知识图谱中与搜索内容相关的知识。",
    #         "strict": True,
    #         "parameters": {
    #             "type": "object",
    #             "properties": {
    #                 "search_content": {
    #                     "type": "string",
    #                     "description": "知识图谱搜索的内容",
    #                 },
    #             },
    #             "required": ["search_content"],
    #             "additionalProperties": False
    #         },
    #     },
    # },
    # {"type": "function",
    #     "function": {
    #         "name": "get_now_time",
    #         "description": "当你需要知道当前时间时非常有用。",
    #         "strict": True,
    #         "parameters": {
    #             "type": "object",
    #             "properties": {
    #                 "input": {
    #                     "type": "string",
    #                     "description": "输入"
    #                 }
    #             },
    #         },
    #         "required": ["input"],
    #         "additionalProperties": False
    #     }
    # },
    # {
    #     "type": "function",
    #     "function": {
    #         "name": "get_start_time",
    #         "description": "通过计算偏移后的时间，获取基于当前时间的开始时间时间。",
    #         "strict": True,
    #         "parameters": {  
    #             "type": "object",
    #             "properties": {
    #                 "base_time": {
    #                     "type": "string",
    #                     "description": "当前时间时间"
    #                 },
    #                 "years_offset": {
    #                     "type": "int",
    #                     "description": "年数偏移量(<0/>0)"
    #                 },
    #                 "months_offset": {
    #                     "type": "int",
    #                     "description": "月数偏移量(<0/>0)"
    #                 },
    #                 "weeks_offset": {
    #                     "type": "int",
    #                     "description": "周数偏移量(<0/>0)"
    #                 },
    #                 "days_offset": {
    #                     "type": "int",
    #                     "description": "天数偏移量(<0/>0)"
    #                 },
    #                 "hours_offset": {
    #                     "type": "int",
    #                     "description": "小时偏移量(<0/>0)"
    #                 },
    #                 "minutes_offset": {
    #                     "type": "int",
    #                     "description": "分钟偏移量(<0/>0)"
    #                 },
    #                 "seconds_offset": {
    #                     "type": "int",
    #                     "description": "秒偏移量(<0/>0)"
    #                 }
    #             }
    #         },
    #         "required": [
    #             "timestamp","years_offset","months_offset","weeks_offset","days_offset","hours_offset","minutes_offset","seconds_offset"
    #         ],
    #         "additionalProperties": False
    #     }
    # },
    # {
    #     "type": "function",
    #     "function": {
    #         "name": "get_end_time",
    #         "description": "通过计算偏移后的时间，获取基于当前时间的结束时间时间。",
    #         "strict": True,
    #         "parameters": {  
    #             "type": "object",
    #             "properties": {
    #                 "base_time": {
    #                     "type": "string",
    #                     "description": "当前时间"
    #                 },
    #                 "years_offset": {
    #                     "type": "int",
    #                     "description": "年数偏移量(<0/>0)"
    #                 },
    #                 "months_offset": {
    #                     "type": "int",
    #                     "description": "月数偏移量(<0/>0)"
    #                 },
    #                 "weeks_offset": {
    #                     "type": "int",
    #                     "description": "周数偏移量(<0/>0)"
    #                 },
    #                 "days_offset": {
    #                     "type": "int",
    #                     "description": "天数偏移量(<0/>0)"
    #                 },
    #                 "hours_offset": {
    #                     "type": "int",
    #                     "description": "小时偏移量(<0/>0)"
    #                 },
    #                 "minutes_offset": {
    #                     "type": "int",
    #                     "description": "分钟偏移量(<0/>0)"
    #                 },
    #                 "seconds_offset": {
    #                     "type": "int",
    #                     "description": "秒偏移量(<0/>0)"
    #                 }
    #             }
    #         },
    #         "required": [
    #             "base_time","years_offset","months_offset","weeks_offset","days_offset","hours_offset","minutes_offset","seconds_offset"
    #         ],
    #         "additionalProperties": False
    #     }
    # },
    # {
    #     "type": "function",
    #     "function": {
    #         "name": "get_specified_timestamp",
    #         "description": "获取特定日期的时间戳",
    #         "parameters": {  
    #             "type": "object",
    #             "properties": {
    #                 "year": {
    #                     "type": "int",
    #                     "description": "年"
    #                 },
    #                 "month": {
    #                     "type": "int",
    #                     "description": "月"
    #                 },
    #                 "day": {
    #                     "type": "int",
    #                     "description": "日"
    #                 },
    #                 "hour": {
    #                     "type": "int",
    #                     "description": "时"
    #                 },
    #                 "minute": {
    #                     "type": "int",
    #                     "description": "分"
    #                 },
    #                 "second": {
    #                     "type": "int",
    #                     "description": "秒"
    #                 },
    #             }
    #         },
    #         "required": [
    #             "year","month","day","hour","minute","second"
    #         ]
    #     }
    # },
    # {
    #     "type": "function",
    #     "function": {
    #         "name": "search_database_chat_by_time",
    #         "description": "获取特定时间范围的聊天记录",
    #         "parameters": {  
    #             "type": "object",
    #             "properties": {
    #                 "start_time": {
    #                     "type": "string",
    #                     "description": "开始时间"
    #                 },
    #                 "end_time": {
    #                     "type": "string",
    #                     "description": "结束时间"
    #                 }
    #             }
    #         },
    #         "required": [
    #             "start_time","end_time"
    #         ]
    #     }
    # },
    # {
    #     "type": "function",
    #     "function": {
    #         "name": "search_group_chat_history",
    #         "description": "通过发送者及时间范围查询聊天记录",
    #         "strict": True,
    #         "parameters": {  
    #             "type": "object",
    #             "properties": {
    #                 "regex_word": {
    #                     "type": "string",
    #                     "description": "正则匹配聊天记录的关键字,如'公司'"
    #                 },
    #                 "sender": {
    #                     "type": ["list",[]],
    #                     "description": "发送者"
    #                 },
    #                 "start_time": {
    #                     "type": ["string",None],
    #                     "description": "开始时间"
    #                 },
    #                 "end_time": {
    #                     "type": ["string",None],
    #                     "description": "结束时间"
    #                 }
    #             }
    #         },
    #         "required": ["sender","start_time","end_time"],
    #         "additionalProperties": False
    #     }
    # },
    {
        "type": "function",
        "function": {
            "name": "mix_search",
            "description": "混合搜索知识库及对话记录",
            "strict": True,
            "parameters": {  
                "type": "object",
                "properties": {
                    "search_content": {
                        "type": "string",
                        "description": "搜索内容"
                    },
                    "sender": {
                        "type": ["list",[]],
                        "description": "发送者"
                    },
                    "start_time": {
                        "type": ["string",None],
                        "description": "开始时间"
                    },
                    "end_time": {
                        "type": ["string",None],
                        "description": "结束时间"
                    }
                }
            },
            "required": ["search_content"],
            "additionalProperties": False
        }
    },
]


async def execute_tool(tool_name: str, tool_args: Dict[str, Any], data_args: Dict[str, Any], _=None, lang=None) -> tuple:
    """统一工具执行入口"""
    try:
        if tool_name == "mix_search":
            search_data = await mix_search(
                data_args["group_data"]["id"],
                data_args["group_collection"],
                tool_args.get("sender", []),
                tool_args.get("start_time", ""),
                tool_args.get("end_time", ""),
                tool_args["search_content"],
            )
            return "mix_search", {"search_data":[] if len(search_data) == 0 else [data['content'] for data in search_data]}
        if tool_name == "web_search":
            search_data = await web_search(tool_args["search_content"],lang)
            return "web_search", {"search_data":search_data}
        if tool_name == "search_database_file":
            file_content = await search_database_file(
                tool_args["search_content"], data_args["group_data"]["id"]
            )
            return "search_database_file", {"file_content":[] if len(file_content) == 0 else file_content}
        if tool_name == "get_group_recent_chat_history":
            chat_history = await get_group_recent_chat_history(
                data_args["group_collection"], data_args["cited_msg"], data_args["group_data"]
            )
            return "get_group_recent_chat_history", {"chat_content":[] if len(chat_history) == 0 else chat_history}
        if tool_name == "search_graph":
            graph_data = await search_graph(data_args["group_data"]["id"], tool_args["search_content"])
            return "search_graph", {"search_data":[] if len(graph_data)==0 else [data.page_content for data in graph_data]}
        if tool_name == "get_group_old_chat_history":
            chat_history = await get_group_old_chat_history(
                data_args["group_data"]["id"], tool_args["search_content"]
            )
            return "get_group_old_chat_history", {"chat_content":[] if len(chat_history) == 0 else chat_history}
        if tool_name == "get_start_time":
            return "get_start_time", {'start_time':await get_timestamp(tool_args.get("base_time",""),int(tool_args.get("years_offset",0)),int(tool_args.get("months_offset",0)),int(tool_args.get("weeks_offset",0)),int(tool_args.get("days_offset",0)), int(tool_args.get("hours_offset",0)), int(tool_args.get("minutes_offset",0)), int(tool_args.get("seconds_offset",0)))}
        if tool_name == "get_end_time":
            return "get_end_time", {'end_time':await get_timestamp(tool_args.get("base_time",""),int(tool_args.get("years_offset",0)),int(tool_args.get("months_offset",0)),int(tool_args.get("weeks_offset",0)),int(tool_args.get("days_offset",0)), int(tool_args.get("hours_offset",0)), int(tool_args.get("minutes_offset",0)), int(tool_args.get("seconds_offset",0)))}
        if tool_name == "get_now_time":
            return "get_now_time", await get_now_time()
        if tool_name == "get_specified_timestamp":
            return "get_specified_timestamp", [f'指定时间为{await get_specified_timestamp(tool_args.get("year",2025),tool_args.get("month",0),tool_args.get("day",0),tool_args.get("hour",0),tool_args.get("minute",0),tool_args.get("second",0))}']
        if tool_name == "search_database_chat_by_time":
            chat_history = await search_database_chat_by_time(
                data_args["group_data"]["id"],
                tool_args.get("start_time", ""),
                tool_args.get("end_time", ""),
            )
            return "search_database_chat_by_time", {"chat_content":[] if len(chat_history) == 0 else chat_history}
        if tool_name == "search_group_chat_history":
            chat_history = await search_group_chat_history(
                data_args["group_collection"],
                tool_args.get("sender", []),
                tool_args.get("start_time", ""),
                tool_args.get("end_time", ""),
                tool_args.get("regex_word", ""),
            )
            return "search_database_chat_by_time", {"chat_content":[] if len(chat_history) == 0 else chat_history}
        logger.error(f"未知工具调用: {tool_name}")
        return None,{"error": _("未知工具")}
    except Exception as e:
        logger.error(f"工具执行失败: {str(e)}", exc_info=True)
        return None, {"error": str(e)}


# 具体工具实现


async def log_tool_usage(
    user_id: str,
    group_id: str,
    tool_name: str,
    args: dict,
    result: dict,
    message_id: str,
):
    """记录工具使用日志"""
    manage_db = await get_database()
    await manage_db.tool_logs.insert_one(
        {
            "user_id": user_id,
            "group_id": group_id,
            "message_id": message_id,
            "tool_name": tool_name,
            "args": args,
            "result": result,
            "timestamp": datetime.now(),
        }
    )

async def get_now_time(input:str=""):
    return {"base_time":datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

async def get_specified_timestamp(year, month, day, hour, minute, second) -> 0:
    try:
        if year < 1970:
            year = 2025
        dt = datetime(year, month, day, hour, minute, second)

        timestamp = dt.timestamp()

        return int(timestamp)
    except Exception as e:
        return 0

async def get_timestamp(base_time:str,years_offset=0,months_offset=0,weeks_offset=0,days_offset=0, hours_offset=0, minutes_offset=0, seconds_offset=0) -> int:
    """
    基于基准时间计算偏移后的时间戳
    :param base_time: 基准时间
    :param days_offset: 天数偏移量
    :param hours_offset: 小时偏移量
    :param minutes_offset: 分钟偏移量
    :param seconds_offset: 秒偏移量
    :return: 偏移后的时间戳
    """
    # # 获取当前时间的日期时间对象
    # current_time = datetime.now()
    # print(f"当前时间: {current_time}")
    try:
        if weeks_offset!=0:
            days_offset += weeks_offset * 7
        if months_offset!=0:
            days_offset += months_offset * 30
        if years_offset!=0:
            days_offset += years_offset * 365

        # 计算时间偏移量
        delta = timedelta(days=days_offset, hours=hours_offset, minutes=minutes_offset, seconds=seconds_offset)

        # 计算偏移后的日期时间对象
        offset_time = datetime.strptime(base_time, '%Y-%m-%d %H:%M:%S') + delta

        # 将当前时间和偏移后的时间转换为时间戳
        current_timestamp = base_time
        # offset_timestamp = offset_time.timestamp()

        return offset_time.strftime('%Y-%m-%d %H:%M:%S')
    except Exception as e:
        return 0


async def get_now_date():
    return [f"当前时间为：{int(time.time())}"]


async def web_search(search_content: str,lang) -> List[dict]:
    try:
        """网络搜索工具"""
        if lang == "en":
            # 英文联网搜索
            web_search = TavilySearch()
            results = await web_search.search(
                search_content, freshness="noLimit", summary=True, count=10
            )
            search_results = await web_search.pretty_search_results(results)
            search_results = [{"content":res['summary']} for res in search_results]
            rerank_results = rerank_context(search_content,search_results,5)
            return [res['content'] for res in rerank_results]
        else:
            # 中文联网搜索
            web_search = BoChaSearch()
            results = await web_search.search(
                search_content, freshness="noLimit", summary=True, count=10
            )
            search_results = await web_search.pretty_search_results(results)
            search_results = [{"content":res['summary']} for res in search_results]
            rerank_results = rerank_context(search_content,search_results,5)
            return [res['content'] for res in rerank_results]
    except Exception as e:
        return []

def rerank_context(question, context, top_k):
    """
    ### 对模型context内容进行重排
    """
    context_list = []
    for content in context:
        context_list.append(content["content"])
    rerank_res = reranker_model.compute_score(question, context_list)
    # TODO: 聊天记录都重排干扰较大
    # 取出top_k个结果
    docs = sorted(zip(rerank_res, context), key=lambda x: x[0], reverse=True)
    res = [i[1] for i in docs[:top_k]]
    return res

async def get_collection_data(group_id):
    # 获取群组数据
    database_db = await get_database()
    group_data = await database_db.groups.find_one({"id": group_id})
    if not group_data:
        logger.error(f"群组 {group_id} 不存在")
        return []

    # 获取数据库ID列表
    database_ids = group_data.get("database_ids", [])
    if not database_ids:
        logger.error(f"群组 {group_id} 未绑定知识库")
        return []

    # 查询所有关联的数据库记录获取v_name
    collection = []
    for db_id in database_ids:
        database = await database_db.databases.find_one({"id": db_id})
        if database:
            collection.append(database)

    return collection

def extend_search_docs(
    slot: int, collection_name: str, docs: list[Document]
) -> list[Document]:
    """### 文档知识库对检索结果进行扩大查询拼接，以保障段落的完整性
    :param slot: 前后段落检索数量
    :param collection_name: 集合名称
    :param docs: 当前检索结果
    """
    search_ids = []
    doc_ids = [doc.metadata["pk"] for doc in docs]
    for doc_id in doc_ids:
        split_id = doc_id.split("_")
        for i in range(1, slot + 1):
            add_doc_id = f"{split_id[0]}_{int(split_id[1]) + i}"
            if add_doc_id not in doc_ids and add_doc_id not in search_ids:
                search_ids.append(add_doc_id)
            reduce_doc_id = f"{split_id[0]}_{int(split_id[1]) - i}"
            if reduce_doc_id not in doc_ids and reduce_doc_id not in search_ids:
                search_ids.append(reduce_doc_id)
    if len(search_ids) > 0:
        client = MilvusClient(
            uri=f"http://{settings.MILVUS_HOST}:{settings.MILVUS_PORT}"
        )
        # 通过前后片段id进行查找
        search_result = client.query(
            collection_name=collection_name,
            filter=f"pk in {str(search_ids)}",
            k=len(search_ids),
        )
        add_docs = [
            Document(
                page_content=result["text"],
                metadata={"pk": result["pk"]},
            )
            for result in search_result
        ]
        docs.extend(add_docs)
        # 合并连续段落
        docs = merge_docs(docs)
    return docs

def merge_docs(docs: list[Document]) -> list[Document]:
    """
    ### 将docs中pk连续的Document对象进行page_content合并
    """
    merge_res = []
    merge_dict = {}
    # 构造{文档id:{"序号":文本}}映射
    for doc in docs:
        split_id = doc.metadata["pk"].split("_")
        if split_id[0] in merge_dict.keys():
            merge_dict[split_id[0]].update({split_id[1]: doc.page_content})
        else:
            merge_dict.update({split_id[0]: {split_id[1]: doc.page_content}})
    for item, texts in merge_dict.items():
        # 先将序号转换为整型并排序
        sorted_keys = sorted([int(k) for k in texts.keys()])
        segments = []  # 用于存储拼接后的各段
        current_segment = []  # 当前段的文本列表
        previous_key = None  # 记录上一个序号

        # 遍历所有的序号
        for key in sorted_keys:
            # 如果是第一项或当前序号和前一个序号连续（即相差1）并且拼接长度小于4段
            if (previous_key is None or key == previous_key + 1) and len(
                current_segment
            ) < 4:
                current_segment.append(texts[str(key)])
            else:
                # 序号不连续，则将前面收集的文本拼接成一段，并重置 current_segment
                segments.append(
                    Document(
                        page_content="".join(current_segment),
                        metadata={"doc_uuid": item},
                    )
                )
                current_segment = [texts[str(key)]]
            previous_key = key

        # 循环结束后，不要忘记将最后一段加入到 segments 中
        if current_segment:
            segments.append(
                Document(
                    page_content="".join(current_segment), metadata={"doc_uuid": item}
                )
            )

        merge_res.extend(segments)
    return merge_res


async def search_database_file(search_content: str, group_id: str) -> List:
    collection = await get_collection_data(group_id)
    all_docs = []
    for collection_data in collection:
        if collection_data["type"] not in ["file","col"]:
            continue
        if not BM25_MANAGER.have_index(collection_data["v_name"]):
            BM25_MANAGER.build_index(collection_data["v_name"])
        bm25_docs = BM25_MANAGER.search(collection_data["v_name"], search_content, top_k=10)
        all_docs.extend(bm25_docs)
    # all_docs = [{"content":doc.page_content} for doc in all_docs]
    # rerank_results = rerank_context(search_content,all_docs,5)
    return all_docs

async def search_faq(search_content: str, group_id: str) -> List:
    collection = await get_collection_data(group_id)
    all_docs = []
    for collection_data in collection:
        if collection_data["type"] != "file":
            continue
        faq_docs = await BM25_FAQ_MANAGER.search(collection_data["id"], search_content, top_k=10)
        all_docs.extend(faq_docs)
    return all_docs

async def mix_search(group_id: str,group_collection,sender:str,start_time:str,end_time:str,search_content: str,top_k: int = 5) -> List:
    database_res = await search_database_file(search_content,group_id)
    # graph_res = await search_graph(group_id,search_content)
    faq_res = await search_faq(search_content, group_id)
    chat_res = await search_group_chat_history(group_collection=group_collection,
                                               sender=sender,
                                               start_time=start_time,
                                                end_time=end_time,
                                               content=search_content)
    # 混合排序
    all_docs = database_res + chat_res + faq_res
    sorted_docs = sorted(all_docs, key=lambda x: x.metadata['score'] if hasattr(x,'metadata') else x["score"], reverse=True)
    result = []
    for doc in sorted_docs:
        if isinstance(doc, Document):
            if doc.metadata.get('score') == 1:
                # 满分片段无视top_k限制
                result.append({"content": doc.page_content})
            elif len(result) < top_k:
                result.append({"content": doc.page_content})
        else:
            if doc.get('score') == 1:
                # 满分片段无视top_k限制
                result.append(doc)
            elif len(result) < top_k:
                result.append(doc)
    return result

async def search_documents_bm25(
    collection: str, query: str, top_k: int = 5
) -> Dict[str, Any]:
    if not BM25_MANAGER.have_index(collection):
        success = BM25_MANAGER.build_index(collection)
        if not success:
            return {
                "error": f"无法建立集合 {collection} 的索引，可能集合不存在或数据为空"
            }

    results = BM25_MANAGER.search(collection, query, top_k)
    return results

async def search_group_chat_history( group_collection, sender, start_time, end_time, content):
    """
    按照关键信息搜索聊天记录
    """
    query = {"type":{"$nin":["new","revoke"]}}
    # if content:
    #     escaped_name = re.escape(content)
    #     regex_pattern = f".*{escaped_name}.*"
    #     query["content"] =  {"$regex": regex_pattern, "$options": "i"}
    if sender:
        sender_pattern = "|".join(re.escape(s) for s in sender)  # Escape special regex chars
        regex_pattern = f".*({sender_pattern}).*"  # .* for substring matching
        query["sender_username"] = {"$regex": regex_pattern, "$options": "i"}
    if start_time and end_time:
        query['created_at'] = {"$gte":datetime.strptime(start_time, '%Y-%m-%d %H:%M:%S'),"$lte":datetime.strptime(end_time, '%Y-%m-%d %H:%M:%S')}
    else:
        if start_time:
            query['created_at'] = {"$gt":datetime.strptime(start_time, '%Y-%m-%d %H:%M:%S')}
        if end_time:
            query['created_at'] = {"$lt":datetime.strptime(start_time, '%Y-%m-%d %H:%M:%S')}
    messages = await group_collection.find(query).to_list(None)
    if content and messages:
        # 通过BM25匹配
        messages = RealTimeBM25Sreacher(messages,content).search(top_k=15,search_key="content")
    for message in messages:
        if not content:
            # 精确聊天记录搜索时赋予封顶分数
            message["score"] = 1
        message["content"] = f"【{message['created_at'].strftime('%Y-%m-%d %H:%M')}】【{message['sender_username']}】{message['content']}"
    # result = [f"【{message['created_at'].strftime('%Y-%m-%d %H:%M')}】【{message['sender_username']}】{message['content']}" for message in messages]
    return messages

async def get_group_recent_chat_history(group_collection, cited_msg, group_data, slot: int=3, sender_id: str="", time_and_sender=True) -> list:
    """
    ## 获取历史间隔小于slot分钟的聊天记录
    :param group_collection: 群组集合
    :param cited_msg: 用户当前发起消息
    :param group_data: 群组数据
    :param slot: 对话间隔
    :param sender_id: 发送者ID
    :param time_and_sender: 是否包含时间和发送者
    :return: 历史聊天记录
    """
    now_time = cited_msg["created_at"]
    history_chat_content = []
    chat_content = (
        await group_collection.find(
            {
                "id": {"$ne": cited_msg["id"]},  # 过滤用户当前发起的消息
                "content": {"$ne": ""},  # 过滤空消息
                "is_revoke": {"$ne": True},  # 过滤撤回消息
                "created_at": {"$lte": cited_msg["created_at"]},
            }
        )
        .sort("created_at", -1)
        .to_list(None)
    )
    i = 0
    while True:
        # 越界或新话题进行截断
        if i >= len(chat_content) or chat_content[i]["type"] == "new":
            break
        last_message_time = chat_content[i]["created_at"]
        # 计算时间差
        time_difference = now_time - last_message_time
        # 将时间差转换为分钟
        minutes_difference = time_difference.total_seconds() / 60

        is_pass = False
        msg = chat_content[i]
        # if "" != sender_id and msg["cite"]:
        #   logger.info(f'eeeeeeeeeeeeeee: {msg["cite"]} {msg["cite"]["sender_id"]} {sender_id} {msg["content"]} {msg["is_AI"]}')
        # 如果此消息是bot发送并且引用的消息是sender_id
        if "" != sender_id and msg["cite"] and msg["cite"]["sender_id"] == sender_id and msg["is_AI"] == True:
            is_pass = True
        # 如果此消息是sender_id发送
        if "" != sender_id and msg["sender_id"] == sender_id: 
            is_pass = True
        if minutes_difference < slot:
            if is_pass == True:
                history_chat_content.append(chat_content[i])
            now_time = chat_content[i]["created_at"]
            i += 1
        else:
            break
    # 解析上下文或引用内容中的文档
    history_chat_content = await get_file_contents(
            history_chat_content, group_data, group_collection
        )
    for item in history_chat_content:
        if time_and_sender == True:
            item["content"] = f"【{item['created_at'].strftime('%Y-%m-%d %H:%M:%S')}】【{item.get('sender_username','')}】{item['content']}"
        else:
            item["content"] = f"{item['content']}"
    return history_chat_content

async def broadcast_and_save_msg(
    group_collection,
    manage_db,
    content,
    type,
    group_id,
    sender_id,
    cite_id=None,
    sound_file_id=None,
    duration=None,
    save_message=True,
    at_list=[],
    is_AI: bool = False,
    is_streaming=False,
):
    """
    ### 广播并保存消息内容
    """
    try:
        user_data = await manage_db.users.find_one({"id": sender_id})
    except:
        user_data = {"avatar": None, "username": sender_id}
    if not user_data:
        user_data = {"avatar": None, "username": sender_id}
    if isinstance(content, dict):
        content = json.dumps(content)
    if cite_id:
        cite_data = await group_collection.find_one({"id": cite_id})
    else:
        cite_data = None
    group_data = await manage_db.groups.find_one({"id": group_id})
    # 保存用户消息
    message = Message(
        type=type,
        content=content,
        sender_id=sender_id,
        sender_avatar=user_data["avatar"],
        sender_username=user_data["username"],
        group_id=group_data["id"],
        receiver=[],
        vector_status="pending",
        transfer=None,
        duration=duration,
        cite=MessageData(**cite_data).model_dump() if cite_data else None,
        sound_file_id=sound_file_id,  # 语音文件ID
        at_list=at_list,
        read_list=[sender_id] if isinstance(sender_id, str) else [],
        is_AI=is_AI,
    )
    message_dict = message.model_dump()
    # 更新群聊最后一条对话记录
    message_dict["is_streaming"] = is_streaming
    receiver_list = await connection_dic.broadcast(
        group_data["member_ids"],
        json.dumps(MessageData(**message_dict).model_dump()),
    )
    if save_message:
        if type != "new":
            await manage_db.groups.update_one(
                {"id": group_data["id"]},
                {"$set": {"last_message": message_dict}},
            )
        # 记录发送给了哪些用户方便排查websocket管理问题
        message_dict["receiver"] = receiver_list
        await group_collection.insert_one(message_dict)

    return message

async def _process_audio_message(msg):
    """处理音频消息的公共方法"""
    # Get translator from context
    _ = await get_translator()

    manage_db = await get_database()
    if msg.get("transfer"):
        return msg["transfer"]
    elif msg.get("transfer_doc_id"):
        # 提取数据库操作到独立方法
        doc = await manage_db.files.find_one({"id": msg["transfer_doc_id"]})
        cloud_file_path = doc.get("cloud_file_path")
        local_file_path = doc.get("local_file_path")
        # 如果能从本地找到文档,从本地打开文档读取内容
        if local_file_path and os.path.exists(local_file_path):
            with open(local_file_path, "r", encoding="utf-8") as f:
                content = f.read()
        else:
            # 从云端下载
            local_file_path = await download_file(cloud_file_path)
            if os.path.exists(local_file_path):
                with open(local_file_path, "r", encoding="utf-8") as f:
                    content = f.read()
            else:
                content = _("音频文件不存在")
        return content
    return None

async def get_file_contents(chat_content: list, group_data:dict, group_collection, _=None) ->  list:
    """
    将消息列表中文档消息的content改写为文档内容
    
    Args:
        chat_content: 消息列表
        group_data: 群组数据
        group_collection: 群组数据库集合对象
    
    Returns:
        转换后的消息列表
    """
    try:
        manage_db = await get_database()
        notify_audio_transfer = False
        new_chat_content = []
        # 遍历chat_content中的所有项
        for msg in chat_content:
            try:
                # 检查每个项的type字段
                if msg.get("type").startswith("audio/"):
                    # 首先检查是否有transfer字段
                    transfer = await _process_audio_message(msg)
                    if transfer:
                        msg['content'] = transfer
                        continue
                    else:
                        if not notify_audio_transfer:
                            await broadcast_and_save_msg(
                                group_collection=group_collection,
                                manage_db=manage_db,
                                content=_("检测到上下文或引用消息中含有未识别音频文件,识别后模型回复更精细哦！"),
                                type="text",
                                group_id=group_data["id"],
                                sender_id=_(settings.DEFAULT_GROUP_ASSISTANT_NAME),
                                save_message=True,
                            )
                        notify_audio_transfer = True
                        continue
                # 当type既不是text又不是model时
                elif msg.get("type") != "text" and msg.get("type") != "model" and msg.get("type") != "new":
                    # 获得msg的content项
                    try:
                        path = ast.literal_eval(msg.get("content"))
                    except (SyntaxError, ValueError) as e:
                        logger.warning(f"无法解析消息内容: {msg.get('content')},错误:{e}")
                        continue
                    # 获取path中的id
                    file_id = path.get("id")
                    # 从数据库中获取id对应的文档
                    doc = await manage_db.files.find_one({"id": file_id})
                    cloud_file_path = doc.get("cloud_file_path")
                    local_file_path = doc.get("local_file_path")
                    # 如果能从本地找到文档,从本地打开文档读取内容
                    if os.path.exists(local_file_path):
                        file_path = local_file_path
                    else:
                        # 从云端下载
                        file_path = await download_file(cloud_file_path)
                    file_content = await identify_unstructured_file(file_path,_)
                    msg["content"] = f"{file_content}\n"
                else:
                    # 如果type为text,跳过该项不处理
                    pass
            except Exception as e:
                logger.error(f"消息{msg}文档内容转写失败: {str(e)}")
            finally:
                new_chat_content.append(msg)
        return new_chat_content
    except Exception as e:
        logger.error(f"获取文件内容失败: {str(e)}")
        return new_chat_content

async def get_group_old_chat_history(group_id, search_content):
    try:
        collection = await get_collection_data(group_id)
        all_docs = []
        # 循环遍历数据库,找到相似度最高的5个数据
        for collection_data in collection:
            if collection_data["type"] != "chat":
                continue
            if not BM25_MANAGER.have_index(collection_data["v_name"]):
                BM25_MANAGER.build_index(collection_data["v_name"])
            bm25_docs = BM25_MANAGER.search(collection_data["v_name"], search_content, top_k=10)
            all_docs.extend(bm25_docs)
            vector_store = Milvus(
                embedding_function=embedding_model,
                collection_name=collection_data["v_name"],
                connection_args={
                    "host": settings.MILVUS_HOST,
                    "port": settings.MILVUS_PORT,
                    "uri": f"http://{settings.MILVUS_HOST}:{settings.MILVUS_PORT}",
                },
            )
            try:
                docs = await vector_store.asimilarity_search(search_content, k=10)
            except Exception as e:
                logger.error(
                    f"{group_id}中{collection_data['v_name']}检索失败:{e}"
                )
                docs = []
            if "type" in collection_data and collection_data["type"] == "file":
                slot = 1
                docs = await asyncio.to_thread(
                    extend_search_docs, slot, collection_data["v_name"], docs
                )
            all_docs.extend(docs)
    except Exception as e:
        return []
    all_docs = [{"content":doc.page_content} for doc in all_docs]
    rerank_result = rerank_context(search_content,all_docs,5)
    return [res["content"] for res in rerank_result]

async def search_graph(group_id: str, search_content: str):
    """
    ### 从知识库中查询问题（类封装版）
    """
    manage_db = await get_database()
    group_data = await manage_db.groups.find_one({"id": group_id})
    handler = GraphQueryHandler(group_data, search_content)
    # triplets = await handler.execute()
    # if triplets:
    #     logger.info(f"知识图谱查询结果: {triplets}")
    await handler.execute(top_k=15)
    all_docs = []
    client = MilvusClient(
            uri=f"http://{settings.MILVUS_HOST}:{settings.MILVUS_PORT}"
        )
    for db_id,doc_sequences in handler.doc_sequences.items():
        if len(doc_sequences) == 0:
            continue
        database_data = await manage_db.databases.find_one({"id": db_id,"is_deleted":False})
        search_result = client.query(
            collection_name=database_data['v_name'],
            filter=f"pk in {str(list(doc_sequences.keys()))}",
            k=len(list(doc_sequences.keys())),
        )
        docs = [
            Document(
                page_content=result["text"],
                metadata={"pk": result["pk"],
                "score":doc_sequences.get(result["pk"],0)},
            )
            for result in search_result
        ]
        all_docs.extend(docs)

    return all_docs

async def search_database_chat_by_time(
    group_id: str,
    start_time: str,
    end_time: str
) -> List:
    """
    按时间戳范围搜索数据库文件中的聊天记录

    Args:
        group_id: 群组ID
        start_time: 开始时间
        end_time: 结束时间

    Returns:
        搜索结果列表
    """
    try:
        collection = await get_collection_data(group_id)
        all_docs = []

        # 构建时间戳过滤表达式
        filter_expr = None
        if start_time is not None or end_time is not None:
            start_timestamp = datetime.strptime(start_time, '%Y-%m-%d %H:%M:%S').timestamp()
            end_timestamp = datetime.strptime(end_time, '%Y-%m-%d %H:%M:%S').timestamp()
            time_conditions = []
            if start_time is not None:
                time_conditions.append(f"timestamp >= {start_timestamp}")
            if end_time is not None:
                time_conditions.append(f"timestamp <= {end_timestamp}")
            filter_expr = " && ".join(time_conditions)

        # 构造一个固定的“空向量”
        dummy_query_vector = [[0.0] * 768]  # 如果你的 embedding 不是 384，请改成对应维度

        for collection_data in collection:
            if collection_data["type"] != "chat":
                continue

            vector_store = Milvus(
                embedding_function=embedding_model,
                collection_name=collection_data["v_name"],
                connection_args={
                    "host": settings.MILVUS_HOST,
                    "port": settings.MILVUS_PORT,
                    "uri": f"http://{settings.MILVUS_HOST}:{settings.MILVUS_PORT}",
                },
            )

            try:
                if filter_expr:
                    docs = await vector_store.asimilarity_search_by_vector(
                        embedding=dummy_query_vector[0],
                        k=10000,
                        expr=filter_expr
                    )
                    all_docs.extend(docs)
                else:
                    docs = []
            except Exception as e:
                pass
        if len(all_docs) == 0:
            # 从mongo查
            try:
                db = await get_chat_database()
                docs = await getattr(db,group_id).find({"$and":[{"created_at":{"$gt":datetime.strptime(start_time, '%Y-%m-%d %H:%M:%S')}},
                                                        {"created_at":{"$lt":datetime.strptime(end_time, '%Y-%m-%d %H:%M:%S')}},
                                                        {"type":"text"}]}).to_list(None)
                docs = [Document(page_content=f'【{doc["created_at"].strftime("%Y-%m-%d %H:%M")}】【{doc["sender_username"]}】{doc["content"]}') for doc in docs]
                all_docs.extend(docs)
            except Exception as e:
                logger.error(f"{group_id}中{collection_data['v_name']}检索失败: {e}")
                docs = []
        all_docs = [doc.page_content for doc in all_docs]
        return all_docs
    except Exception as e:
        logger.error(e)
        return []

