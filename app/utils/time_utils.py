from .remote_model import chat_model

def resolve_relative_time(text: str) -> str:
    return chat_model.convert_relative_times_in_text(text)

# print(resolve_relative_time("周一"))      
# print(resolve_relative_time("周五"))      
# print(resolve_relative_time("周日"))      
# print(resolve_relative_time("本周日"))    
# print(resolve_relative_time("上周三"))    
# print(resolve_relative_time("上周日"))    
# print(resolve_relative_time("本周五"))    

