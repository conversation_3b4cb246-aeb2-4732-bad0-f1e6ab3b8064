import base64

from langchain_community.callbacks.manager import get_openai_callback

from ..database import get_database, get_eval_database
from ..models.user import User
from .llm import (
    SupportedModels,
    broadcast_and_save_msg,
    finalize_streaming_msg,
    get_llm,
    update_streaming_msg,
)
from .log import logger


async def llm_process_picture(
    picture_content: bytes,
    picture_type: str,
    question: str,
    group: dict,
    group_collection,
    message_id: str,
    model: str,
    user: User,
):
    """
    异步处理音频文件并触发唤醒词响应
    Args:
        picture_content: 图片内容
        question: 问题
        group: 群组数据
        group_collection: 群组数据库集合
        message_id: 消息ID
        model: 模型名称
        user: 用户对象
    """
    manage_db = await get_database()
    eval_db = await get_eval_database()
    response = ""
    try:
        # 将图片内容用base64编码
        base64_image = base64.b64encode(picture_content).decode("utf-8")

        # 1. 根据中文选模型
        # 2. TODO: 增加唤醒词设置后，编写唤醒词模型映射查询逻辑
        # 触发智能体回应
        user_data = await manage_db.users.find_one(
            {
                "id": user.id,
                "is_active": True,
            }
        )
        await broadcast_and_save_msg(
            group_collection=group_collection,
            manage_db=manage_db,
            content=f"@{model} {question}",
            type="text",
            group_id=group["id"],
            sender_id=user.id,
            cite_id=message_id,
        )

        if model != _(SupportedModels.TONGYI_QIANWEN_VL.value):
            await broadcast_and_save_msg(
                group_collection=group_collection,
                manage_db=manage_db,
                content="请使用图片识别模型识图",
                type="text",
                group_id=group["id"],
                sender_id=model,
                cite_id=message_id,
                is_AI=True,
            )
            return

        # 如果编码后图片大于10MB，就不处理了
        if len(base64_image) > 10 * 1024 * 1024:
            await broadcast_and_save_msg(
                group_collection=group_collection,
                manage_db=manage_db,
                content="图片过大（base64编码后超过10MB），无法识别",
                type="text",
                group_id=group["id"],
                sender_id=model,
                cite_id=message_id,
                is_AI=True,
            )
            return

        vllm, _ = get_llm(model, user_data.get("user_setting", {}))

        if picture_type.endswith("png"):
            picture_type = "png"
        elif picture_type.endswith("jpg") or picture_type.endswith("jpeg"):
            picture_type = "png"
        elif picture_type.endswith("webp"):
            picture_type = "png"

        whole_question = [
            {
                "type": "image_url",
                # 需要注意，传入Base64，图像格式（即image/{format}）需要与支持的图片列表中的Content Type保持一致。"f"是字符串格式化的方法。
                # PNG图像：  f"data:image/png;base64,{base64_image}"
                # JPEG图像： f"data:image/jpeg;base64,{base64_image}"
                # WEBP图像： f"data:image/webp;base64,{base64_image}"
                "image_url": {
                    "url": f"data:image/{picture_type};base64,{base64_image}"
                },
            },
            {"type": "text", "text": f"{question}"},
        ]
        # 从群的agent_setting的role项获取系统提示词
        role = group["agent_setting"]["role"]
        if not role:
            role = "你是图片识别模型,请根据图片内容和问题,输出回答。"
        messages = [
            {
                "role": "system",
                "content": [{"type": "text", "text": role}],
            },
            {
                "role": "user",
                "content": whole_question,
            },
        ]
        logger.info(f"{messages}")
        with get_openai_callback() as callback:
            initial_msg = await broadcast_and_save_msg(
                group_collection=group_collection,
                manage_db=manage_db,
                content="",  # 初始为空内容
                type="text",
                group_id=group["id"],
                sender_id=model,
                cite_id=message_id,
                is_streaming=True,
                is_AI=True,
            )

            full_response = ""
            async for chunk in vllm.astream(messages):
                chunk_content = (
                    chunk.content if hasattr(chunk, "content") else str(chunk)
                )
                full_response += chunk_content
                await update_streaming_msg(
                    group_collection=group_collection,
                    group_data=group,
                    message_id=initial_msg.id,
                    chunk=chunk_content,
                    full_content=full_response,
                    is_streaming=True,
                    created_at=initial_msg.created_at.strftime("%Y-%m-%d %H:%M:%S"),
                    is_AI=True,
                )

        logger.info(f"{initial_msg.id}对话token消耗情况:{callback}")
        await manage_db.users.update_one(
            {"id": user.id},
            {"$inc": {"user_service.token_usage": callback.total_tokens}},
        )

    except Exception as e:
        logger.error(f"Error processing picture: {e}")
        if "not enough quota" in str(e):
            response = "公有云账号余额不足，请联系管理员充值。"
        elif "too long" in str(e):
            response = "上下文过长，模型暂无法处理，正在优化中。"
        else:
            response = "获取大模型回复异常，请稍后再试。"

    finally:
        await finalize_streaming_msg(
            group_collection=group_collection,
            manage_db=manage_db,
            message_id=initial_msg.id,
            full_content=full_response + response,
            group_data=group,
            is_AI=True,
        )
        await eval_db.records.insert_one(
            {
                "message_id": initial_msg.id,
                "model": vllm.model_name,
                "question": question,
                "context": whole_question,
                "answer": response,
            }
        )
