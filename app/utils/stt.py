import asyncio
import os
import uuid
import json

from ..database import get_chat_database, get_database
from ..models.user import User
from .asr import VoiceTranscriber
from .file_handler import download_file, save_file
from .llm import background_answer, broadcast_and_save_msg
from .log import logger
from .websocket_manager import connection_dic

async def process_audio_for_wakeword(
    content: bytes,
    group: dict,
    group_collection,
    message_id: str,
    model: str,
    user: User,
    language: str = "cn",
    _=None
) -> None:
    """
    异步处理音频文件并触发唤醒词响应
    Args:
        file: 文件对象
        group: 群组数据
        group_collection: 群组数据库集合
        message_id: 消息ID
        model: 模型名称
        language: 识别语言，默认为'cn'
    """
    manage_db = await get_database()
    # 异步读取文件内容
    transcriber = VoiceTranscriber()
    # recognized_text = transcriber.transcribe_audio(content)
    recognized_text = await asyncio.to_thread(transcriber.transcribe_audio, content)
    logger.critical(f"识别结果: {recognized_text}")

    # 1. 根据中文选模型
    # 2. TODO: 增加唤醒词设置后，编写唤醒词模型映射查询逻辑
    # 触发智能体回应
    user_data = await manage_db.users.find_one(
        {
            "id": user.id,
            "is_active": True,
        }
    )
    await connection_dic.broadcast(
        group["member_ids"],
        json.dumps({"type":"transfer","id":message_id,"group_id":group["id"],"transfer":recognized_text}),
    )
    asyncio.create_task(
        background_answer(
            group_data=group,
            question=recognized_text,
            llm_model=model,
            group_collection=group_collection,
            user_data=user_data,
            cite_id=message_id,
            _=_
        )
    )

    try:
        # 将识别结果写入transfer字段
        await group_collection.update_one(
            {"id": message_id}, {"$set": {"transfer": recognized_text}}
        )
    except Exception as e:
        logger.error(f"保存识别结果失败: {str(e)}")


async def recognize_audio_file(
    file_id: str, message_id: str, group_id: str, current_user, long: bool
) -> str:
    """
    根据文件ID和群组ID获取音频数据

    Args:
        file_id: 文件唯一标识
        group_id: 群组ID（用于日志追踪）
        manage_db: 数据库连接
        current_user: 用户数据
        long: 是否长语音识别
    Returns:
        str: 识别结果
    """
    try:
        manage_db = await get_database()
        chat_db = await get_chat_database()
        group_collection = getattr(chat_db, group_id)
        # 查询文件记录
        file_record = await manage_db.files.find_one(
            {"id": file_id, "is_deleted": False}
        )

        if not file_record:
            logger.error(f"音频文件 {file_id} 不存在或已被删除")
            await group_collection.update_one(
                {"id": message_id}, {"$set": {"transfer_status": "fail"}}
            )
            return "识别失败,音频文件不存在或已被删除"

        # 获取文件路径
        local_file_path = file_record.get("local_file_path")
        cloud_file_path = file_record.get("cloud_file_path")

        # 路径校验
        if not all([local_file_path, cloud_file_path]):
            logger.error(f"文件路径信息不完整 [文件:{file_id}")
            await group_collection.update_one(
                {"id": message_id}, {"$set": {"transfer_status": "fail"}}
            )
            return "识别失败,文件路径信息不完整,请重试"

        if os.path.exists(local_file_path):
            # 本地文件存在，直接打开本地文件获取audio_data
            with open(local_file_path, "rb") as f:
                audio_data = f.read()
        else:
            # 本地不存在则从云端下载
            audio_file_path = cloud_file_path
            try:
                # 下载音频文件
                local_audio_file_path = await download_file(audio_file_path)
                with open(local_audio_file_path, "rb") as f:
                    audio_data = f.read()
            except Exception as download_error:
                logger.error(
                    f"文件下载失败 [路径:{cloud_file_path}: {str(download_error)}"
                )
                await group_collection.update_one(
                    {"id": message_id}, {"$set": {"transfer_status": "fail"}}
                )
                return "识别失败,服务器未能获取录音文件,请重试"

        await group_collection.update_one(
            {"id": message_id}, {"$set": {"transfer_status": "parsing"}}
        )
        logger.info(f"开始识别音频文件 {file_id}")
        transcriber = VoiceTranscriber()
        recognition_result = await asyncio.to_thread(
            transcriber.transcribe_audio, audio_data
        )
        logger.info(f"结束识别音频文件 {file_id}")
        if long:
            # 把文件通过save上传到云端
            create_time = file_record.get("created_at")
            txt_file_name = f"{create_time}音频识别结果.txt"
            safe_filename = "".join(
                char for char in txt_file_name if char.isalnum() or char in "._- "
            )
            txt_header = {
                "filename": safe_filename,
                "content_type": "text/plain",
                "size": len(recognition_result),
            }
            txt_file_content = recognition_result.encode("utf-8")
            doc_uuid = str(uuid.uuid4())
            await save_file(
                txt_file_content,
                current_user,
                doc_uuid,
                manage_db,
                txt_header,
                None,
                group_id,
            )
            # 广播发送文档的消息
            message = await broadcast_and_save_msg(
                group_collection=group_collection,
                manage_db=manage_db,
                content={"id": doc_uuid, "filename": safe_filename},
                type="text/plain",
                group_id=group_id,
                sender_id=current_user.id,
                cite_id=message_id,
                duration=None,
            )
            update_data = {
                "$set": {
                    "transfer_status": "success",
                    "transfer_doc_id": doc_uuid,
                    "cite": message.model_dump(),
                }
            }
        else:
            update_data = {
                "$set": {"transfer": recognition_result, "transfer_status": "success"}
            }
        await group_collection.update_one(
            {"id": message_id},
            update_data,
        )
        return recognition_result
    except Exception as e:
        logger.error(f"获取识别结果异常 [文件:{file_id}]: {str(e)}", exc_info=True)
        await group_collection.update_one(
            {"id": message_id}, {"$set": {"transfer_status": "fail"}}
        )
        return "识别失败,识别过程出现异常,请重试"
