import logging
import os
from datetime import datetime

from pymilvus import Collection, connections, utility

# 设置日志时间格式
logging.basicConfig(
    format="%(asctime)s - %(levelname)s - %(message)s", level=logging.INFO
)

# 定义配置项目
MILVUS_HOST = ""
MILVUS_PORT = ""
DOCUMENT_SAVE_PATH = ""
HWC_BUCKET_NAME = ""

# 从.env文件中获取配置
conf_file = open(".env", "r")
for line in conf_file:
    if line.startswith("MILVUS_HOST ="):
        MILVUS_HOST = line.split("=")[1].strip()
    if line.startswith("MILVUS_PORT ="):
        MILVUS_PORT = line.split("=")[1].strip()
    if line.startswith("DOCUMENT_SAVE_PATH ="):
        DOCUMENT_SAVE_PATH = line.split("=")[1].strip()
    if line.startswith("HWC_BUCKET_NAME ="):
        HWC_BUCKET_NAME = line.split("=")[1].strip()

# 打印配置
logging.info(
    f"MILVUS_HOST: {MILVUS_HOST}, MILVUS_PORT: {MILVUS_PORT}, DOCUMENT_SAVE_PATH: {DOCUMENT_SAVE_PATH}, HWC_BUCKET_NAME: {HWC_BUCKET_NAME}"
)

# 定义搜索参数
search_params = {
    "metric_type": "L2",  # 使用欧氏距离
    "params": {"nprobe": 10},  # 检查 10 个聚类
}

# 定义空间距离阈值
similarity_threshold = 0.15  # 空间距离空间距离 0.15


def clean_collection(collection, now):
    # 查询所有向量，并指定 limit 参数
    all_vectors = collection.query(
        expr="", output_fields=["pk", "vector", "text"], limit=10000
    )

    # 创建一个主键到向量和文本的映射
    all_vector_dict = {
        vec["pk"]: {
            "vector": vec["vector"],
            "text": vec["text"],
        }
        for vec in all_vectors
    }

    # 打印向量数量
    logging.info(f"集合: {collection.name} 向量数量: {len(all_vector_dict)}")

    # 定义一个列表，用于存储已经删除的向量id
    deleted_vector_ids = []

    # 保存删除的向量到文件
    filename = f"vector-delete-{now.strftime('%Y%m%d%H%M%S')}.json"
    filepath = f"{DOCUMENT_SAVE_PATH}/{HWC_BUCKET_NAME}/{collection.name}"
    fullpath = f"{filepath}/{filename}"

    # 如果目录不存在，则创建目录
    if not os.path.exists(filepath):
        os.makedirs(filepath)
    file_write = open(fullpath, "w", encoding="utf-8")

    # 对于每一个向量，进行搜索
    for pk, curr_vector in all_vector_dict.items():
        # 如果当前向量id在已删除列表中，则跳过
        if pk in deleted_vector_ids:
            continue

        # 搜索与其最相似的10个向量
        logging.info(f"开始搜索与{pk}最相似的10个向量")
        results = collection.search(
            data=[curr_vector["vector"]],
            anns_field="vector",  # 指定向量字段
            param=search_params,  # 指定搜索参数
            limit=10,  # 指定返回的数量
            output_fields=["text"],  # 指定返回的字段
        )
        logging.info(f"搜索与{pk}最相似的10个向量完成")

        # 分析搜索结果，如果空间距离小于0.15，则删除
        for result in results[0]:
            # 如果当前向量与搜索向量id相同，则跳过
            if result.id == pk:
                continue

            # 如果空间距离大于0.15，则跳出循环，因为后续也大于0.15
            if result.distance > similarity_threshold:
                break

            # 如果要删除的向量id不在已删除列表中，则添加到已删除列表
            if result.id not in deleted_vector_ids:
                deleted_vector_ids.append(result.id)

            # 删除向量
            collection.delete(f"pk in {[result.id]}")

            # 以json格式保存删除的向量到文件
            file_write.write(f"src_id: {pk}, {result}\n")

    # 关闭文件
    file_write.close()

    # 打印删除的向量数量
    logging.info(f"删除向量数量: {len(deleted_vector_ids)}")


def clean_all_collections(now):
    # 获取所有集合
    logging.info(f"开始获取所有集合")
    collection_list = utility.list_collections()  # 获取所有集合
    logging.info(f"获取所有集合完成，集合数量: {len(collection_list)}")
    for collection_name in collection_list:
        logging.info(f"开始加载集合: {collection_name}")
        collection = Collection(collection_name)  # 遍历每个集合
        collection.load()  # 加载集合
        logging.info(f"加载集合: {collection_name} 完成")
        logging.info(f"开始清理集合: {collection_name}")
        clean_collection(collection, now)
        logging.info(f"集合: {collection_name} 清理完成")


if __name__ == "__main__":
    # 连接 Milvus
    connections.connect("default", host=MILVUS_HOST, port=MILVUS_PORT)

    # 清洗向量
    now = datetime.now()
    clean_all_collections(now)

    # 断开 Milvus 连接
    connections.disconnect("default")
