import asyncio
import uuid

from ..config import settings
from ..database import get_chat_database, get_database
from ..models.user import User
from ..routes.group_route import create_group
from ..schemas.group import GroupCreate
from ..schemas.user import UserResponse
from ..utils.security import get_password_hash
from .database import delete_file_from_database
from .file_handler import delete_file
from .log import logger
from .milvusdb import delete_collection
from .remote_model import system_graphbase_manager


class AdminServer:

    def __init__(self, user: User):
        self.user = user

    def is_admin(self) -> bool:
        """
        # 判断是否为管理员
        :return: bool
        """
        return True

    async def create_user(
        self, username: str, email: str, phone: str, password: str
    ) -> tuple[dict, bool]:
        """
        # 创建或更新管理员用户
        :return: (用户信息, 操作结果)
        """
        if not self.is_admin():
            logger.info("越权使用管理员权限，服务器已记录")
            return {}, False

        try:
            manage_db = await get_database()

            # 检查邮箱和手机号唯一性
            existing_user = None
            if email:
                existing_user = await manage_db.users.find_one(
                    {"email": email, "is_active": True}
                )
            if not existing_user and phone:
                existing_user = await manage_db.users.find_one(
                    {"phone": phone, "is_active": True}
                )

            if existing_user:
                # 更新缺失字段
                update_data = {}
                if email and not existing_user.get("email"):
                    update_data["email"] = email
                if phone and not existing_user.get("phone"):
                    update_data["phone"] = phone
                if update_data:
                    await manage_db.users.update_one(
                        {"id": existing_user["id"]}, {"$set": update_data}
                    )
                return UserResponse(**existing_user).model_dump(), True

            # 创建新用户
            user_id = str(uuid.uuid4())
            hashed_password = get_password_hash(password)

            new_user = User(
                id=user_id,
                username=username,
                email=email,
                phone=phone,
                hashed_password=hashed_password,
                avatar="/static/default_avatar.png",  # 直接设置默认头像路径
                is_active=True,
                friends=[user_id],  # 初始化好友列表包含自己
                verification_code=None,
            )

            # 创建用户记录
            await manage_db.users.insert_one(new_user.model_dump())

            # 创建默认群组（复用现有逻辑）
            await create_group(
                GroupCreate(
                    name=settings.DEFAULT_GROUP_NAME,
                    database_ids=[],
                    member_ids=[user_id],
                    owner_id=user_id,
                    agent_setting={
                        "abbr": [{"origin": "", "abbr": ""}],
                        "model": "",
                        "quality_check": True,
                        "role": "",
                        "integrate": False,
                    },
                ),
                current_user=new_user,
                db=manage_db,
            )

            return UserResponse(**new_user.model_dump()).model_dump(), True

        except Exception as e:
            logger.error(f"管理员创建用户失败: {str(e)}", exc_info=True)
            return {}, False

    async def add_friend(self, user_id_a: str, user_id_b: str):
        """
        # 添加好友
        :param user_id_a: 用户A ID
        :param user_id_b: 用户B ID
        :return: bool
        """
        if not self.is_admin():
            logger.info("越权使用管理员权限，服务器已记录")
            return False

        manage_db = await get_database()

        try:
            # 双向添加好友关系
            await manage_db.users.update_one(
                {"id": user_id_a}, {"$addToSet": {"friends": user_id_b}}
            )
            await manage_db.users.update_one(
                {"id": user_id_b}, {"$addToSet": {"friends": user_id_a}}
            )
            return True
        except Exception as e:
            logger.error(f"添加好友失败: {str(e)}")
            return False

    async def delete_friend(self, friend_id: str):
        """
        # 删除好友
        :param friend_id: 好友id
        :return: bool
        """
        if not self.is_admin():
            logger.info("越权使用管理员权限，服务器已记录")
            return False

        logger.info(f"管理员删除好友: {friend_id}")

        manage_db = await get_database()

        # 把好友从管理员好友列表中删除
        try:
            await manage_db.users.update_one(
                {"id": self.user.id}, {"$pull": {"friends": friend_id}}
            )
            await manage_db.users.update_one(
                {"id": friend_id}, {"$pull": {"friends": self.user.id}}
            )
        except Exception as e:
            logger.error(f"管理员删除好友失败: {str(e)}")
            return True

        return True

    async def delete_database(self, database_id: str) -> bool:
        """
        # 删除数据库
        :param database_id: 数据库id
        :return: bool
        """
        if not self.is_admin():
            logger.info("越权使用管理员权限，服务器已记录")
            return False

        logger.info(f"管理员删除数据库: {database_id}")

        manage_db = await get_database()

        database = await manage_db.databases.find_one(
            {"id": database_id, "is_deleted": False}
        )
        if not database:
            logger.error(f"数据库 {database_id} 不存在")
            return True

        # 解绑知识库绑定的群
        for group_id in database["group_ids"]:
            await manage_db.groups.update_one(
                {"id": group_id}, {"$pull": {"database_ids": database_id}}
            )

        # 删除文件
        files = await manage_db.files.find(
            {"vectordb_id": database_id, "is_deleted": False}
        ).to_list(None)
        # 创建并行删除任务

        semaphore = asyncio.Semaphore(20)  # 并发限制

        async def delete_file_task(file):
            async with semaphore:
                return await delete_file_from_database(
                    file_id=file["id"],
                    database=database,
                    current_user=database["owner_id"],
                    file_size=file["file_size"],
                    delete_database=True,
                )

        delete_tasks = [delete_file_task(file) for file in files]
        # 并行执行所有删除任务
        await asyncio.gather(*delete_tasks)

        await delete_collection(database["v_name"])

        # 删除neo4j数据库
        if await system_graphbase_manager.check_graph_database_exists(database_id):
            await system_graphbase_manager.delete_graph_database(database_id)

        await manage_db.databases.update_one(
            {"id": database_id}, {"$set": {"is_deleted": True}}
        )

        return True

    async def delete_file(self, file_id: str) -> bool:
        """
        # 删除文件
        :param file_id: 文件id
        :return: bool
        """
        if not self.is_admin():
            logger.info("越权使用管理员权限，服务器已记录")
            return False

        logger.info(f"管理员删除文件: {file_id}")

        manage_db = await get_database()
        chat_db = await get_chat_database()

        file = await manage_db.files.find_one({"id": file_id, "is_deleted": False})

        if not file:
            logger.error(f"文件 {file_id} 不存在")
            return True

        uploader = await manage_db.users.find_one({"id": file["uploader_id"]})
        uploader_data = User(**uploader)

        # 删除文件
        if file["vectordb_id"]:
            await delete_file_from_database(
                file_id=file["id"],
                database=file["vectordb_id"],
                current_user=uploader_data,
                file_size=file["file_size"],
            )
        else:
            await delete_file(
                file_id=file["id"],
                current_user=uploader_data,
                file_size=file["file_size"],
            )
            # 获取chat_db中的聊天记录
            if file["group_id"]:
                group_collection = getattr(chat_db, file["group_id"])
                # 删除content中file_id的聊天记录
                await group_collection.delete_many({"content.file_id": file["id"]})

        await manage_db.files.update_one(
            {"id": file_id}, {"$set": {"is_deleted": True}}
        )

        return True

    async def delete_group(self, group_id: str) -> bool:
        """
        # 解散群聊
        :param group_id: 群聊id
        :return: bool
        """
        if not self.is_admin():
            logger.info("越权使用管理员权限，服务器已记录")
            return False

        logger.info(f"管理员解散群聊: {group_id}")

        manage_db = await get_database()

        group = await manage_db.groups.find_one({"id": group_id, "is_dissolved": False})
        if not group:
            logger.error(f"群聊 {group_id} 不存在")
            return True

        # 删除群聊
        await manage_db.groups.update_one(
            {"id": group_id}, {"$set": {"is_dissolved": True}}
        )

        return True

    async def delete_user(self, user_id: str) -> bool:
        """
        # 用户封号
        :param user_id: 用户id
        :return: bool
        """
        if not self.is_admin():
            logger.info("越权使用管理员权限，服务器已记录")
            return False

        logger.info(f"管理员封禁 {user_id}账号")

        manage_db = await get_database()

        user = await manage_db.users.find_one({"id": user_id, "is_active": True})
        user_data = User(**user)

        if not user:
            logger.error(f"用户 {user_id} 不存在")
            return True

        # TODO 把用户所有知识库删除

        # 把用户的所有文件都删除
        files = await manage_db.files.find(
            {"uploader_id": user_id, "is_deleted": False}
        ).to_list(None)
        for file in files:
            if file["vectordb_id"]:
                await delete_file_from_database(
                    file_id=file["id"],
                    database=file["vectordb_id"],
                    current_user=user_data,
                    file_size=file["file_size"],
                )
            else:
                await delete_file(
                    file_id=file["id"],
                    current_user=user_data,
                    file_size=file["file_size"],
                )

        # TODO 把用户踢出所有群

        # TODO 把用户的所有好友删除

        # TODO 把用户的所有聊天记录删除

        # 更新用户状态：不再活跃
        await manage_db.users.update_one(
            {"id": user_id}, {"$set": {"is_active": False}}
        )

        return True

    async def get_all_users(self) -> tuple[list, bool]:
        """
        # 获取所有用户
        :return: list, bool
        """
        if not self.is_admin():
            logger.info("越权使用管理员权限，服务器已记录")
            return [], False

        logger.info(f"管理员获取所有用户列表")

        manage_db = await get_database()

        # 获取所有活跃用户（根据业务需求可以移除is_active过滤）
        users = await manage_db.users.find({"is_active": True}).to_list(None)
        return [User(**user) for user in users], True  # 返回用户列表和成功状态

    async def get_all_databases(self) -> tuple[list, bool]:
        """
        # 获取所有数据库
        :return: list, bool
        """
        if not self.is_admin():
            logger.info("越权使用管理员权限，服务器已记录")
            return [], False

        logger.info(f"管理员获取所有数据库列表")

        manage_db = await get_database()

        # 获取所有数据库（根据业务需求可以移除is_deleted过滤）
        databases = await manage_db.databases.find({"is_deleted": False}).to_list(None)
        return [database for database in databases], True  # 返回数据库列表和成功状态

    async def get_all_groups(self) -> tuple[list, bool]:
        """
        # 获取所有群聊
        :return: list, bool
        """
        if not self.is_admin():
            logger.info("越权使用管理员权限，服务器已记录")
            return [], False

        logger.info(f"管理员获取所有群聊列表")

        manage_db = await get_database()

        # 获取所有群聊（根据业务需求可以移除is_dissolved过滤）
        groups = await manage_db.groups.find({"is_dissolved": False}).to_list(None)
        return [group for group in groups], True  # 返回群聊列表和成功状态

    async def get_all_friends(self) -> tuple[list, bool]:
        """
        # 获取所有好友
        :return: list, bool
        """
        if not self.is_admin():
            logger.info("越权使用管理员权限，服务器已记录")
            return [], False

        logger.info(f"管理员获取所有好友列表")

        manage_db = await get_database()

        friend_ids = self.user.friends

        # 获取所有好友
        friends = await manage_db.users.find(
            {"is_active": True, "id": {"$in": friend_ids}}
        ).to_list(None)

        return [User(**friend) for friend in friends], True  # 返回好友列表和成功状态

    async def get_group_chat(self, group_id: str) -> tuple[list, bool]:
        """
        # 获取群聊聊天记录
        :param group_id: 群聊id
        :return: list, bool
        """
        if not self.is_admin():
            logger.info("越权使用管理员权限，服务器已记录")
            return [], False

        logger.info(f"管理员获取群聊 {group_id} 聊天记录")

        manage_db = await get_database()
        chat_db = await get_chat_database()

        group = await manage_db.groups.find_one({"id": group_id, "is_dissolved": False})
        if not group:
            logger.error(f"群聊 {group_id} 不存在")
            return [], True

        # 获取群聊聊天记录
        group_collection = getattr(chat_db, group_id)
        chat_records = await group_collection.find().to_list(None)

        return chat_records, True  # 返回聊天记录列表和成功状态
