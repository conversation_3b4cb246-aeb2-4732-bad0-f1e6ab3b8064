import asyncio
import json
import re
from io import BytesIO, StringIO

import pandas as pd
from langchain_openai import ChatOpenAI

from ..config import settings
from .file_handler import split_text
from .log import logger


async def excel_to_jsonl(input_content, file_name):
    """
    将Excel文件转换为JSONL格式的知识图谱内容
    参数：
    input_content: 输入的Excel文件内容
    """
    # 初始化JSONL内容
    jsonl_content = []
    seen_triplets = set()
    seen_triplets.add((file_name, "文件名", file_name))
    jsonl_content.append(
        json.dumps(
            {
                "h": file_name,
                "r": "文件名",
                "t": file_name,
            },
            ensure_ascii=False,
        )
    )

    # 读取Excel文件内容，自动识别表头
    try:
        xl = pd.ExcelFile(
            BytesIO(input_content), engine="openpyxl"
        )  # 使用内存中的二进制数据
    except Exception as e:
        logger.error(f"读取Excel文件时出错: {str(e)}")
        return "\n".join(jsonl_content)

    df_raws = []
    for sheet_name in xl.sheet_names:
        df_raw = xl.parse(sheet_name, header=None)  # 先读取原始数据
        df_raws.append(df_raw)

    for sheet_index, df_raw in enumerate(df_raws):

        sheet_name = xl.sheet_names[sheet_index]

        if (file_name, "工作表名", sheet_name) not in seen_triplets:
            seen_triplets.add((file_name, "工作表名", sheet_name))
            jsonl_content.append(
                json.dumps(
                    {
                        "h": file_name,
                        "r": "工作表名",
                        "t": sheet_name,
                    },
                    ensure_ascii=False,
                )
            )

        # 添加sheet与行列数的关系
        valid_row_count = len(df_raw)
        valid_col_count = len(df_raw.columns)
        if (sheet_name, "行数", str(valid_row_count)) not in seen_triplets:
            seen_triplets.add((sheet_name, "行数", str(valid_row_count)))
            jsonl_content.append(
                json.dumps(
                    {"h": sheet_name, "r": "行数", "t": str(valid_row_count)},
                    ensure_ascii=False,
                )
            )
        if (sheet_name, "列数", str(valid_col_count)) not in seen_triplets:
            seen_triplets.add((sheet_name, "列数", str(valid_col_count)))
            jsonl_content.append(
                json.dumps(
                    {"h": sheet_name, "r": "列数", "t": str(valid_col_count)},
                    ensure_ascii=False,
                )
            )

        # 处理全空值的幽灵工作表
        if df_raw.empty or df_raw.isnull().all().all():
            logger.info(f"工作表 '{sheet_name}' 没有有效数据，已跳过")
            continue

        # 跳过0行的工作表
        if len(df_raw) < 1:
            logger.warning(f"工作表 '{sheet_name}' 没有数据行，已跳过")
            continue

        # 查找有效标题行（第一个包含至少两列的非空行）
        header_row = None
        for i in range(min(10, len(df_raw))):  # 最多检查前10行
            current_row = df_raw.iloc[i].dropna()
            if len(current_row) >= 2 and any(str(cell).strip() for cell in current_row):
                header_row = i
                break

        if header_row is None or header_row >= len(df_raw):
            logger.info(f"工作表 '{sheet_name}' 中未找到有效标题行，已跳过")
            continue

        # 重新读取数据，使用找到的标题行
        try:
            df = xl.parse(sheet_name, header=header_row).dropna(axis=1, how="all")
            columns = [str(col).strip() for col in df.columns if str(col).strip()]

            # 列名有效性检查
            if len(columns) < 2:
                logger.warning(
                    f"工作表 '{sheet_name}' 有效列不足（至少需要2列），已跳过"
                )
                continue

            # 新增数据行检查
            if df.empty:
                logger.warning(f"工作表 '{sheet_name}' 没有有效数据行，已跳过")
                continue

            for row_index, row in df.iterrows():
                try:
                    # 第一列作为头实体
                    head_entity = row[columns[0]]

                    if pd.isna(head_entity):
                        logger.info(
                            f"工作表 '{sheet_name}' 第 {row_index+2} 行发现空头实体，已跳过"
                        )
                        continue

                    head_str = str(head_entity)
                    heads = await asyncio.to_thread(split_text, head_str)
                    head = heads[0].page_content
                    # 对关系字段进行分片
                    relation_str = str(columns[0])
                    relations = await asyncio.to_thread(split_text, relation_str)
                    # 为每个分片创建JSON对象
                    for relation in relations:
                        # 连接头实体和sheet名
                        if (
                            sheet_name,
                            relation.page_content,
                            head,
                        ) not in seen_triplets:
                            seen_triplets.add((sheet_name, relation.page_content, head))
                            jsonl_content.append(
                                json.dumps(
                                    {
                                        "h": sheet_name,
                                        "r": relation.page_content,
                                        "t": head,
                                    },
                                    ensure_ascii=False,
                                )
                            )
                        for head_rest in heads[1:]:
                            if (
                                head,
                                relation.page_content,
                                head_rest.page_content,
                            ) not in seen_triplets:
                                seen_triplets.add(
                                    (
                                        head,
                                        relation.page_content,
                                        head_rest.page_content,
                                    )
                                )
                                json_obj = {
                                    "h": head,
                                    "r": relation.page_content,
                                    "t": head_rest.page_content,
                                }
                                jsonl_content.append(
                                    json.dumps(json_obj, ensure_ascii=False)
                                )

                    # 遍历其他列生成关系
                    for col in columns[1:]:
                        tail_value = row[col]

                        # 跳过空值
                        if pd.isna(tail_value):
                            continue

                        # 分片处理逻辑
                        tail_str = str(tail_value)
                        tails = await asyncio.to_thread(split_text, tail_str)
                        rel_str = str(col)
                        rels = await asyncio.to_thread(split_text, rel_str)

                        # 为每个分片创建JSON对象
                        for tail in tails:
                            for rel in rels:
                                if (
                                    head,
                                    rel.page_content,
                                    tail.page_content,
                                ) not in seen_triplets:
                                    seen_triplets.add(
                                        (head, rel.page_content, tail.page_content)
                                    )
                                    json_obj = {
                                        "h": str(head),
                                        "r": rel.page_content,
                                        "t": tail.page_content,  # 使用分片后的内容
                                    }
                                    jsonl_content.append(
                                        json.dumps(json_obj, ensure_ascii=False)
                                    )
                except Exception as row_e:
                    logger.error(
                        f"处理工作表 '{sheet_name}' 第 {row_index+2} 行时发生错误：{str(row_e)}"
                    )
                    continue
        except Exception as sheet_e:
            logger.error(f"解析工作表 '{sheet_name}' 时发生错误：{str(sheet_e)}")
            continue

    return "\n".join(jsonl_content)  # 返回拼接好的JSONL内容


async def csv_to_jsonl(input_content, file_name):
    """
    将csv文件内容转换为JSONL格式的知识图谱内容
    参数：
    input_content: 输入的csv文件内容
    """
    jsonl_content = []
    seen_triplets = set()
    try:
        seen_triplets.add((file_name, "文件名", file_name))
        jsonl_content.append(
            json.dumps(
                {
                    "h": file_name,
                    "r": "文件名",
                    "t": file_name,
                },
                ensure_ascii=False,
            )
        )
        # 尝试多种编码格式
        encodings = ["utf-8", "gbk", "ansi", "latin1"]
        decoded_content = None
        for encoding in encodings:
            try:
                decoded_content = input_content.decode(encoding)
                break
            except UnicodeDecodeError:
                continue

        # 新增空内容检查
        if not decoded_content.strip():
            jsonl_content.extend(
                [
                    json.dumps(
                        {"h": file_name, "r": "行数", "t": 0},
                        ensure_ascii=False,
                    ),
                    json.dumps(
                        {"h": file_name, "r": "列数", "t": 0},
                        ensure_ascii=False,
                    ),
                ]
            )
            logger.info(f"{file_name}内容为空")
            return "\n".join(jsonl_content)

        df = pd.read_csv(StringIO(decoded_content))
        jsonl_content.extend(
            [
                json.dumps(
                    {"h": file_name, "r": "行数", "t": str(len(df))}, ensure_ascii=False
                ),
                json.dumps(
                    {"h": file_name, "r": "列数", "t": str(len(df.columns))},
                    ensure_ascii=False,
                ),
            ]
        )

        # 获取所有列名
        columns = df.columns.tolist()
        if len(columns) < 2:
            logger.info(f"{file_name}不足两列")
            return "\n".join(jsonl_content)

        for _, row in df.iterrows():
            # 第一列作为头实体
            head_entity = row[columns[0]]

            # 空值检查
            if pd.isna(head_entity):
                logger.info(f"发现空头实体，行号 {_ + 1}，已跳过")
                continue

            head_str = str(head_entity)
            heads = await asyncio.to_thread(split_text, head_str)
            head = heads[0].page_content
            # 对关系字段进行分片
            relation_str = str(columns[0])
            relations = await asyncio.to_thread(split_text, relation_str)

            # 为每个分片创建JSON对象
            for relation in relations:
                # 连接头实体和文件名
                if (file_name, relation.page_content, head) not in seen_triplets:
                    seen_triplets.add((file_name, relation.page_content, head))
                    jsonl_content.append(
                        json.dumps(
                            {
                                "h": file_name,
                                "r": relation.page_content,
                                "t": head,
                            },
                            ensure_ascii=False,
                        )
                    )
                for head_rest in heads[1:]:
                    if (
                        head,
                        relation.page_content,
                        head_rest.page_content,
                    ) not in seen_triplets:
                        seen_triplets.add(
                            (head, relation.page_content, head_rest.page_content)
                        )
                        json_obj = {
                            "h": head,
                            "r": relation.page_content,
                            "t": head_rest.page_content,
                        }
                        jsonl_content.append(json.dumps(json_obj, ensure_ascii=False))

            # 遍历其他列生成关系
            for col in columns[1:]:
                tail_value = row[col]

                # 跳过空值
                if pd.isna(tail_value):
                    continue

                # 分片处理逻辑
                tail_str = str(tail_value)
                tails = await asyncio.to_thread(split_text, tail_str)
                rel_str = str(col)
                rels = await asyncio.to_thread(split_text, rel_str)

                # 为每个分片创建JSON对象
                for tail in tails:
                    for rel in rels:
                        if (
                            head,
                            rel.page_content,
                            tail.page_content,
                        ) not in seen_triplets:
                            seen_triplets.add(
                                (head, rel.page_content, tail.page_content)
                            )
                            json_obj = {
                                "h": str(head),
                                "r": rel.page_content,
                                "t": tail.page_content,  # 使用分片后的内容
                            }
                            jsonl_content.append(
                                json.dumps(json_obj, ensure_ascii=False)
                            )

        return "\n".join(jsonl_content)  # 返回拼接好的JSONL内容
    except Exception as e:
        logger.error(f"{file_name}转换为JSONL格式时出错: {str(e)}")
        return "\n".join(jsonl_content)

async def get_triplet_from_text(text,prompt=None):
    """
    从文本中提取三元组
    """
    async def get_result_from_response(response):
        if isinstance(response,list):
            return response
        elif hasattr(response, "content"):
            return response.content
        else:
            if isinstance(response,dict):
                for k,v in response.items():
                    return get_result_from_response(v)
            else:
                return str(response)

    if prompt is None:
        prompt = [
                    {
                        "role": "system",
                        "content": '你是一个专业的关系三元组提取器，请从文本中提取（头实体，关系，尾实体）三元组。输出格式：[["h1", "r1", "t1"], ["h2", "r2", "t2"], ...]',
                    },
                    {
                        "role": "user",
                        "content": f"请从以下文本提取关系三元组：\n{text}\n",
                    },
                ]
    local_llm = ChatOpenAI(
        model=settings.LOCAL_MODEL_NAME,
        api_key=settings.DEEPSEEK_KEY,  # 与本地模型配置一致
        base_url=settings.LOCAL_MODEL_URL,
        temperature=0.1,
    ).with_structured_output(method="json_mode")
    cloud_llm = ChatOpenAI(
        model=settings.DEEPSEEK_MODEL_NAME,
        api_key=settings.DEEPSEEK_KEY,  # 与本地模型配置一致
        base_url=settings.DEEPSEEK_URL,
        temperature=0.1,
    ).with_structured_output(method="json_mode")
    for i in range(3):
        try:
            # 直接调用模型API
            response = await local_llm.ainvoke(prompt)
            # result = (
            #     response.content if hasattr(response, "content") else str(response)
            # )
            result = await get_result_from_response(response)
            if not result:
                # 本地模型没有提取结果时，用云端模型再提一次
                response = await cloud_llm.ainvoke(prompt)
                result = await get_result_from_response(response)
                if result:
                    break
            break
        except Exception as e:
            pass
    
    return result

async def text_to_jsonl(chunks, file_name, doc_uuid):
    """
    将文本内容转换为JSONL格式的知识图谱内容
    参数：
    chunks: 输入的文本内容
    """
    jsonl_content = []
    seen_triplets = set()
    lock = asyncio.Lock()  # 新增异步锁
    semaphore = asyncio.Semaphore(10)  # 创建一个信号量，限制并发数为10
    
    total_chunks = len(chunks)
    logger.info(
        f"正在使用{settings.LOCAL_MODEL_NAME}提取文本关系三元组，共{total_chunks}个文本块..."
    )

    async def process_chunk(num, chunk):
        async with semaphore:
            if num % 10 == 0:
                logger.info(
                    f"{settings.LOCAL_MODEL_NAME}正在处理第{num+1}/{total_chunks}个文本块..."
                )
            try:
                # 解析响应
                try:
                    # # 使用正则表达式提取所有可能的三元组
                    # matches = re.findall(
                    #     r'\["([^"]+)",\s*"([^"]+)",\s*"([^"]+)"\]', result
                    # )
                    # # 格式校验
                    result = await get_triplet_from_text(chunk.page_content)
                    # # 格式校验
                    valid_relations = []
                    for match in result:
                        if len(match) == 3:
                            # 清理可能存在的转义符号
                            h = match[0].replace("\\", "").replace("'", "")
                            r = match[1].replace("\\", "").replace("'", "")
                            t = match[2].replace("\\", "").replace("'", "")
                            valid_relations.append((h, r, t))
                    # 如果没有匹配到有效三元组，尝试备用模式
                    if not valid_relations:
                        backup_matches = re.findall(
                            r'\["([^"]+)",\s*"([^"]+)",\s*"([^"]+)"\]', str(result)
                        )
                        for match in backup_matches:
                            if len(match) == 3:
                                valid_relations.append(
                                    tuple(m.replace('"', "") for m in match)
                                )

                    if not valid_relations:
                        logger.warning(f"文档《{file_name}》片段{doc_uuid}_{num}丢失")

                    # 写入有效数据
                    for h, r, t in valid_relations:
                        async with lock:  # 加锁保护原子操作
                            identifier = (h.strip(), r.strip(), t.strip(), f"{doc_uuid}_{num}")
                            if identifier not in seen_triplets:
                                seen_triplets.add(identifier)
                                jsonl_content.append(
                                    json.dumps(
                                        {
                                            "h": h.strip(),
                                            "r": r.strip(),
                                            "t": t.strip(),
                                            "sequence" : f"{doc_uuid}_{num}"
                                        },
                                        ensure_ascii=False,
                                    )
                                )
                        # 为每个节点添加文件名关联
                        file_relation = "所属文件名"
                        for entity in [h.strip(), t.strip()]:
                            file_id = (entity, file_relation, file_name)
                            async with lock:  # 加锁保护原子操作
                                if file_id not in seen_triplets:
                                    seen_triplets.add(file_id)
                                    jsonl_content.append(
                                        json.dumps(
                                            {
                                                "h": entity,
                                                "r": file_relation,
                                                "t": file_name,
                                                "sequence" : f"{doc_uuid}_{num}"
                                            },
                                            ensure_ascii=False,
                                        )
                                    )
                except Exception as e:
                    logger.error(f"解析模型响应失败: {str(e)}")

            except Exception as e:
                logger.error(f"处理文本内容时发生错误: {str(e)}")

    tasks = [process_chunk(num, chunk) for num, chunk in enumerate(chunks)]
    await asyncio.gather(*tasks)
    return "\n".join(jsonl_content)
