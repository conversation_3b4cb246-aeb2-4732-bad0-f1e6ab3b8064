from ..utils.log import logger
from ..config import settings

from tencentcloud.common import credential
from tencentcloud.common.exception.tencent_cloud_sdk_exception import (
    TencentCloudSDKException,
)

# 导入对应产品模块的client models。
from tencentcloud.sms.v20210111 import sms_client, models


async def send_verification_phone(phone: str, code=str):
    """### 调用公有云短信接口发送手机验证码"""
    try:
        sms = Tencent_sms()
        sms.send_sms(phone, code)
        return True
    except Exception as e:
        logger.error(f"发送短信验证码时出错: {str(e)}", exc_info=True)
        raise Exception("发送短信验证码失败，请稍后重试")


class Tencent_sms:
    def __init__(self):
        self.cred = credential.Credential(settings.TC_SECRET_ID, settings.TC_SECRET_KEY)
        self.client = sms_client.SmsClient(self.cred, "ap-guangzhou")
        self.req = models.SendSmsRequest()
        self.req.SmsSdkAppId = settings.TC_SmsSdkAppId
        self.req.SignName = settings.TC_SmsSignName
        self.req.TemplateId = settings.TC_SmsTemplateId

    def send_sms(self, phone, code):
        self.req.PhoneNumberSet = [phone]
        self.req.TemplateParamSet = [code]
        try:
            resp = self.client.SendSms(self.req)
            logger.info(resp.to_json_string(indent=2))
            return True
        except TencentCloudSDKException as e:
            logger.error(e)
            return False
