# 导入BM25算法库
from ..utils.log import logger
from ..config import settings
from pymilvus import connections, Collection
from rank_bm25 import BM25Okapi
from langchain_core.documents import Document
from ..database import get_database
import numpy as np
import jieba



class BM25IndexManager:
    def __init__(self, milvus_host, milvus_port):
        self.bm25_index = {}
        self.doc_mapping = {}  # 映射BM25索引位置到文档ID
        self.tokenized_corpus = {}
        self.raw_corpus = {}
        # 初始化连接
        connections.connect(
            alias="default",
            host=milvus_host,
            port=milvus_port,
        )

    def normalize_scores(self, scores: np.ndarray, method: str = 'min_max') -> np.ndarray:
        """
        对 BM25 分数进行归一化处理
        
        Args:
            scores: BM25原始分数
            method: 归一化方法，可选 'min_max' 或 'softmax'
            
        Returns:
            归一化后的分数数组
        """
        if len(scores) == 0:
            return scores
            
        if method == 'min_max':
            min_score = np.min(scores)
            max_score = np.max(scores)
            if max_score == min_score:
                return np.ones_like(scores)
            return (scores - min_score) / (max_score - min_score)
        
        elif method == 'softmax':
            exp_scores = np.exp(scores - np.max(scores))  # 减去最大值防止数值溢出
            return exp_scores / exp_scores.sum()
        
        else:
            raise ValueError(f"Unsupported normalization method: {method}")

    def have_index(self, collection_name):
        if collection_name in self.bm25_index:
            return True
        else:
            return False
        
    def build_index(self, collection_name):
        """从Milvus中获取指定集合的文档并构建BM25索引"""

        # 获取Collection对象，不存在直接返回
        try:
            collection = Collection(collection_name)
            collection.load()
        except:
            return False
        collection_fields = [field.name for field in collection.schema.fields]
        output_fields = ["text", "doc_uuid"]
        if "file_name" in collection_fields:
            output_fields.append("file_name")
        # 获取字段
        results = collection.query(
            expr="",
            output_fields=output_fields,
            limit=10000
        )

        if not results: 
            return False

        print(f"create {collection_name} bm25 index, doc count: {len(results)}")
        # 提取字段内容
        documents = []
        for res in results:
            if "file_name" in res:
              content = f'[{res["file_name"]}]: {res["text"]}'
            else:
              content = f'{res["text"]}'
            documents.append(content)
        doc_ids = [res["doc_uuid"] for res in results]

        # 保存原始文档
        self.raw_corpus[collection_name] = documents
        self.doc_mapping[collection_name] = {i: doc_id for i, doc_id in enumerate(doc_ids)}

        # 分词
        tokenized_docs = [list(jieba.cut(doc)) for doc in documents]
        self.tokenized_corpus[collection_name] = tokenized_docs

        # 构建BM25索引
        self.bm25_index[collection_name] = BM25Okapi(tokenized_docs)
        return True
        
    def search(self, collection_name, query, top_k=5, normalize_method: str = 'min_max'):
        """使用BM25检索相关文档"""
        if collection_name not in self.bm25_index:
            return []
        
        # 对查询进行分词
        tokenized_query = list(jieba.cut(query))
        
        # 获取BM25得分
        bm25_scores = self.bm25_index[collection_name].get_scores(tokenized_query)
        # 归一化分数
        normalized_scores = self.normalize_scores(bm25_scores, method=normalize_method)
        
        # 获取得分最高的文档索引
        top_indices = np.argsort(bm25_scores)[-top_k:][::-1]
        
        # 返回结果
        results = []
        for idx in top_indices:
            if normalized_scores[idx] > 0:  # 只返回有相关性的结果
                results.append(Document(
                    page_content=self.raw_corpus[collection_name][idx], 
                    metadata={'doc_uuid': self.doc_mapping[collection_name][idx], 
                              'score': float(normalized_scores[idx])}
                    )
                )
        
        return results

class RealTimeBM25Sreacher(BM25IndexManager):
    def __init__(self,origin_content,query_content):
        self.origin_content = origin_content
        self.query_content = query_content

    def search(self,top_k, search_key, normalize_method: str = 'min_max'):
        # 分词
        tokenized_docs = [list(jieba.cut(doc[search_key])) for doc in self.origin_content]
        bm25search = BM25Okapi(tokenized_docs)
        tokenized_query = list(jieba.cut(self.query_content))
         # 获取BM25得分
        bm25_scores = bm25search.get_scores(tokenized_query)
        # 归一化分数
        normalized_scores = self.normalize_scores(bm25_scores, method=normalize_method)
        
        # 获取得分最高的文档索引
        top_indices = np.argsort(bm25_scores)[-top_k:][::-1]
        # 返回结果
        results = []
        for idx in top_indices:
            if normalized_scores[idx] > 0:  # 只返回有相关性的结果
                content = self.origin_content[idx].copy()
                content["score"] = float(normalized_scores[idx])
                results.append(content)
        return results

class BM25FaqIndexManager(BM25IndexManager):
    def __init__(self):
        self.bm25_index = {}
        self.raw_corpus = {}

    async def extract_faq_data(self, vectordb_id):
        results = []
        manage_db = await get_database()
        
        async for doc in manage_db.files.find({'vectordb_id': vectordb_id}):
            file_name = doc.get('file_name', '')
            faq_set = doc.get('faq_set', [])
            
            for first_class in faq_set:
                first_class_title = first_class.get('first_class_title', '')
                sub_sections = first_class.get('subSections', [])
                
                for section in sub_sections:
                    second_class_title = section.get('second_class_title', '')
                    for qa in section.get('qa_set', []):
                        question = qa.get('question') or qa.get('quetion')  # 容错处理
                        answer = qa.get('answer', '')
                        if question:
                            results.append(f'【{file_name}】【{first_class_title}】【{second_class_title}】【{question}】{answer}')
        
        return results


    async def search(self, vectordb_id, query, top_k, normalize_method: str = 'min_max'):
        if vectordb_id not in self.bm25_index:
            # 存储原始文档
            doc_list = await self.extract_faq_data(vectordb_id)
            if 0 == len(doc_list): return []
            self.raw_corpus[vectordb_id] = doc_list
            # 分词 构建BM25索引
            tokenized_docs = [list(jieba.cut(doc)) for doc in doc_list]
            self.bm25_index[vectordb_id] = BM25Okapi(tokenized_docs)

        # 对查询进行分词
        tokenized_query = list(jieba.cut(query))
        
        # 获取BM25得分
        bm25_scores = self.bm25_index[vectordb_id].get_scores(tokenized_query)
        # 归一化分数
        normalized_scores = self.normalize_scores(bm25_scores, method=normalize_method)
        
        # 获取得分最高的文档索引
        top_indices = np.argsort(bm25_scores)[-top_k:][::-1]
        
        # 返回结果
        results = []
        for idx in top_indices:
            if normalized_scores[idx] > 0:  # 只返回有相关性的结果
                results.append(Document(
                    page_content=self.raw_corpus[vectordb_id][idx], 
                    metadata={'score': float(normalized_scores[idx])}
                    )
                )
        
        return results

BM25_MANAGER = BM25IndexManager(settings.MILVUS_HOST, settings.MILVUS_PORT)
BM25_FAQ_MANAGER = BM25FaqIndexManager()

## 如果集合没有建立BM25索引，则获取所有文档建立
#if not BM25_MANAGER.have_index(collection_data["v_name"]):
#    BM25_MANAGER.build_index(collection_data["v_name"])
#
## BM25搜索TOP N匹配
#bm25_docs = BM25_MANAGER.search(collection_data["v_name"], question, top_k=10)
#all_docs.extend(bm25_docs)
