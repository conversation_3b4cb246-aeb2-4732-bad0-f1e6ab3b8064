from fastapi import  Request
from starlette.middleware.base import BaseHTTPMiddleware
import gettext
import contextvars
from typing import Callable

# 存储当前请求或连接的 _ 函数
gettext_context: contextvars.ContextVar[Callable[[str], str]] = contextvars.ContextVar("gettext_context", default=lambda s: s)

def set_gettext(_func):
    gettext_context.set(_func)

async def get_translator():
    return gettext_context.get()


def get_translator_func(lang: str):
    try:
        translator = gettext.translation("messages", localedir="locales", languages=[lang])
        return translator.gettext
    except FileNotFoundError:
        return lambda s: s

class TranslationMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        lang = request.headers.get("Accept-Language", "zh-CN").split(",")[0]

        try:
            _ = get_translator_func(lang)
            request.state._ = _
            set_gettext(_)
        except FileNotFoundError:
            request.state._ = lambda s: s  # fallback: return original text

        response = await call_next(request)
        return response