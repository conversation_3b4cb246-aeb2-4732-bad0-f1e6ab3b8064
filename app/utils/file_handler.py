import asyncio
import os
import re
import subprocess
from io import Bytes<PERSON>
from typing import Optional

import pandas as pd
from fastapi import UploadFile
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_core.documents import Document
from PIL import Image
from pydub import AudioSegment
from tika import parser
from transformers import AutoTokenizer

from ..config import settings
from ..database import get_database
from ..models.file import File as FileModel
from ..models.user import User
from .cloudstorage import qicaioss
from .log import logger, pdf_warning_filter
from .remote_model import chat_model
from .locales import get_translator
from .tts import TextToSpeech
from unstructured.partition.auto import partition
from unstructured.chunking.title import chunk_by_title
from unstructured.documents.elements import Table
from unstructured.chunking.basic import chunk_elements

class UnstructuredDocHandler:

    """
    用于替代tika解析的非结构化文档支持类\n
    (1)支持文档类型https://docs.unstructured.io/open-source/introduction/supported-file-types

    """
    def __init__(self,file_path,strategy="auto"):
        with pdf_warning_filter:
            self.elements = partition(filename=file_path,strategy=strategy)

    def seperate_by_title(self,new_after_n_chars=1000,max_characters=1500,combine_text_under_n_chars=100):
        """
        将文档分割为标题和内容
        :param new_after_n_chars: 新标题后字符数
        :param max_characters: 最大字符数
        :param combine_text_under_n_chars: 合并文本的最小字符数
        :return:
        """
        chunks = chunk_by_title(self.elements,
                                multipage_sections=False,
                                combine_text_under_n_chars=combine_text_under_n_chars,
                                max_characters=max_characters,
                                new_after_n_chars=new_after_n_chars)
        return chunks
    
    def seperate(self,max_characters,new_after_n_chars):
        chunks = chunk_elements(self.elements,
                                max_characters=max_characters,
                                new_after_n_chars=new_after_n_chars)
        return chunks

tokenizer = AutoTokenizer.from_pretrained(
    rf"{settings.MODEL_root_dir}/bce-embedding-base_v1"
)


def get_audio_duration(file_path):
    """
    获取音频文件的时长（秒）
    :param file_path: 音频文件路径
    :return: 音频时长（秒）
    """
    try:
        # 加载音频文件
        audio = AudioSegment.from_file(file_path)
        # 获取时长（秒）
        duration = audio.duration_seconds
        return duration
    except Exception as e:
        logger.error(f"获取音频{file_path}时长时出错: {e}")
        return 0


async def validate_file(
    file: UploadFile,
    allow_extensions: list,
    max_file_size: int,
) -> bool:
    # 检查文件大小
    file.file.seek(0, 2)
    size = file.file.tell()
    file.file.seek(0)
    if max_file_size:
        # 添加文件类型判断
        if any(ext in file.filename.lower() for ext in [".mp3", ".wav"]):
            # 对于 mp3 和 wav 文件，限制大小为 1GB
            if size > 1 * 1024 * 1024 * 1024:  # 1GB
                raise ValueError(f"{file.filename}mp3或wav文件大小超过限制")
        else:
            if size > max_file_size:
                raise ValueError(
                    f"{file.filename}文件大小超过限制，请上传小于{int(max_file_size/1024/1024)}MB的文件"
                )

    await file.seek(0)

    if not any(ext in file.filename.lower() for ext in allow_extensions):
        raise ValueError(f"不支持的文件类型：{file.filename}")

    return True


async def compress_image(image_data, quality=0.5):
    """
    压缩图片像素
    :param image_data: 图片二进制数据
    :param quality: 压缩质量，取值范围为0-1，默认为0.5
    :return: 压缩后的图片二进制数据
    """
    image = Image.open(BytesIO(image_data))
    output_io = BytesIO()
    image.save(
        output_io, format=image.format, quality=int(quality * 100), optimize=True
    )
    output_io.seek(0)
    return output_io.getvalue()


def bce_token_count(text: str) -> int:
    # 使用bce-embedding-base_v1模型的分词器计算文本的token数量
    tokens = tokenizer.encode(text)
    return len(tokens)


def split_text(large_text, size=500, overlap=150):
    text_splitter = RecursiveCharacterTextSplitter(
        chunk_size=size,  # 每个块的字符数
        chunk_overlap=overlap,  # 块之间的重叠字符数
        length_function=bce_token_count,  # 指定token计算函数
        add_start_index=True,
    )
    try:
        texts = text_splitter.split_text(large_text)
        chunks = []
        metadata = {}
        for i in texts:
            chunks.append(Document(page_content=i, metadata=metadata))
        return chunks
    except Exception as e:
        logger.error(f"输入内容格式异常: {str(e)}", exc_info=True)
        raise RuntimeError(f"输入内容格式异常: {str(e)}")


def split_excel_text(file_path, max_tokens=512):
    chunks = []
    try:
        xls = pd.ExcelFile(file_path)

        for sheet_name in xls.sheet_names:
            df = pd.read_excel(xls, sheet_name=sheet_name, dtype=str).fillna("")

            # sheet 信息头
            sheet_header = f"工作簿：{os.path.basename(file_path)} | 工作表: {sheet_name}"

            # 获取前5行数据
            first_5_rows = df.head(5).to_string(index=False)

            # 获取表格的标题
            header_row = "    " + "\t".join(df.columns.astype(str))  # 标题行

            # 调用大模型获取表头建议
            suggested_headers = chat_model.get_excel_headers_from_llm(first_5_rows)

            # 如果获取到有效建议且列数匹配，则使用新表头
            if suggested_headers:
                header_row = "    " + "\t".join(suggested_headers)
                print(f"工作表 '{sheet_name}' 使用优化表头: {suggested_headers}")

            # 初始化分片变量
            current_chunk = [
                sheet_header,
                header_row,
            ]  # 存储当前分片内容，包含 sheet 标题和表格标题行
            current_tokens = bce_token_count(sheet_header) + bce_token_count(
                header_row
            )  # 计算 token 数量

            for _, row in df.iterrows():
                row_text = "    " + "\t".join(row.astype(str))  # 行内容，4 个空格缩进
                row_tokens = bce_token_count(row_text)

                # 如果加上当前行会超过 max_tokens，则保存当前分片并创建新的分片
                if current_tokens + row_tokens > max_tokens:
                    chunks.append(
                        Document(page_content="\n".join(current_chunk), metadata={})
                    )
                    current_chunk = [
                        sheet_header,
                        header_row,
                        row_text,
                    ]  # 新分片，带上 sheet 标题和表格标题行
                    current_tokens = (
                        bce_token_count(sheet_header)
                        + bce_token_count(header_row)
                        + row_tokens
                    )
                else:
                    # 继续累加当前分片
                    current_chunk.append(row_text)
                    current_tokens += row_tokens

            # 保存最后一个分片
            if current_chunk:
                chunks.append(Document(page_content="\n".join(current_chunk), metadata={}))
    except Exception as e:
        logger.error(f"文档{file_path}解析异常")

    return chunks


async def clean_text(text):
    if text is None:
        return ""
    """文本清洗：去掉特殊字符、空白行、冗余符号"""
    text = re.sub(r"\n\s*\n", "\n", text)  # 去除多余空格
    text = re.sub(r"(?<![-=])\s{2,}(?![-=])", " ", text)  # 只精简普通字符间的空格
    text = re.sub(r"[\u200b-\u200f]", "", text)  # 处理隐藏的不可见字符
    return text.strip()

def parse_unstructured_file(file_path):
    with pdf_warning_filter:
        doc_loader = UnstructuredDocHandler(file_path)
        parsed = doc_loader.seperate_by_title(
                            new_after_n_chars=6500,
                            max_characters=8000,
                            combine_text_under_n_chars=1000
                            )
    return parsed

async def identify_unstructured_file(file_path,_):
    try:
        logger.debug(f"文档{file_path}开始纯文本解析")
        file_name = os.path.basename(file_path)
        parsed = await asyncio.to_thread(
                        parse_unstructured_file,
                        file_path,
                    )
        logger.debug(f"文档{file_path}结束纯文本解析")
        content = [part.metadata.text_as_html if isinstance(part,Table) else part.text for part in parsed]
        return _("文档《{file_name}》内容：{content}").format(file_name=file_name, content=''.join(content))
    except Exception as e:
        logger.error(f"文档{file_path}纯文本解析异常: {str(e)}")
        raise RuntimeError(f"文件解析异常: {str(e)}")

async def identify_file(file_path):
    try:
        logger.info(f"文档{file_path}开始解析")
        _ = await get_translator
        # 设置重试次数
        max_retries = 3
        retry_count = 0
        file_name = os.path.basename(file_path)

        # 判断文件是为 .xlsx 格式
        if file_path.lower().endswith(".xlsx"):
            logger.info(f"文件 {file_path} 是 Excel ，开始使用split_excel_text解析")
            st = await asyncio.to_thread(split_excel_text, file_path)
            logger.info(f"文件 {file_path} 是 Excel ，split_excel_text解析结束")
            return st

        while retry_count < max_retries:
            try:
                parsed = await asyncio.to_thread(
                    parser.from_file,
                    file_path,
                    serverEndpoint=settings.TIKA_SERVER,
                    requestOptions={"timeout": 600},
                )
                logger.info(f"文档{file_path}结束解析")
                large_text = await clean_text(parsed["content"])
                return _("文档《{file_name}》内容：{content}").format(file_name=file_name, content=large_text)

            except Exception as e:
                retry_count += 1
                if retry_count >= max_retries:
                    raise e
                logger.warning(f"文档解析失败，正在进行第{retry_count}次重试: {str(e)}")
                await asyncio.sleep(1)  # 重试前等待1秒

    except Exception as e:
        logger.error(f"文档{file_path}解析异常: {str(e)}", exc_info=True)
        raise RuntimeError(f"文件解析异常: {str(e)}")

async def identify_and_load_unstructured_doc(file_path):
    try:
        st = []
        if file_path.lower().endswith(".xlsx"):
            logger.info(f"文件 {file_path} 是 Excel ，开始使用split_excel_text解析")
            st = await asyncio.to_thread(split_excel_text, file_path)
            logger.info(f"文件 {file_path} 是 Excel ，split_excel_text解析结束")
            return st
        logger.debug(f"文档{file_path}开始解析")
        parsed = await asyncio.to_thread(
                    parse_unstructured_file,
                    file_path,
                )
        # metadata = {}
        # for i in parsed:
        #     st.append(Document(page_content=i.text, metadata=metadata))
        logger.debug(f"文档{file_path}结束解析")
        large_text = await clean_text(''.join([i.text for i in parsed]))
        logger.info(f"文档{file_path}开始分片")
        st = await asyncio.to_thread(split_text, large_text)
        logger.info(f"文档{file_path}结束分片")
        return st
    except Exception as e:
        logger.error(f"文档{file_path}解析异常: {str(e)}", exc_info=True)
        raise RuntimeError(f"文件解析异常: {str(e)}")

async def identify_and_load(file_path):
    try:
        logger.info(f"文档{file_path}开始解析")

        # 设置重试次数
        max_retries = 3
        retry_count = 0

        # 判断文件是为 .xlsx 格式
        if file_path.lower().endswith(".xlsx"):
            logger.info(f"文件 {file_path} 是 Excel ，开始使用split_excel_text解析")
            st = await asyncio.to_thread(split_excel_text, file_path)
            logger.info(f"文件 {file_path} 是 Excel ，split_excel_text解析结束")
            return st

        while retry_count < max_retries:
            try:
                parsed = await asyncio.to_thread(
                    parser.from_file,
                    file_path,
                    serverEndpoint=settings.TIKA_SERVER,
                    requestOptions={"timeout": 600},
                )

                # if parsed.get("content") is None:
                #     raise Exception("解析结果为空")
                logger.info(f"文档{file_path}结束解析")
                large_text = await clean_text(parsed["content"])
                logger.info(f"文档{file_path}开始分片")
                st = await asyncio.to_thread(split_text, large_text)
                logger.info(f"文档{file_path}结束分片")
                return st

            except Exception as e:
                retry_count += 1
                if retry_count >= max_retries:
                    raise e
                logger.warning(f"文档解析失败，正在进行第{retry_count}次重试: {str(e)}")
                await asyncio.sleep(1)  # 重试前等待1秒

    except Exception as e:
        logger.error(f"文档{file_path}解析异常: {str(e)}", exc_info=True)
        raise RuntimeError(f"文件解析异常: {str(e)}")


def document_obj_handler():
    """
    # 向量数据库只储存doc_uuid,文件名,向量，文本\n
    检查document对象字段，抽取相应数据

    :return:
    """


async def download_file(cloud_file_path: str) -> str:
    """
    从华为云OBS下载文件到本地

    Args:
        cloud_file_path (str): 云存储中的文件路径 (例如: "user_uuid/doc_uuid/filename.pdf")

    Returns:
        str: 下载到本地的文件路径

    Raises:
        Exception: 当下载失败时抛出异常
    """
    try:
        # 构建本地文件保存路径
        local_file_path = f"{settings.DOCUMENT_SAVE_PATH}/{settings.HWC_BUCKET_NAME}/{cloud_file_path}"
        local_dir_path = os.path.dirname(local_file_path)

        # 确保本地存储目录存在
        if not os.path.exists(local_dir_path):
            os.makedirs(local_dir_path)

        # 调用华为云OBS的下载方法
        resp = qicaioss.download_file(
            cloud_file_path=cloud_file_path, local_file_path=local_file_path
        )

        if resp:
            logger.info(f"文件下载成功: {local_file_path}")
            return local_file_path
        else:
            raise Exception(f"文件下载失败：{resp}")

    except Exception as e:
        logger.error(f"文件下载失败: {str(e)}", exc_info=True)
        raise Exception(f"文件下载失败: {str(e)}")


async def list_avatar_files():
    """
    获取存储桶中avatar目录下的所有图片文件
    :return: 文件名列表
    """
    try:
        # 使用obsClient列出指定目录下的所有对象
        resp = qicaioss.obsClient.listObjects(bucketName="agent-ai", prefix="avatar/")

        # 过滤出图片文件
        image_files = []
        if resp.status < 300:  # 确保请求成功
            for content in resp.body.contents:
                file_name = content.key
                # 检查是否为图片文件
                if file_name.lower().endswith((".png", ".jpg", ".jpeg")):
                    # 只返回文件名，不包含路径
                    image_files.append(file_name)

        return image_files
    except Exception as e:
        logger.error(f"获取头像文件列表失败: {str(e)}")
        return []


async def download_default_avatars(cloud_file_paths: list) -> list:

    try:
        # 构建本地文件保存路径
        local_dir_path = "/static/"

        # 确保本地存储目录存在
        if not os.path.exists(local_dir_path):
            os.makedirs(local_dir_path)

        # 调用华为云OBS的下载方法
        for file_path in cloud_file_paths:
            resp = qicaioss.download_file(file_path)
            if resp.status < 300:
                logger.info(f"文件下载成功: {file_path}")
            else:
                raise Exception(f"文件下载失败：{resp.errorMessage}")
    except Exception as e:
        logger.error(f"文件下载失败: {str(e)}", exc_info=True)
        raise Exception(f"文件下载失败: {str(e)}")


async def save_speech_file(text: str, user_data: User, doc_uuid, db):
    """
    ## 将文本转为语音文件进行保存
    """
    user_data = User(**user_data)
    # 云储存路径定义(部门id(如有)+用户id+文档id)
    cloud_dir_path = (
        f"{user_data.department_id}/"
        if user_data.department_id not in [None, ""]
        else "" + f"{user_data.id}/{doc_uuid}/{doc_uuid}.wav"
    )
    # 本地储存路径定义(保存根目录+储存名称+云储存路径)
    local_dir_path = os.path.normpath(
        f"{settings.DOCUMENT_SAVE_PATH}/{settings.HWC_BUCKET_NAME}/{cloud_dir_path}"
    )
    tts_model = TextToSpeech()
    await asyncio.to_thread(tts_model.generate_sound_file, text, local_dir_path)
    with open(local_dir_path, "rb") as f:
        file_content = f.read()
    file_header = {
        "filename": f"{doc_uuid}.wav",
        "size": os.path.getsize(local_dir_path),
        "content_type": "audio/wave",
    }
    file_path = await save_file(
        file_content, user_data, doc_uuid, db, file_header=file_header
    )

    return file_path


async def save_file(
    file: bytes,
    user_data: User,
    doc_uuid: str,
    db: get_database,
    file_header: Optional[dict] = None,
    database_id: Optional[str] = None,
    group_id: Optional[str] = None,
) -> str:
    """
    # 保存文件到本地(自动创建文件夹)及云端
    :param file: bytes数据
    :param user_data: 用户数据
    :param doc_uuid: 文档id
    :param db: 数据库连接
    :param file_header: 文件头信息
    :param database_id: 数据库id
    :return: 本地保存文档绝对路径
    """
    # 对文件名进行处理，移除或替换可能导致问题的字符
    safe_filename = "".join(
        char for char in file_header["filename"] if char.isalnum() or char in "._- "
    )
    # 云储存路径定义(部门id(如有)+用户id+文档id)
    cloud_dir_path = (
        f"{user_data.department_id}/"
        if user_data.department_id not in [None, ""]
        else "" + f"{user_data.id}/{doc_uuid}"
    )
    # 本地储存路径定义(保存根目录+储存名称+云储存路径)
    local_dir_path = os.path.normpath(
        f"{settings.DOCUMENT_SAVE_PATH}/{settings.HWC_BUCKET_NAME}/{cloud_dir_path}"
    )
    # 自动创建本地文件夹
    if not os.path.exists(local_dir_path):
        os.makedirs(local_dir_path)
    # 定义本地储存文档绝对路径
    local_file_path = os.path.normpath(os.path.join(local_dir_path, safe_filename))
    # 定义云储存文档绝对路径
    cloud_file_path = os.path.normpath(os.path.join(cloud_dir_path, safe_filename))
    # 写入本地文件
    with open(local_file_path, "wb") as buffer:
        # 图片文件进行压缩
        if safe_filename.lower().endswith((".png", ".jpg", ".jpeg")):
            # 压缩图片像素
            file = await compress_image(file, settings.HEAD_COMPRESS_RATIO)
        buffer.write(file)
    # .webm音频文件强制转为.mp3格式
    if safe_filename.lower().endswith((".webm")):
        safe_filename = safe_filename.replace(".webm", ".mp3")
        mp3_local_file_path = os.path.normpath(os.path.join(local_dir_path, safe_filename))
        # 通过ffmpeg进行转换音频
        cmd = [
            "ffmpeg",
            "-i",
            local_file_path,
            "-b:a",
            "192k",
            mp3_local_file_path
        ]

        try:
            subprocess.run(cmd, check=True)
            # 将文件路径改为mp3
            local_file_path = os.path.normpath(os.path.join(local_dir_path, safe_filename))
        except subprocess.CalledProcessError as e:
            logger.error(f"音频文件{local_file_path}转换过程中出现错误：{e}")
    # 调用储存桶上传文件函数进行上传
    qicaioss.upload_file(
        cloud_file_path=cloud_file_path, local_file_path=local_file_path
    )
    # 定义文件头信息
    if file_header is not None:
        file_size = (
            file_header["size"] if file_header.get("size", None) is not None else 0
        )
        file_type = (
            file_header["content_type"]
            if file_header.get("content_type", None) is not None
            else "text/plain"
        )
    else:
        file_size = 0
        file_type = "text/plain"
    await db.users.update_one(
        {"id": user_data.id},
        {"$inc": {"user_service.filestorage_usage": file_size / 1024 / 1024 / 1024}},
    )
    # logger.
    # 保存数据库记录
    new_file = FileModel(
        id=doc_uuid,
        file_name=safe_filename,
        file_size=file_size,
        file_type=file_type,
        cloud_file_path=cloud_file_path,
        local_file_path=local_file_path,
        uploader_id=user_data.id,
        vectordb_id=database_id,
        group_id=group_id,
    )
    await db.files.insert_one(new_file.model_dump())
    return local_file_path


async def delete_file(
    file_id: str,
    current_user: User,
    file_size: int,
):
    """
    # 删除文件
    :param user_data: 用户数据
    :param doc_uuid: 文档id
    :param db: 数据库连接
    :return: 是否删除成功
    """
    try:
        manage_db = await get_database()

        # 从doc_uuid在mongodb中找到文件
        file_record = await manage_db.files.find_one({"id": file_id})

        # 删除本地文件
        if os.path.exists(file_record["local_file_path"]):
            os.remove(file_record["local_file_path"])

        # 调用储存桶删除文件函数进行删除
        qicaioss.delete_file(file_record["cloud_file_path"])

        # 更新用户储存使用
        await manage_db.users.update_one(
            {"id": current_user.id},
            {
                "$inc": {
                    "user_service.filestorage_usage": -(
                        float(file_size) / 1024 / 1024 / 1024
                    )
                }
            },
        )

        return True
    except Exception as e:
        logger.error(f"删除文件失败: {str(e)}")
        return False
