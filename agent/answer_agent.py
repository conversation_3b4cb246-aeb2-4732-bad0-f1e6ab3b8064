from smolagents import MCPClient,ToolCallingAgent,OpenAIServerModel

model = OpenAIServerModel(
    api_base="https://dashscope.aliyuncs.com/compatible-mode/v1",
    model_id="qwen-max-latest",
    api_key="",
    temperature=0.7
)

with MCPClient({"url": "http://127.0.0.1:18124/analytics/mcp/", "transport": "streamable-http"}) as tools:
    agent = ToolCallingAgent(tools=tools, model=model)
    agent.run("我们公司什么时候发放年假？group_id=a868908f-bf56-4dcb-9da1-eb4596f6af21")