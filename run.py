import argparse
import sys

import uvicorn


# 接收命令行关键字参数
def get_command_parser():
    try:
        parser = argparse.ArgumentParser(description="uvicorn启动工具")
        parser.add_argument(
            "-ip", "--host", type=str, default="127.0.0.1", help="主机(默认:127.0.0.1)"
        )
        parser.add_argument(
            "-p", "--port", type=int, default=18124, help="端口号(默认:18124)"
        )
        parser.add_argument(
            "-r",
            "--reload",
            type=bool,
            default=True,
            help="是否开启debug模式(默认:True)",
        )
        parser.add_argument(
            "-w", "--workers", type=int, default=1, help="进程数(默认:1)"
        )
        args = parser.parse_args()
        return args
    except Exception as e:
        print(f"错误:{e}")
        parser.print_help()
        sys.exit(1)


if __name__ == "__main__":
    args = get_command_parser()
    uvicorn.run(
        "app.main:app",
        host=args.host,
        port=args.port,
        reload=args.reload,
        workers=args.workers,
        timeout_keep_alive=300
    )
