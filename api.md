# API 文档

## 1. 用户认证相关接口

### 1.1 发送注册验证码
```http
POST /api/auth/register/send-code

请求参数：
{
    "username": string,  // 用户名
    "email": string     // 用户邮箱
}

响应：
{
    "message": "验证码已发送"
}
```

调用示例：
```javascript
async function sendVerificationCode(username, email) {
    try {
        const response = await fetch('/api/auth/register/send-code', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ username, email })
        });
        const data = await response.json();
        console.log('验证码已发送');
    } catch (error) {
        console.error('发送验证码失败:', error);
    }
}
```

### 1.2 用户注册
```http
POST /api/auth/register

请求参数：
{
    "username": string,      // 用户名
    "email": string,        // 用户邮箱
    "password": string,     // 密码
    "verification_code": string  // 验证码
}

响应：
{
    "id": number,
    "uuid": string,      // 用户唯一标识符
    "username": string,
    "email": string,
    "is_active": boolean
}
```

调用示例：
```javascript
async function register(username, email, password, verificationCode) {
    try {
        const response = await fetch('/api/auth/register', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                username,
                email,
                password,
                verification_code: verificationCode
            })
        });
        const data = await response.json();
        console.log('注册成功:', data);
    } catch (error) {
        console.error('注册失败:', error);
    }
}
```

### 1.3 用户登录
```http
POST /api/auth/login

请求参数：
{
    "email": string,    // 用户邮箱
    "password": string  // 密码
}

响应：
{
    "access_token": string,
    "token_type": "bearer",
    "user": {
        "id": number,
        "uuid": string,      // 用户唯一标识符
        "username": string,
        "email": string,
        "is_active": boolean
    }
}
```

调用示例：
```javascript
async function login(email, password) {
    try {
        const response = await fetch('/api/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                email,
                password
            })
        });
        const data = await response.json();
        // 保存token到localStorage
        localStorage.setItem('token', data.access_token);
        console.log('登录成功:', data);
    } catch (error) {
        console.error('登录失败:', error);
    }
}
```

## 2. WebSocket 聊天接口

### 2.1 建立WebSocket连接
```http
WS /api/chat/ws/{user_uuid}
```

调用示例：
```javascript
class ChatService {
    constructor(userUuid) {
        this.ws = null;
        this.userUuid = userUuid;
    }

    connect() {
        this.ws = new WebSocket(`ws://your-domain/ws/${this.userUuid}`);
        
        this.ws.onopen = () => {
            console.log('WebSocket连接已建立');
        };

        this.ws.onmessage = (event) => {
            const data = JSON.parse(event.data);
            this.handleMessage(data);
        };

        this.ws.onclose = () => {
            console.log('WebSocket连接已关闭');
        };
    }

    // 发送私聊消息
    sendPrivateMessage(targetUuid, content) {
        this.ws.send(JSON.stringify({
            type: 'private',
            target_uuid: targetUuid,
            content: content
        }));
    }

    // 发送群聊消息
    sendGroupMessage(groupUuid, content) {
        this.ws.send(JSON.stringify({
            type: 'group',
            group_uuid: groupUuid,
            content: content
        }));
    }

    // 处理接收到的消息
    handleMessage(data) {
        if (data.type === 'private') {
            console.log(`收到来自 ${data.from_username} 的私聊消息:`, data.content);
        } else if (data.type === 'group') {
            console.log(`收到群 ${data.group_name} 的消息:`, data.content);
        }
    }

    // 关闭连接
    disconnect() {
        if (this.ws) {
            this.ws.close();
        }
    }
}
```

### WebSocket消息格式

#### 私聊消息
```javascript
// 发送格式
{
    "type": "private",
    "target_uuid": string,  // 接收者UUID
    "content": string     // 消息内容
}

// 接收格式
{
    "type": "private",
    "from_uuid": string,      // 发送者UUID
    "from_username": string,  // 发送者用户名
    "content": string        // 消息内容
}
```

#### 群聊消息
```javascript
// 发送格式
{
    "type": "group",
    "group_uuid": string,   // 群组UUID
    "content": string      // 消息内容
}

// 接收格式
{
    "type": "group",
    "from_uuid": string,      // 发送者UUID
    "from_username": string,  // 发送者用户名
    "group_uuid": string,     // 群组UUID
    "group_name": string,     // 群组名称
    "content": string         // 消息内容
}
```

## 3. 群组相关接口

### 3.1 获取群组列表
```http
GET /api/group/list

请求头：
Authorization: Bearer <access_token>

响应：
[
    {
        "id": number,
        "uuid": string,      // 群组唯一标识符
        "name": string,
        "owner_id": number,
        "members": number[]  // 成员ID列表
    }
]
```

### 3.2 获取群组详细信息
```http
GET /api/group/{group_uuid}

请求头：
Authorization: Bearer <access_token>

响应：
{
    "id": number,
    "uuid": string,      // 群组唯一标识符
    "name": string,
    "owner": {          // 群主信息
        "id": number,
        "uuid": string,
        "username": string,
        "email": string,
        "is_active": boolean
    },
    "members": [        // 群成员列表
        {
            "id": number,
            "uuid": string,
            "username": string,
            "email": string,
            "is_active": boolean
        }
    ],
    "is_dissolved": boolean
}
```

### 3.3 通过群名获取群组详细信息
```http
GET /api/group/name/{group_name}

请求头：
Authorization: Bearer <access_token>

响应：
{
    // 响应格式同上
}
```

### 3.4 创建群组
```http
POST /api/group/create

请求头：
Authorization: Bearer <access_token>

请求参数：
{
    "name": string  // 群组名称
}

响应：
{
    "id": number,
    "uuid": string,      // 群组唯一标识符
    "name": string,
    "owner_id": number,
    "members": number[]  // 成员ID列表
}
```

### 3.5 加入群组
```http
POST /api/group/{group_uuid}/join

请求头：
Authorization: Bearer <access_token>

响应：
{
    "message": "加入群组成功"
}
```

### 3.6 退出群组
```http
POST /api/group/{group_uuid}/leave

请求头：
Authorization: Bearer <access_token>

响应：
{
    "message": "退出群组成功"
}
```

### 3.7 解散群组
```http
DELETE /api/group/{group_uuid}

请求头：
Authorization: Bearer <access_token>

响应：
{
    "message": "群组已解散"
}
```

注意事项：
1. 所有HTTP请求在登录后需要在header中携带token
2. 用户和群组的标识符都使用UUID而不是数字ID
3. WebSocket连接使用用户的UUID而不是数字ID
4. 群组解散后会被标记为已解散，而不是真正删除
5. 群主不能退出群组，必须先转让群主或解散群组
6. 不能对已解散的群组进行操作

## 4. 向量数据库相关接口

### 4.1 创建向量数据库
```http
POST /api/database/create

请求头：
Authorization: Bearer <access_token>

请求参数：
{
    "name": string  // 数据库名称
}

响应：
{
    "id": number,
    "name": string,      // 数据库名称
    "owner_id": number,  // 创建者ID
    "created_at": string // 创建时间
}

调用示例：
async function createDatabase(name) {
    try {
        const response = await fetch('/api/database/create', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            },
            body: JSON.stringify({ name })
        });
        const data = await response.json();
        console.log('数据库创建成功:', data);
    } catch (error) {
        console.error('数据库创建失败:', error);
    }
}
```

### 4.2 上传文件到向量数据库
```http
POST /api/upload/{database_id}

请求头：
Authorization: Bearer <access_token>
Content-Type: multipart/form-data

请求参数：
- `database_id`: string  // 向量数据库ID
- `file`: file  // 上传的文件

响应：
{
    "status": "success",
    "message": "文件上传成功",
    "file_id": number  // 文件ID
}

调用示例：
```javascript
async function uploadFile(databaseId, file) {
    const formData = new FormData();
    formData.append('file', file);

    try {
        const response = await fetch(`/api/upload/${databaseId}`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            },
            body: formData
        });
        const data = await response.json();
        console.log('文件上传成功:', data);
    } catch (error) {
        console.error('文件上传失败:', error);
    }
}
```

## 5. 组织和部门相关接口

### 5.1 组织管理

#### 创建组织
```http
POST /api/organizations

请求头：
Authorization: Bearer <access_token>

请求参数：
{
    // OrganizationCreate schema
}

响应：OrganizationResponse
```

#### 获取组织列表
```http
GET /api/organizations

请求头：
Authorization: Bearer <access_token>

响应：List[OrganizationResponse]
```

#### 获取指定组织
```http
GET /api/organizations/{org_uuid}

请求头：
Authorization: Bearer <access_token>

响应：OrganizationResponse
```

#### 删除组织
```http
DELETE /api/organizations/{org_uuid}

请求头：
Authorization: Bearer <access_token>

响应：
{
    "message": "Organization deleted"
}
```

### 5.2 部门管理

#### 创建部门
```http
POST /api/departments

请求头：
Authorization: Bearer <access_token>

请求参数：
{
    // DepartmentCreate schema
}

响应：DepartmentResponse
```

#### 获取组织的部门列表
```http
GET /api/organizations/{org_uuid}/departments

请求头：
Authorization: Bearer <access_token>

响应：List[DepartmentResponse]
```

#### 删除部门
```http
DELETE /api/departments/{dept_uuid}

请求头：
Authorization: Bearer <access_token>

响应：
{
    "message": "Department deleted"
}
```

## 6. 数据库管理接口

### 6.1 数据库操作

#### 创建数据库
```http
POST /api/database/create

请求头：
Authorization: Bearer <access_token>

请求参数：
{
    "name": string  // 数据库名称
}

响应：
{
    "id": number,
    "name": string,
    "owner_id": number,
    "created_at": string
}
```

#### 获取数据库列表
```http
GET /api/database/list

请求头：
Authorization: Bearer <access_token>

响应：Array<Database>
```

#### 获取共享数据库列表
```http
GET /api/database/shared

请求头：
Authorization: Bearer <access_token>

响应：Array<Database>
```

#### 获取指定数据库
```http
GET /api/database/{database_id}

请求头：
Authorization: Bearer <access_token>

响应：Database
```

#### 更新数据库
```http
PUT /api/database/{database_id}

请求头：
Authorization: Bearer <access_token>

请求参数：
{
    "name": string  // 新数据库名称
}

响应：Database
```

#### 删除数据库
```http
DELETE /api/database/{database_id}

请求头：
Authorization: Bearer <access_token>

响应：
{
    "message": "Database deleted successfully"
}
```