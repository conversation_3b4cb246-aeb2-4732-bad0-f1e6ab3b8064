import re
import sys
import os

def extract_quoted_chinese_lines(file_path):
    """提取文件中每行双引号之间的中文内容"""
    pattern = re.compile(r'"(.*?)"')  # 匹配双引号之间的内容
    results = set()
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                # 忽略包含 logger 或 # 的行（不区分大小写）
                if "logger" in line.lower() or "#" in line:
                    continue
                
                # 检查行中是否有中文字符
                if re.search(r'[\u4e00-\u9fa5]', line):
                    # 提取所有双引号中的内容
                    matches = pattern.findall(line)
                    for match in matches:
                        # 只保留包含中文的匹配项
                        if re.search(r'[\u4e00-\u9fa5]', match):
                            results.add(match.strip())  # 去除首尾空格
    except Exception as e:
        print(f"Error reading file {file_path}: {e}")
    
    return results

def process_directory(directory_path):
    """递归遍历目录下的所有文件并提取中文"""
    all_results = set()
    
    for root, dirs, files in os.walk(directory_path):
        for file in files:
            file_path = os.path.join(root, file)
            try:
                if os.path.isfile(file_path):  # 确保是文件
                    file_results = extract_quoted_chinese_lines(file_path)
                    all_results.update(file_results)
            except Exception as e:
                print(f"Error processing file {file_path}: {e}")
                continue
    
    return sorted(all_results, key=lambda x: len(x), reverse=True)  # 按长度排序

def main():
    if len(sys.argv) < 2:
        print("Usage: python extract_chinese.py <directory_path>")
        return
    
    directory_path = sys.argv[1]
    if not os.path.isdir(directory_path):
        print(f"Error: {directory_path} is not a valid directory")
        return
    
    chinese_quotes = process_directory(directory_path)
    
    print(f"从目录 {directory_path} 中提取的双引号中文内容（已去重）：")
    for i, text in enumerate(chinese_quotes, 1):
        print(f"{i}. {text}")

if __name__ == '__main__':
    main()
