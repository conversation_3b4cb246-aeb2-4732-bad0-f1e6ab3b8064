import os
import sys
from typing import Dict

enlang_dict_llm_py = {
    "- 引用：": "- Reference",
    "- 问题：": "- Question",
    "你是一个智能助手": "You are an intelligent assistant",
    "你是一个智能助手": "You are an intelligent assistant",
    "你需要根据用户的问题": "You need to respond based on the user's question",
    "整理上下文的内容输出回复": "Organize the context and respond",
    "如果上下文不足以回复用户的问题": "If the context is not sufficient to answer the user's question",
    "请你自行补充": "Please complete it yourself",
    "另外这是在多人群聊里": "This is a multi-user group chat",
    "会有很多消息": "There will be a lot of messages",
    "每条消息都以【时间】【用户名】为前缀": "Each message is prefixed with [Time][Username]",
    "请根据【】中内容识别时间和发言者": "Please identify the time and speaker based on the content inside the brackets",
    "当前查询时间": "Current query time",
    "用户的问题": "User question",
    "注意": "Note",
    "除非用户在问题中有明确要求": "Unless the user explicitly requests otherwise",
    "否则query及prompt只作为参考内容内容": "The query and prompt are only for reference",
    "若因上下文不完整,相关性不足,或有矛盾而影响解答": "If the answer is affected due to incomplete context, insufficient relevance, or contradictions",
    "你可自行解答,或补充后解答": "You may answer or supplement the answer yourself",
    "遇到指代不明时可合理推断": "In case of ambiguous references, make reasonable assumptions",
    "由于是多人参与的Chat Agent": "As this is a conversation involving multiple people",
    "由于是多人参与的对话": "As this is a conversation involving multiple people",
    "所以用户问题如果与之前的对话内容无关": "so if the user's question is unrelated to previous conversations",
    "请忽略之前的对话内容": "Please ignore the previous context",
    "只回答结论": "Only answer the conclusion",
    "回答完整内容": "Answer the full content",
    "如果你接收到的问题存在影响回答质量的干扰因素": "If there are interfering factors that may affect answer quality",
    "请提示用户": "Remind the user",
    "情况严重时可不必强行输出答案": "If the issue is serious, you may skip outputting an answer",

    "上下文context结构": "Context structure",
    "我是一个基于本地RAG的多人参与的对话Agent": "I am a multi-user conversation agent based on local RAG",
    "因此,我发送给你的内容中会包括`上下文context结构`": "so the content I send to you will include a `context structure`",
    "请你根据`用户的问题`,整理`上下文context结构`的内容输出回复": "Please organize the content of the `context structure` and respond based on the `user's question`",
    "如果`上下文context结构`不足以回复`用户的问题`,请你自行补充": "If the `context structure` is insufficient to answer the `user's question`, please complete it yourself",

    "你是一个多function的Agent系统的路由中枢": "You are the routing center of a multi-function Agent system",
    "你需要根据如下要求解答用户问题": "You need to answer user questions according to the following requirements",
    "对于常识性或公共信息的问题,你可直接回答,无需调用其他function": "For common sense or public information questions, you can answer directly without calling other functions",
    "对于典型的用户私域问题,调用本地信息混合检索函数function A": "For typical user-private questions, call the local hybrid retrieval function A",
    "对于时效性问题(例如新闻,天气等),调用联网查询function B": "For time-sensitive issues (e.g., news, weather), call the online search function B",
    "如果你无法判断应该调用function A或B,可以同时调用这两个function": "If you cannot determine whether to call function A or B, you may call both",
    "你作为路由中枢,不能自造function,也不能自行修改function A和B函数名": "As the routing center, you must not create new functions or change the names of function A and B",
    "你调用function时必须严格遵循格式:{'name':'','arguments':''}输出": "You must strictly follow the format {'name':'','arguments':''} when calling functions",

    "此消息来自一个多人(含AI)多群组,具备RAG能力的 Chat Agent": "This message comes from a multi-user (including AI) multi-group Chat Agent with RAG capability",
    "role为tool的内容为function calls返回的内容,包括": "Content with role 'tool' represents the return of function calls",
    "是群组对话历史查询记录": "is the group chat history record",
    "是来自互联网查询的结果": "is the result from internet search",
    "知识图谱搜索结果": "knowledge graph search result",
    "以上function calls的返回内容,不分优先级": "The return contents of the above function calls have no priority",
    "function calls的返回内容,如果可以完整且准确的回答用户问题,你可以直接基于这些内容,组织语言回答": "If the function call results can completely and accurately answer the user's question, you can respond directly based on them",
    "function calls的返回内容": "The return contents of the above function calls",
    "如果无效,或为空,或不充分,或有问题": "If invalid, empty, insufficient, or problematic",
    "你需要根据自己的知识进行完善并回答": "You need to complete the answer based on your own knowledge",
    "所以,user会指代多人,而真实用户名称,以user的content里面【】中的用户名为准": "so user may refer to multiple people, and the real user name is determined by the name in the brackets in the user's content",
    "且不同用户之间的问题可能不相关": "Questions from different users may be unrelated",
    "回答时间相关的问题时,请你根据用户消息里所携带的绝对时间,进行换算。": "When answering time-related questions, please perform conversions based on the absolute time provided in the user's message.",

    "你是一个文档分析专家": "You are a document analysis expert",
    "请根据文档内容,提取出文档的结构化内容,并输出结构化内容": "Please extract and output structured content based on the document",
    "结构化内容要求": "Structured content requirements",
    "你需要仔细阅读并理解文档内容,按照文档知识结构提取知识要点": "You need to carefully read and understand the document, and extract key knowledge points based on the document's structure",
    "每个要点的标题,必须是文章原始内容": "Each point's title must come from the original content",

    "你需要按照如下要求，对文档内容进行重构和输出": "You need to restructure and output the document content as follows",
    "输出要求": "Output requirements",
    "重构要求": "Restructuring requirements",
    "#一级要点标题": "# Tier-1 Key Points",
    "# 数量不限": "# Unlimited quantity",
    "# 知识要点": "# Key Knowledge Points",
    "# 覆盖知识要点所有内容的问题集": "# Question Set Covering All Key Knowledge Points",
    "# 针对知识要点的问题": "# Questions Targeting the Knowledge Points",
    "# 针对该问题的标准答案": "# Standard Answers to the Questions",
    "你需要首先通读并理解文档": "You need to read and understand the document first",
    "提取文档每个主要章节的标题": "Extract the title of each main section",
    "再以每个章节里的最小段落为基准,从每个段落里提取全部原子化的知识要点": "Then, using the smallest paragraph within each chapter as the baseline, extract all atomic knowledge points from each paragraph.",
    "要求每个最小化段落至少一个知识要点,且每个知识要点至少提出一个问题,不能有遗漏！都需要输出": "Each minimized paragraph must yield at least one knowledge point, and each knowledge point must generate at least one question. No omissions allowed! All outputs are mandatory.",
    "针对每个问题的标准答案，不能引入任何此文档外的知识或数据，仅能来自于此文档": "Standard answers to each question must not introduce any knowledge or data outside this document",
    "需确保以FAQ形式解析,输出文档的全部内容,严禁自行删减或忽略任何内容": "You must parse and output all content in the form of FAQs",
    "对于表格内容，需完整输出，以便于后期检索": "Table content must be fully output for future retrieval",
    "如果文档内容中,有代码行、或Linux命令、或表格,请无遗漏的提出相应问题,并在答案中包含这些内容,即使可能有相似或重复。": "If there are code lines, Linux commands, or tables, raise corresponding questions and include them in the answers without omission"
}

enlang_dict = {
    "{base_prompt if RAG else ''}\n上下文context结构:{about}\n{f'当前查询时间:{datetime.now()}' if not RAG else ''}\n{f'`用户的问题`:{question}' if not RAG else ''}\n{'只回答结论' if not integrate else '回答完整内容'}\n{'如果你接收到的问题存在影响回答质量的干扰因素,请提示用户。情况严重时可不必强行输出答案。' if quality_check else ''}": "{base_prompt if RAG else ''}\nContext structure:{about}\n{f'Current query time:{datetime.now()}' if not RAG else ''}\n{f'`User question`:{question}' if not RAG else ''}\n{'Only answer the conclusion' if not integrate else 'Answer complete content'}\n{'If there are interfering factors in the received question that affect answer quality, please alert the user. In severe cases, you may choose not to force an answer.' if quality_check else ''}",
    "发送者:{item.get('sender_username','')},发送时间:{item['created_at'].strftime('%Y-%m-%d %H:%M:%S')},发送内容:{item['content']}": "Sender:{item.get('sender_username','')}, Send time:{item['created_at'].strftime('%Y-%m-%d %H:%M:%S')}, Content:{item['content']}",
    "用户({current_user.id})分享{len(message_data.message_ids)}条群({message_data.group_id})聊天记录": "User({current_user.id}) shared {len(message_data.message_ids)} group({message_data.group_id}) chat records",
    "{current_user.id} 绑定群{add_data.group_id}知识库{add_data.database_ids}出错: {str(e)}": "{current_user.id} error binding group{add_data.group_id} knowledge base{add_data.database_ids}: {str(e)}",
    "{current_user.id} 解绑群{add_data.group_id}知识库{add_data.database_ids}出错: {str(e)}": "{current_user.id} error unbinding group{add_data.group_id} knowledge base{add_data.database_ids}: {str(e)}",
    "群用户{current_user.id}上传多媒体:前端传入group_id:{group_id},duration:{duration},at:{at}": "Group user{current_user.id} uploaded media: frontend passed group_id:{group_id},duration:{duration},at:{at}",
    "用户{current_user.id}创建知识库{create_data.name}与群组{create_data.group_id}绑定": "User{current_user.id} created knowledge base{create_data.name} bound to group{create_data.group_id}",
    "发现{user.username}({user.id})违规向群{group_data['name']}({group_id})发送消息": "Found {user.username}({user.id}) violating rules by sending messages to group{group_data['name']}({group_id})",
    "{current_user.id} 对消息 {evaluate_data.message_id} 进行评估出错: {str(e)}": "{current_user.id} error evaluating message {evaluate_data.message_id}: {str(e)}",
    "{file.filename}文件大小超过限制，请上传小于{int(max_file_size/1024/1024)}MB的文件": "{file.filename} file size exceeds limit, please upload files smaller than {int(max_file_size/1024/1024)}MB",
    "正在使用{settings.LOCAL_MODEL_NAME}提取文本关系三元组，共{total_chunks}个文本块...": "Using {settings.LOCAL_MODEL_NAME} to extract text relation triples, total {total_chunks} text chunks...",
    "知识库{add_data.database_id}绑定群组{add_data.group_ids}失败: {str(e)}": "Failed to bind knowledge base{add_data.database_id} to groups{add_data.group_ids}: {str(e)}",
    "用户{current_user.id}把群文件{info.file_id}上传到知识库{info.database_id}": "User{current_user.id} uploaded group file{info.file_id} to knowledge base{info.database_id}",
    "{settings.LOCAL_MODEL_NAME}正在处理第{num+1}/{total_chunks}个文本块...": "{settings.LOCAL_MODEL_NAME} is processing text chunk {num+1}/{total_chunks}...",
    "知识库{add_data.database_id}解绑群组{add_data.group_ids}失败: {str(e)}": "Failed to unbind knowledge base{add_data.database_id} from groups{add_data.group_ids}: {str(e)}",
    "API返回非成功状态码: {results.get('code')}, 消息: {results.get('msg')}": "API returned non-success status code: {results.get('code')}, message: {results.get('msg')}",
    "{update_data.group_id} 记录已读消息成员{current_user.id}出错: {str(e)}": "{update_data.group_id} error recording read messages for member{current_user.id}: {str(e)}",
    "{current_user.id} 修改群{group_uuid}信息{group_data}出错: {str(e)}": "{current_user.id} error modifying group{group_uuid} info{group_data}: {str(e)}",
    "{cite_id}第{tool_call_retry+1}轮调用function {tool_call_chunks}": "{cite_id} round {tool_call_retry+1} calling function {tool_call_chunks}",
    "用户({current_user.id})更新知识库({database_id})信息{database_data}": "User({current_user.id}) updated knowledge base({database_id}) info{database_data}",
    "处理工作表 '{sheet_name}' 第 {row_index+2} 行时发生错误：{str(row_e)}": "Error processing worksheet '{sheet_name}' row {row_index+2}: {str(row_e)}",
    "{cite_id}第{tool_call_retry+1}轮调用结果 {tool_call_responses}": "{cite_id} round {tool_call_retry+1} call results {tool_call_responses}",
    "你是一个智能助手,你需要根据用户的问题,整理上下文的内容输出回复,如果上下文不足以回复用户的问题,请你自行补充。": "You are an intelligent assistant. You need to organize context content to output replies based on user questions. If the context is insufficient to answer the user's question, please supplement it yourself.",
    "从 {collection_name} 中删除 doc_uuid 为 {doc_uuid} 的项时出错: {e}": "Error deleting item with doc_uuid {doc_uuid} from {collection_name}: {e}",
    "当前基准时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n": "Current reference time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n",
    "{current_user.id} 通过uuid获取对话记录{search_data}出错: {str(e)}": "{current_user.id} error getting conversation records{search_data} by uuid: {str(e)}",
    "发现用户 {current_user.id} 越权绑定知识库 {database['name']}到非自有群": "Found user {current_user.id} unauthorized binding of knowledge base {database['name']} to non-owned group",
    "{current_user.id} 非群主(成员)尝试将用户移出群组{post_data.group_id}": "{current_user.id} non-owner(member) attempted to remove user from group{post_data.group_id}",
    "{group_data['id']}中{collection_data['v_name']}检索失败:{e}": "Retrieval failed for {collection_data['v_name']} in {group_data['id']}:{e}",
    "用户{current_user.id}在知识库{database_id}上传文档接口出错:{str(e)}": "User{current_user.id} error in document upload interface for knowledge base{database_id}:{str(e)}",
    "发现用户{current_user.id}非法访问数据库{search_data.database_id}": "Found user{current_user.id} illegally accessing database{search_data.database_id}",
    "工作簿：{os.path.basename(file_path)} | 工作表: {sheet_name}": "Workbook: {os.path.basename(file_path)} | Worksheet: {sheet_name}",
    "发现用户 {current_user.id} 越权解绑非自有群{database['name']}知识库": "Found user {current_user.id} unauthorized unbinding of non-owned group{database['name']} knowledge base",
    "{current_user.id} 通过uuid获取群组{group_uuid}出错: {str(e)}": "{current_user.id} error getting group{group_uuid} by uuid: {str(e)}",
    "文件下载失败 [路径:{cloud_file_path}: {str(download_error)}": "File download failed [path:{cloud_file_path}: {str(download_error)}",
    "发现{user.username}({user.id})违规向群不存在的群{group_id}发送消息": "Found {user.username}({user.id}) violating rules by sending messages to non-existent group{group_id}",
    "（系统提示：第{','.join(skipped_images)}张图片因超过10MB限制已跳过）\n": "(System notice: images {','.join(skipped_images)} skipped due to exceeding 10MB limit)\n",
    "集合: {collection.name} 向量数量: {len(all_vector_dict)}": "Collection: {collection.name} Vector count: {len(all_vector_dict)}",
    "统计过去3天(2025-05-30 21:41:12,2025-06-02 21:41:12)的数据": "Statistics for past 3 days (2025-05-30 21:41:12,2025-06-02 21:41:12)",
    "{doc_uuid}文档内容已成功写入 Milvus 向量数据库{collection_name}！": "{doc_uuid} document content successfully written to Milvus vector database{collection_name}!",
    "群用户{current_user.id}批量上传文件:前端传入group_id:{group_id}": "Group user{current_user.id} batch uploaded files: frontend passed group_id:{group_id}",
    "(然后|因此|所以|因为|此外|因而|由此|而且|并且|但是|然而|换句话说|也就是说|也即是说)$": "(then|therefore|so|because|moreover|thus|hence|and|but|however|in other words|that is to say|namely)$",
    "{current_user.id} 非群主尝试将用户移出群组{post_data.group_id}": "{current_user.id} non-owner attempted to remove user from group{post_data.group_id}",
    "{current_user.id} 将用户移除出群组{post_data}出错: {str(e)}": "{current_user.id} error removing user from group{post_data}: {str(e)}",
    "{current_user.id}转存对话记录{message_data}出错: {str(e)}": "{current_user.id} error saving conversation records{message_data}: {str(e)}",
    "发现{current_user.id} 越权对{collection.group_id}群添加收藏": "Found {current_user.id} unauthorized adding favorites to group{collection.group_id}",
    "{current_user.id} 添加用户到群组{post_data}出错: {str(e)}": "{current_user.id} error adding user to group{post_data}: {str(e)}",
    "发现非群主{current_user.id}越权修改群{add_data.group_id}信息": "Found non-owner{current_user.id} unauthorized modifying group{add_data.group_id} info",
    "{current_user.id} 一键已读群{group_id}消息出错: {str(e)}": "{current_user.id} error marking all group{group_id} messages as read: {str(e)}",
    "{current_user.id} 暂停消息输出{stop_data}出错: {str(e)}": "{current_user.id} error pausing message output{stop_data}: {str(e)}",
    "{cite_id}第{tool_call_retry+1}轮调用function失败: {e}": "{cite_id} round {tool_call_retry+1} function call failed: {e}",
    "发现用户 {current_user.id} 越权使用知识库 {database['id']}": "Found user {current_user.id} unauthorized using knowledge base {database['id']}",
    "发现用户 {current_user.id} 越权修改机器人 {database['id']}": "Found user {current_user.id} unauthorized modifying bot {database['id']}",
    "{current_user.id} 解散群组{group_uuid}出错: {str(e)}": "{current_user.id} error disbanding group{group_uuid}: {str(e)}",
    "工作表 '{sheet_name}' 使用优化表头: {suggested_headers}": "Worksheet '{sheet_name}' using optimized headers: {suggested_headers}",
    "工作表 '{sheet_name}' 第 {row_index+2} 行发现空头实体，已跳过": "Worksheet '{sheet_name}' row {row_index+2} found empty header entity, skipped",
    "{group_id}中{collection_data['v_name']}检索失败:{e}": "Retrieval failed for {collection_data['v_name']} in {group_id}:{e}",
    "{current_user.id} 创建群组{group_data}出错: {str(e)}": "{current_user.id} error creating group{group_data}: {str(e)}",
    "{current_user.id}上传头像{doc_uuid}接口出错: {str(e)}": "{current_user.id} error in avatar upload{doc_uuid} interface: {str(e)}",
    "{current_user.id} 获取群组列表{inquiry}出错: {str(e)}": "{current_user.id} error getting group list{inquiry}: {str(e)}",
    "FFmpeg转换失败，错误信息：{stderr_data.decode('utf-8')}": "FFmpeg conversion failed, error: {stderr_data.decode('utf-8')}",
    "向量索引不存在，请先创建索引: {e}, {traceback.format_exc()}": "Vector index doesn't exist, please create index first: {e}, {traceback.format_exc()}",
    "发现用户{current_user.id}非法上传文档到数据库{database_id}": "Found user{current_user.id} illegally uploading documents to database{database_id}",
    "{current_user.id} 退出群组{group_id}出错: {str(e)}": "{current_user.id} error leaving group{group_id}: {str(e)}",
    "{settings.DEFAULT_GROUP_ASSISTANT_NAME}（推荐）": "{settings.DEFAULT_GROUP_ASSISTANT_NAME} (Recommended)",
    "用户{token}在知识库{database_id}上传文档接口出错:{str(e)}": "User{token} error in document upload interface for knowledge base{database_id}:{str(e)}",
    "已识别成功,请查看识别结果文档{parsed_content['filename']}": "Successfully recognized, please check recognition result document{parsed_content['filename']}",
    "{user.id} WebSocket 业务数据{data}出错: {str(e)}": "{user.id} WebSocket business data{data} error: {str(e)}",
    "你是一个专业的数据分析师，请根据提供的表格数据内容，推测并返回最合适的表头名称。": "You are a professional data analyst. Please infer and return the most appropriate header names based on the provided table data content.",
    "删除数据库中的指定实体三元组, 参数entity_name为空则删除全部实体": "Delete specified entity triples in database. If entity_name parameter is empty, delete all entities.",
    "获取所有集合完成，集合数量: {len(collection_list)}": "Retrieved all collections, count: {len(collection_list)}",
    "工作表 '{sheet_name}' 有效列不足（至少需要2列），已跳过": "Worksheet '{sheet_name}' has insufficient valid columns (minimum 2 required), skipped",
    "文档《{file_name}》内容：{''.join(content)}": "Document《{file_name}》content: {''.join(content)}",
    "请将下面的问题转换为知识图谱查询模式：\n{self.question}": "Please convert the following question to knowledge graph query mode:\n{self.question}",
    "无法建立集合 {collection} 的索引，可能集合不存在或数据为空": "Cannot create index for collection {collection}, possibly collection doesn't exist or data is empty",
    "API请求失败，状态码: {response.status_code}": "API request failed, status code: {response.status_code}",
    "请为以下表格数据推荐合适的表头名称:\n{sample_text}\n": "Please recommend appropriate header names for the following table data:\n{sample_text}\n",
    "文档内容：\n{' '.join(document_context)}": "Document content:\n{' '.join(document_context)}",
    "只返回表头列表，例如: ['姓名', '年龄', '性别']": "Only return header list, e.g.: ['Name', 'Age', 'Gender']",
    "检测到上下文或引用消息中含有未识别音频文件,识别后模型回复更精细哦！": "Detected unrecognized audio files in context or referenced messages, model replies will be more detailed after recognition!",
    "删除向量数量: {len(deleted_vector_ids)}": "Deleted vector count: {len(deleted_vector_ids)}",
    "未找到v_name为 {database_v_name} 的数据库": "Database with v_name {database_v_name} not found",
    "1. 识别文本中的所有相对时间词（如：昨天、上周、过去3个月）\n": "1. Identify all relative time words in text (e.g.: yesterday, last week, past 3 months)\n",
    "用户{user['username']}已在群聊中，不能重复添加": "User{user['username']} already in group chat, cannot add repeatedly",
    "无权绑定到{database['name']}知识库到非自有群": "No permission to bind {database['name']} knowledge base to non-owned group",
    "只需要返回表头列表，不需要任何解释。表头数量应与数据列数一致。": "Only need to return header list, no explanation needed. Header count should match data column count.",
    "{file.filename}mp3或wav文件大小超过限制": "{file.filename} mp3 or wav file size exceeds limit",
    "文档《{file_name}》内容：{large_text}": "Document《{file_name}》content: {large_text}",
    "处理结果时发现错误: {results['error']}": "Error encountered while processing results: {results['error']}",
    "无权解绑非自有群{database['name']}知识库": "No permission to unbind non-owned group{database['name']} knowledge base",
    "- 时间格式：YYYY-MM-DD HH:MM:SS\n": "- Time format: YYYY-MM-DD HH:MM:SS\n",
    "3. 在每个相对时间词后添加括号，括号内写入起止时间\n": "3. Add parentheses after each relative time word, write start-end time inside\n",
    "未定义的Prompt类型: {prompt_type}": "Undefined Prompt type: {prompt_type}",
    "使用ffmpeg将原始音频数据转换为vosk需要的格式": "Use ffmpeg to convert raw audio data to format required by vosk",
    "图片过大（base64编码后超过10MB），无法识别": "Image too large (exceeds 10MB after base64 encoding), cannot recognize",
    "请返回处理后的完整文本（只需返回文本本身，不要解释）": "Please return the complete processed text (just the text itself, no explanation)",
    "从Milvus中获取指定集合的文档并构建BM25索引": "Get specified collection documents from Milvus and build BM25 index",
    "更新知识库失败，服务端遇到未知异常：{str(e)}": "Failed to update knowledge base, server encountered unknown exception: {str(e)}",
    "加载集合: {collection_name} 完成": "Loaded collection: {collection_name} completed",
    "(截断){truncated_content}...": "(Truncated){truncated_content}...",
    "文件下载失败：{resp.errorMessage}": "File download failed: {resp.errorMessage}",
    "集合: {collection_name} 清理完成": "Collection: {collection_name} cleanup completed",
    "开始加载集合: {collection_name}": "Start loading collection: {collection_name}",
    "当前群聊未设置默认AI服务,请联系群主设置后再试。": "The current group chat has no default AI service set, please contact the group owner to set it up.",
    "你是多模态分析模型,请根据提供的文档进行分析回答。": "You are a multimodal analysis model, please analyze and answer based on the provided documents.",
    "2. 根据当前时间计算每个相对时间词的精确范围\n": "2. Calculate the exact range for each relative time term based on current time\n",
    "你是图片识别模型,请根据图片内容和问题,输出回答。": "You are an image recognition model, please output answers based on the image content and questions.",
    "HTTP错误: {response.status}": "HTTP error: {response.status}",
    "开始清理集合: {collection_name}": "Start cleaning collection: {collection_name}",
    "start_index 不能为空当 id 大于 1": "start_index cannot be empty when id is greater than 1",
    "- 只处理过去的时间（不要计算未来的时间范围）\n": "- Only process past times (do not calculate future time ranges)\n",
    "- 模糊时间（如'早上'）按该时间段起始时刻计算": "- Fuzzy times (like 'morning') are calculated based on the start time of that period",
    "当前时间为：{int(time.time())}": "Current time is: {int(time.time())}",
    "创建新的数据库，如果已存在则返回已有数据库的名称": "Create a new database, if it exists return the name of the existing database",
    "你是一个专业的时间处理助手，请执行以下操作：\n": "You are a professional time processing assistant, please perform the following operations:\n",
    "降噪后的音频已保存为 {output_file}": "Denoised audio has been saved as {output_file}",
    "请从以下文本提取关系三元组：\n{text}\n": "Please extract relation triples from the following text:\n{text}\n",
    "不支持的文件类型：{file.filename}": "Unsupported file type: {file.filename}",
    "{group_data.name}群文档知识库": "{group_data.name} group document knowledge base",
    "检查当前数据库是否为空（是否存在任何实体节点）": "Check if current database is empty (whether any entity nodes exist)",
    "{create_time}音频识别结果.txt": "{create_time} audio recognition result.txt",
    "我理解你的问题是:{question}。\n": "I understand your question is: {question}.\n",
    "用户通过手机验证码登录(如未注册则自动注册)": "User logs in via mobile verification code (automatically registers if not registered)",
    "(\S+)(吗|呢|吧|啊|么|哪|嘛)$": "(\S+)(吗|呢|吧|啊|么|哪|嘛)$",
    "{group['name']}群收藏知识库": "{group['name']} group collection knowledge base",
    "未能识别任何内容，请提示用户检查音频文件。": "Failed to recognize any content, please prompt user to check audio file.",
    "{group['name']}群文档知识库": "{group['name']} group document knowledge base",
    "根据doc_uuid删除所有相关节点和关系": "Delete all related nodes and relationships based on doc_uuid",
    "{group['name']}群对话记录库": "{group['name']} group conversation record database",
    "获取识别内容时发生错误: {str(e)}": "Error occurred while getting recognition content: {str(e)}",
    "未找到模型配置: {model_name}": "Model configuration not found: {model_name}",
    "第%s次查询，处理未完成，任务状态码:%s": "The %sth query, processing not completed, task status code:%s",
    "截断tool角色的内容，优先保留最近的消息": "Truncate tool role content, prioritize keeping recent messages",
    "当你想知道通过搜索引擎查询信息时非常有用。": "Very useful when you want to query information through search engines.",
    "上下文过长，模型暂无法处理，正在优化中。": "Context too long, model currently unable to process, optimizing now.",
    "上下文过长,模型暂无法处理,正在优化中。": "Context too long, model currently unable to process, optimizing now.",
    "文本清洗：去掉特殊字符、空白行、冗余符号": "Text cleaning: remove special characters, blank lines, redundant symbols",
    "bot/DeepSeek-V3/...等": "bot/DeepSeek-V3/...etc",
    "识别失败,服务器未能获取录音文件,请重试": "Recognition failed, server failed to obtain recording file, please retry",
    "公有云账号余额不足,请联系管理员充值。": "Public cloud account balance insufficient, please contact administrator to recharge.",
    "识别过程中发生错误: {str(e)}": "Error occurred during recognition: {str(e)}",
    "公有云账号余额不足，请联系管理员充值。": "Public cloud account balance insufficient, please contact administrator to recharge.",
    "搜索过程中发生错误: {str(e)}": "Error occurred during search: {str(e)}",
    "由于上下文过长,已截断部分上下文。\n": "Due to long context, part of context has been truncated.\n",
    "获取机器人列表失败：{str(e)}": "Failed to get bot list: {str(e)}",
    "用户{user_id}不存在或已注销": "User {user_id} does not exist or has been deactivated",
    "输入内容格式异常: {str(e)}": "Input content format exception: {str(e)}",
    "创建任务接口调用异常，错误详情:%s": "Create task interface call exception, error details:%s",
    "批量上传文件失败: {str(e)}": "Batch file upload failed: {str(e)}",
    "查询任务接口调用异常，错误详情:%s": "Query task interface call exception, error details:%s",
    "识别失败,文件路径信息不完整,请重试": "Recognition failed, file path information incomplete, please retry",
    "开始搜索与{pk}最相似的10个向量": "Start searching for 10 most similar vectors to {pk}",
    "服务端遇到未知异常：{str(e)}": "Server encountered unknown exception: {str(e)}",
    "创建任务成功，task_id: %s": "Task created successfully, task_id: %s",
    "搜索与{pk}最相似的10个向量完成": "Search for 10 most similar vectors to {pk} completed",
    "需要处理的文本：{text}\n\n": "Text to be processed: {text}\n\n",
    "- 范围格式：开始时间~结束时间\n": "- Range format: start time~end time\n",
    "(\S+)\s+([的地得])\s+": "(\S+)\s+([的地得])\s+",
    "模型别名不能与系统默认模型名称相同": "Model alias cannot be the same as system default model name",
    "未能识别任何内容，请检查音频文件。": "Failed to recognize any content, please check audio file.",
    "查询任务成功，音频下载链接: %s": "Query task successful, audio download link: %s",
    "4. 保持原文其他内容不变\n\n": "4. Keep other content of original text unchanged\n\n",
    "识别失败,音频文件不存在或已被删除": "Recognition failed, audio file does not exist or has been deleted",
    "识别失败,识别过程出现异常,请重试": "Recognition failed, exception occurred during recognition process, please retry",
    "根据群名称、消息、用户模糊搜索群组": "Fuzzy search groups based on group name, messages, users",
    "优化后的执行流程，包含新的评分系统": "Optimized execution flow, includes new scoring system",
    "查询指定实体三元组信息（无向关系）": "Query specified entity triple information (undirected relationship)",
    "处理文件时出错: {str(e)}": "Error processing file: {str(e)}",
    "关系类型索引不存在，请先创建索引": "Relationship type index does not exist, please create index first",
    "处理不同类型内容的token计算": "Token calculation for processing different types of content",
    "获取大模型回复异常，请稍后再试。": "Exception getting large model response, please try again later.",
    "节点向量索引不存在，请先创建索引": "Node vector index does not exist, please create index first",
    "文件下载失败: {str(e)}": "File download failed: {str(e)}",
    "保存模型配置失败，请检查参数格式": "Failed to save model configuration, please check parameter format",
    "查询任务失败，返回状态码: %s": "Query task failed, returned status code: %s",
    "文件解析异常: {str(e)}": "File parsing exception: {str(e)}",
    "删除机器人失败：{str(e)}": "Failed to delete bot: {str(e)}",
    "创建任务失败，返回状态码: %s": "Create task failed, returned status code: %s",
    "识别语言，支持'cn'或'en'": "Recognition language, supports 'cn' or 'en'",
    "文件转存失败: {str(e)}": "File transfer failed: {str(e)}",
    "获取机器人失败：{str(e)}": "Failed to get bot: {str(e)}",
    "{str(e)},请重新选择模型": "{str(e)}, please reselect model",
    "创建机器人失败：{str(e)}": "Failed to create bot: {str(e)}",
    "音频转换异常: {str(e)}": "Audio conversion exception: {str(e)}",
    "更新机器人失败：{str(e)}": "Failed to update bot: {str(e)}",
    "发送短信验证码失败，请稍后重试": "Failed to send SMS verification code, please retry later",
    "【语音内容】{content}": "【Voice content】{content}",
    "只有群主才能添加对话记录到收藏": "Only group owner can add conversation records to collection",
    "获取当前用户在群中未读的@消息": "Get current user's unread @ messages in group",
    "查询与指定实体最相似的实体信息": "Query entity information most similar to specified entity",
    "解析API响应时出错: {e}": "Error parsing API response: {e}",
    "使用重排模型计算实体相关性分数": "Use reranking model to calculate entity relevance scores",
    "下载文件失败：{str(e)}": "File download failed: {str(e)}",
    "下载头像失败：{str(e)}": "Avatar download failed: {str(e)}",
    "模型别名已存在，请使用其他别名": "Model alias already exists, please use another alias",
    "根据收藏记录id获取收藏内容": "Get collection content based on collection record id",
    "文档内容:{content}": "Document content:{content}",
    "删除失败: {str(e)}": "Deletion failed: {str(e)}",
    "使用vosk模型进行语音识别": "Use vosk model for speech recognition",
    "用户对 AI 回答进行评估": "User evaluates AI responses",
    "识别异常，后台管理员排查中": "Identification exception, administrator is investigating",
    "问题：{question}": "Question: {question}",
    "发送验证码失败，请稍后重试": "Failed to send verification code, please try again later",
    "查询过程中出错，请稍后重试": "Error during query, please try again later",
    "默认群聊不允许添加其他用户": "Default group chat does not allow adding other users",
    "文件下载失败：{resp}": "File download failed: {resp}",
    "通义千问-文生视频（收费）": "Tongyi Qianwen - Text-to-Video (Paid)",
    "添加JSONL内容中的实体": "Add entities from JSONL content",
    "get result参数：": "get result parameters:",
    "处理过程中出错，请稍后重试": "Error during processing, please try again later",
    "不能创建与默认群聊同名群聊": "Cannot create group chat with same name as default group",
    "用户通过邮箱账号密码登录": "User login via email and password",
    "消息已标记为已读或不存在": "Message already marked as read or does not exist",
    "混合搜索知识库及对话记录": "Hybrid search knowledge base and chat history",
    "不允许修改为默认群聊名称": "Not allowed to change to default group chat name",
    "您没有权限删除此收藏记录": "You don't have permission to delete this bookmark",
    "当上下文过长时切除上下文": "Truncate context when it's too long",
    "创建节点和关系的向量索引": "Create vector index for nodes and relationships",
    "通义千问-长文本（收费）": "Tongyi Qianwen - Long Text (Paid)",
    "您无权限删除此共享知识库": "You don't have permission to delete this shared knowledge base",
    "直接传入音频数据进行识别": "Directly pass audio data for recognition",
    "根据消息ID获取识别内容": "Get recognition content by message ID",
    "未找到对应的音频文件记录": "Corresponding audio file record not found",
    "DeepSeek（收费）": "DeepSeek (Paid)",
    "使用BM25检索相关文档": "Use BM25 to retrieve relevant documents",
    "非群主不能将用户移出群聊": "Only group owner can remove users from group",
    "通义千问-32B(免费)": "Tongyi Qianwen-32B (Free)",
    "通义千问-文生图（收费）": "Tongyi Qianwen - Text-to-Image (Paid)",
    "获取未审核的好友请求数量": "Get count of pending friend requests",
    "非群主不能添加用户到群聊": "Only group owner can add users to group",
    "通义千问-32B（免费）": "Tongyi Qianwen-32B (Free)",
    "根据关键词搜索收藏记录": "Search bookmarks by keywords",
    "只有管理员可以创建组织": "Only admin can create organization",
    "不允许修改默认群聊名称": "Not allowed to modify default group chat name",
    "请使用图片识别模型识图": "Please use image recognition model",
    "处理音频消息的公共方法": "Common method for processing audio messages",
    "您没有权限访问该数据库": "You don't have permission to access this database",
    "把群文件上传到知识库库": "Upload group files to knowledge base",
    "\\n查询任务请求参数:": "\\nQuery task request parameters:",
    "渲染分享的对话记录页面": "Render shared chat history page",
    "只有管理员可以删除组织": "Only admin can delete organization",
    "根据群id返回收藏记录": "Return bookmarks by group id",
    "通义千问-视觉（收费）": "Tongyi Qianwen - Vision (Paid)",
    "只有管理员可以删除部门": "Only admin can delete department",
    "只有管理员可以创建部门": "Only admin can create department",
    "请选择要转存的对话记录": "Please select chat history to save",
    "查询自有权知识库列表": "Query owned knowledge base list",
    "获取转存对话记录出错": "Error getting saved chat history",
    "查询某个群的对话记录": "Query chat history of a group",
    "改进的三元组提取方法": "Improved triple extraction method",
    "获取当前群组的FAQ": "Get current group's FAQ",
    "只有群主能修改群信息": "Only group owner can modify group info",
    "该用户已经是您的好友": "This user is already your friend",
    "获取用户的所有机器人": "Get all bots of a user",
    "处理单个图数据库查询": "Process single graph database query",
    "您没有权限访问该群聊": "You don't have permission to access this group",
    "未找到对应的消息记录": "Corresponding message record not found",
    "前端配置代理确认接口": "Frontend proxy configuration confirmation API",
    "在群组内搜索对话记录": "Search chat history within group",
    "记录已读消息成员出错": "Error recording members who read message",
    "无权限停止该消息输出": "No permission to stop this message output",
    "您已经发送过好友请求": "You have already sent friend request",
    "使当前token失效": "Invalidate current token",
    "将用户移除出群组出错": "Error removing user from group",
    "未找到对应的收藏记录": "Corresponding bookmark not found",
    "重置密码-发送验证码": "Reset password - send verification code",
    "根据文本生成语音文件": "Generate audio file from text",
    "每页数量不能超过50": "Items per page cannot exceed 50",
    "你没有权限解散该群组": "You don't have permission to disband this group",
    "网页上传文档到知识库": "Web upload documents to knowledge base",
    "请先移除部门下的用户": "Please remove users from department first",
    "查询被授权知识库列表": "Query authorized knowledge base list",
    "关联的数据库不存在": "Associated database does not exist",
    "图数据库查询处理器": "Graph database query processor",
    "添加用户到群组出错": "Error adding user to group",
    "邮件验证并完成注册": "Email verification and complete registration",
    "获取自定义模型列表": "Get custom model list",
    "添加对话记录到收藏": "Add chat history to bookmarks",
    "默认群聊不允许移交": "Default group chat cannot be transferred",
    "检查数据库是否存在": "Check if database exists",
    "文件已加入上传队列": "File added to upload queue",
    "该用户不是您的好友": "This user is not your friend",
    "绑定群组知识库出错": "Error binding group knowledge base",
    "用户不存在或已注销": "User does not exist or is deactivated",
    "创建任务请求参数:": "Create task request parameters:",
    "文件未关联到知识库": "File not associated with knowledge base",
    "获取转存的对话记录": "Get saved chat history",
    "统计过去3天的数据": "Statistics of past 3 days data",
    "模型不存在或已删除": "Model does not exist or is deleted",
    "不允许解散默认群聊": "Not allowed to disband default group chat",
    "群组不存在或已解散": "Group does not exist or is disbanded",
    "根据边类型返回权重": "Return weight by edge type",
    "没有查询到绑定群组": "No bound groups found in query",
    "不能将群主移出群聊": "Cannot remove group owner from group",
    "查询群聊知识库列表": "Query group chat knowledge base list",
    "查询知识库文档列表": "Query knowledge base document list",
    "生成FAQ框架失败": "Failed to generate FAQ framework",
    "用户不存在或未激活": "User does not exist or is inactive",
    "根据上下文生成回答": "Generate answer based on context",
    "upload参数：": "upload parameters:",
    "验证码错误或已过期": "Verification code error or expired",
    "关系类型索引不存在": "Relation type index does not exist",
    "递归获取引用链内容": "Recursively get reference chain content",
    "描述一下这张图片": "Describe this image",
    "知识图谱上传文档": "Knowledge graph upload document",
    "识别内容获取成功": "Recognition content obtained successfully",
    "群组名称不能为空": "Group name cannot be empty",
    "用户主动退出群聊": "User actively exited group chat",
    "检查索引是否存在": "Check if index exists",
    "群上传多媒体数据": "Group upload multimedia data",
    "绑定群组到知识库": "Bind group to knowledge base",
    "群聊成员不能为空": "Group chat members cannot be empty",
    "谁是李四的学生？": "Who are Li Si's students?",
    "转存对话记录出错": "Error saving chat history",
    "获取模型列表失败": "Failed to get model list",
    "重新识别中...": "Re-identifying...",
    "群主不能退出群组": "Group owner cannot exit the group",
    "无权修改此知识库": "No permission to modify this knowledge base",
    "停止消息输出失败": "Failed to stop message output",
    "一键已读某群消息": "Mark all group messages as read",
    "开始获取所有集合": "Start getting all collections",
    "构建匹配查询语句": "Build matching query statement",
    "未找到该好友请求": "Friend request not found",
    "精确搜索用户信息": "Precise search user information",
    "处理录音文件识别": "Process audio file recognition",
    "获取对话记录失败": "Failed to get chat history",
    "无数据库访问权限": "No database access permission",
    "记录工具使用日志": "Record tool usage log",
    "token已失效": "Token has expired",
    "收藏内容不能为空": "Favorite content cannot be empty",
    "查询某个群的信息": "Query a group's information",
    "录音文件识别完成": "Audio file recognition completed",
    "更新流式消息内容": "Update streaming message content",
    "解绑群知识库出错": "Error unbinding group knowledge base",
    "统一工具执行入口": "Unified tool execution entry",
    "绑定知识库到群组": "Bind knowledge base to group",
    "处理多个工具调用": "Handle multiple tool calls",
    "无权访问此机器人": "No permission to access this bot",
    "不支持的文件类型": "Unsupported file type",
    "语音文件生成成功": "Voice file generated successfully",
    "未查询到用户信息": "User information not found",
    "记录已读消息成员": "Record members who read messages",
    "无权删除此机器人": "No permission to delete this bot",
    "通义千问（收费）": "Tongyi Qianwen (paid)",
    "无权修改此机器人": "No permission to modify this bot",
    "模型配置保存成功": "Model configuration saved successfully",
    "获取群组列表出错": "Error getting group list",
    "设置关系类型嵌入": "Set relation type embedding",
    "生成语音文件失败": "Failed to generate voice file",
    "张三的父亲是谁？": "Who is Zhang San's father?",
    "不能申请添加自己": "Cannot send friend request to yourself",
    "获取好友请求列表": "Get friend request list",
    "无权使用此知识库": "No permission to use this knowledge base",
    "查询知识库详情": "Query knowledge base details",
    "您不在该群组中": "You are not in this group",
    "无群组访问权限": "No group access permission",
    "将用户移出群聊": "Remove user from group chat",
    "你不在该群组中": "You are not in this group",
    "解绑群组知识库": "Unbind group knowledge base",
    "音频文件不存在": "Audio file does not exist",
    "发送邮箱验证码": "Send email verification code",
    "获取单个机器人": "Get single bot",
    "知识库上传文档": "Knowledge base upload document",
    "手机号已被使用": "Phone number already in use",
    "目标用户不存在": "Target user does not exist",
    "添加实体三元组": "Add entity triple",
    "修改群信息出错": "Error modifying group information",
    "请先删除子部门": "Please delete sub-departments first",
    "删除知识库文件": "Delete knowledge base file",
    "已拒绝好友请求": "Friend request rejected",
    "向量索引不存在": "Vector index does not exist",
    "查询知识库列表": "Query knowledge base list",
    "更新知识库信息": "Update knowledge base information",
    "群批量上传文件": "Group batch upload files",
    "发送手机验证码": "Send SMS verification code",
    "好友请求已发送": "Friend request sent",
    "更新机器人信息": "Update bot information",
    "(呀|啦|哇)": "(ya|la|wa)",
    "更新自定义模型": "Update custom model",
    "解绑知识库群组": "Unbind knowledge base group",
    "语音文件已存在": "Voice file already exists",
    "添加一个三元组": "Add a triple",
    "原始文件不存在": "Original file does not exist",
    "获取数据库列表": "Get database list",
    "关闭数据库连接": "Close database connection",
    "撤回了一条消息": "Recalled a message",
    "删除自定义模型": "Delete custom model",
    "分享记录不存在": "Share record does not exist",
    "生成FAQ失败": "Failed to generate FAQ",
    "已接受好友请求": "Friend request accepted",
    "组织代码已存在": "Organization code already exists",
    "用户名已被使用": "Username already in use",
    "添加自定义模型": "Add custom model",
    "对方已删除聊天": "The other party deleted the chat",
    "邮箱或密码错误": "Email or password incorrect",
    "无效的认证凭据": "Invalid authentication credentials",
    "绑定知识库失败": "Failed to bind knowledge base",
    "搜索知识库文档": "Search knowledge base documents",
    "搜索知识库对话": "Search knowledge base conversations",
    "无效的操作类型": "Invalid operation type",
    "添加用户到群聊": "Add user to group chat",
    "停止消息输出": "Stop message output",
    "父部门不存在": "Parent department does not exist",
    "群对话知识库": "Group conversation knowledge base",
    "删除模型失败": "Failed to delete model",
    "搜索群组失败": "Group search failed",
    "网络搜索工具": "Web search tool",
    "关系向量查询": "Relationship vector query",
    "以下是新话题": "The following are new topics",
    "手机号已注册": "Phone number already registered",
    "获取模型列表": "Get model list",
    "群收藏知识库": "Group favorites knowledge base",
    "检查检索效果": "Check retrieval effectiveness",
    "文件删除成功": "File deleted successfully",
    "模型删除成功": "Model deleted successfully",
    "评估记录出错": "Evaluation record error",
    "解散群组成功": "Group disbanded successfully",
    "修改用户信息": "Modify user information",
    "退出群组出错": "Error leaving group",
    "查询好友列表": "Query friend list",
    "该聊天已删除": "This chat has been deleted",
    "不能删除自己": "Cannot delete yourself",
    "识别中...": "Recognizing...",
    "发送好友请求": "Send friend request",
    "获取群组出错": "Error getting group",
    "验证码已发送": "Verification code sent",
    "语音识别完成": "Voice recognition completed",
    "完成流式消息": "Complete streaming message",
    "解散群组出错": "Error disbanding group",
    "数据库不存在": "Database does not exist",
    "好友申请审核": "Friend request review",
    "退出群组成功": "Successfully left group",
    "搜索用户信息": "Search user information",
    "获取部门列表": "Get department list",
    "群组上传文档": "Group upload document",
    "批量上传成功": "Batch upload successful",
    "评估记录成功": "Evaluation record successful",
    "知识库不存在": "Knowledge base does not exist",
    "智能问答助手": "Intelligent Q&A assistant",
    "机器人不存在": "Robot does not exist",
    "转存对话记录": "Dump conversation records",
    "无权修改信息": "No permission to modify information",
    "一键已读出错": "Mark all as read error",
    "用户重置密码": "User reset password",
    "群文档知识库": "Group document knowledge base",
    "密码重置成功": "Password reset successful",
    "好友添加成功": "Friend added successfully",
    "获取组织列表": "Get organization list",
    "处理句子识别": "Process sentence recognition",
    "执行模式查询": "Execute mode query",
    "创建群组出错": "Error creating group",
    "创建知识库": "Create knowledge base",
    "规则：\n": "Rules:\n",
    "查询部分：": "Query part:",
    "创建机器人": "Create robot",
    "群组不存在": "Group does not exist",
    "所属文件名": "Belonging file name",
    "组织不存在": "Organization does not exist",
    "助手机器人": "Assistant robot",
    "搜索的信息": "Searched information",
    "消息已撤回": "Message has been recalled",
    "邮箱已注册": "Email already registered",
    "无操作权限": "No operation permission",
    "删除数据库": "Delete database",
    "测试数据库": "Test database",
    "部门已删除": "Department deleted",
    "文件不存在": "File does not exist",
    "非文字收藏": "Non-text favorite",
    "删除机器人": "Delete robot",
    "消息不存在": "Message does not exist",
    "模型不存在": "Model does not exist",
    "保存成功！": "Save successful!",
    "验证码错误": "Verification code error",
    "修改群信息": "Modify group information",
    "已停止输出": "Output stopped",
    "用户不存在": "User does not exist",
    "上传部分：": "Upload part:",
    "删除知识库": "Delete knowledge base",
    "群组已解散": "Group has been disbanded",
    "账号未激活": "Account not activated",
    "部门不存在": "Department does not exist",
    "技术讨论组": "Technical discussion group",
    "已识别成功": "Successfully recognized",
    "查询群列表": "Query group list",
    "后台上传中": "Background uploading",
    "组织已删除": "Organization deleted",
    "添加成功": "Added successfully",
    "注册成功": "Registration successful",
    "非法用户": "Illegal user",
    "创建群聊": "Create group chat",
    "解散群聊": "Disband group chat",
    "上传头像": "Upload avatar",
    "注销成功": "Logout successful",
    "记录成功": "Record successful",
    "删除成功": "Deleted successfully",
    "每页数量": "Items per page",
    "解密失败": "Decryption failed",
    "下载头像": "Download avatar",
    "创建成功": "Created successfully",
    "未知工具": "Unknown tool",
    "修改成功": "Modified successfully",
    "上传成功": "Upload successful",
    "通讯成功": "Communication successful",
    "权限不足": "Insufficient permissions",
    "我与AI": "My Ai",
    "收藏成功": "Favorited successfully",
    "消息内容": "Message content",
    "解绑失败": "Unbind failed",
    "下载文档": "Download document",
    "示例公司": "Example company",
    "创建失败": "Creation failed",
    "搜索内容": "Search content",
    "更新成功": "Updated successfully",
    "结束时间": "End time",
    "工作表名": "Worksheet name",
    "删除好友": "Delete friend",
    "注销用户": "Delete user",
    "(哦)$": "(Oh)$",
    "绑定成功": "Bound successfully",
    "移除成功": "Removed successfully",
    "解绑成功": "Unbound successfully",
    "开始时间": "Start time",
    "已删除": "Deleted",
    "验证码": "Verification code",
    "手机号": "Phone number",
    "发送者": "Sender",
    "文件名": "File name",
    "技术部": "Technical department",
    "李四": "Li Si",
    "相关": "Related",
    "引用": "Quote",
    "拥有": "Own",
    "创建": "Create",
    "张三": "Zhang San",
    "你好": "Hello",
    "父亲": "Father",
    "列数": "Number of columns",
    "参与": "Participate",
    "行数": "Number of rows",
    "学生": "Student",
    "页码": "Page number"
}

def replace_dict_keys_in_files(directory: str):
    """
    遍历目录下的所有.py文件，将字典的键替换为对应的值
    
    :param directory: 要遍历的目录路径
    :param key_value_map: 键值映射字典，键是要查找的字符串，值是替换后的字符串
    """
    for root, _, files in os.walk(directory):
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r+', encoding='utf-8') as f:
                        content = f.read()
                        new_content = content
                        
                        # 执行所有键的替换
                        if "llm.py" in file_path:
                            for key, value in enlang_dict_llm_py.items():
                                new_content = new_content.replace(key, value)
                            new_content = new_content.replace("BoChaSearch", "TavilySearch")
                        elif "web_search.py" in file_path:
                            new_content = new_content.replace("# from tavily import TavilyClient", "from tavily import TavilyClient")
                        elif "tools.py" in file_path:
                            new_content = new_content.replace("BoChaSearch", "TavilySearch")
                        for key, value in enlang_dict.items():
                            # 确保键是被引号包围的（作为字典键）
                            for quote in ["'", '"']:
                                old_str = f"{quote}{key}{quote}"
                                new_str = f"{quote}{value}{quote}"
                                new_content = new_content.replace(old_str, new_str)
                        
                        # 如果内容有变化，则写回文件
                        if new_content != content:
                            f.seek(0)
                            f.write(new_content)
                            f.truncate()
                            print(f"已更新文件: {file_path}")
                        else:
                            print(f"无变化文件: {file_path}")
                except Exception as e:
                    print(f"处理文件 {file_path} 时出错: {str(e)}")

# 示例用法
if __name__ == "__main__":
    # 指定要处理的目录
    target_directory = sys.argv[1]
    
    # 执行替换
    replace_dict_keys_in_files(target_directory)
