<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>聊天记录</title>
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/highlight.js@11.8.0/styles/github.min.css">
    <script src="https://cdn.jsdelivr.net/npm/highlight.js@11.8.0/lib/highlight.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/contrib/auto-render.min.js"></script>
    <style>
        .message-container {
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background: #f5f5f5;
            border-radius: 8px;
        }
        .message {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .message-header {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
        }
        .avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 10px;
        }
        .username {
            font-weight: bold;
            color: #333;
        }
        .time {
            color: #999;
            font-size: 12px;
            margin-left: 10px;
        }
        .content {
            margin-left: 50px;
            word-break: break-all;
        }
        /* Markdown 样式 */
        .markdown-content {
            line-height: 1.6;
            word-wrap: break-word;
        }
        .markdown-content p {
            margin: 8px 0;
        }
        .markdown-content code {
            background-color: #f6f8fa;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: monospace;
        }
        .markdown-content pre {
            background-color: #f6f8fa;
            padding: 16px;
            border-radius: 6px;
            overflow: auto;
        }
        .markdown-content pre code {
            background-color: transparent;
            padding: 0;
        }
        .markdown-content blockquote {
            margin: 8px 0;
            padding-left: 1em;
            border-left: 4px solid #ddd;
            color: #666;
        }
        .markdown-content img {
            max-width: 100%;
            height: auto;
        }
        .markdown-content table {
            border-collapse: collapse;
            width: 100%;
            margin: 8px 0;
        }
        .markdown-content th, .markdown-content td {
            border: 1px solid #ddd;
            padding: 8px;
        }
        .markdown-content th {
            background-color: #f6f8fa;
        }
        /* 引用消息样式 */
        .cite-message {
            margin: 5px 0 10px 0;
            padding: 8px 12px;
            background: #f0f2f5;
            border-radius: 4px;
            border-left: 3px solid #1890ff;
        }
        
        .cite-header {
            font-size: 12px;
            color: #666;
            margin-bottom: 4px;
        }
        
        .cite-username {
            font-weight: bold;
            color: #333;
        }
        
        .cite-time {
            margin-left: 8px;
            color: #999;
        }
        
        .cite-content {
            font-size: 13px;
            color: #666;
        }
        
        .cite-content img,
        .cite-content video {
            max-width: 200px !important;
        }
        
        .cite-content .markdown-content {
            font-size: 13px;
        }
        
        .cite-content audio {
            max-width: 200px;
        }
    </style>
</head>
<body>
    <div class="message-container">
        <div class="forward-info" style="margin-bottom: 20px; padding: 10px; border-bottom: 1px solid #eee;">
            <div style="display: flex; align-items: center;">
                {% if forward_user_avatar %}
                <img class="avatar" src="/api/files/head/{{ forward_user_id }}" alt="avatar" style="width: 40px; height: 40px; border-radius: 50%; margin-right: 10px;">
                {% else %}
                <img class="avatar" src="/static/default_avatar.png" alt="default avatar" style="width: 40px; height: 40px; border-radius: 50%; margin-right: 10px;">
                {% endif %}
                <!-- /* <img class="avatar" src="/api/files/head/{{ forward_user_id }}" alt="avatar" style="width: 40px; height: 40px; border-radius: 50%; margin-right: 10px;"> */ -->
                <div>
                    <div style="font-size: 14px; color: #666;">
                        <span style="font-weight: bold;">{{ forward_user }}</span> 分享的聊天记录
                    </div>
                    <div style="font-size: 12px; color: #999; margin-top: 5px;">
                        分享时间：{{ forward_time }}
                    </div>
                </div>
            </div>
        </div>
        <h2>聊天记录</h2>
        {% for message in messages %}
        <div class="message">
            <div class="message-header">
                {% if message.sender_avatar %}
                <img class="avatar" src="/api/files/head/{{ message.sender_id }}" alt="avatar">
                {% else %}
                <img class="avatar" src="/static/default_avatar.png" alt="default avatar">
                {% endif %}
                <span class="username">{{ message.sender_username }}</span>
                <span class="time">{{ message.created_at }}</span>
            </div>
            <div class="content">
                {% if message.cite %}
                    <div class="cite-message">
                        <div class="cite-header">
                            {% if message.cite.sender_avatar %}
                            <img class="avatar" src="/api/files/head/{{ message.cite.sender_id }}" alt="avatar" style="width: 40px; height: 40px; border-radius: 50%; margin-right: 10px;">
                            {% else %}
                            <img class="avatar" src="/static/default_avatar.png" alt="default avatar" style="width: 40px; height: 40px; border-radius: 50%; margin-right: 10px;">
                            {% endif %}
                            <span class="cite-username">{{ message.cite.sender_username }}</span>
                            <span class="cite-time">{{ message.cite.created_at }}</span>
                        </div>
                        <div class="cite-content">
                            {% if message.cite.type == 'text' %}
                                <div class="markdown-content">{{ message.cite.content }}</div>
                            {% elif cite_message.type.startswith('image') %}
                                <img src="/api/files/download/{{ message.cite.content.id }}" style="max-width: 200px;">
                            {% elif message.cite.type.startswith('audio') %}
                                <audio controls src="/api/files/download/{{ message.cite.content }}"></audio>
                            {% elif message.cite.type.startswith('video') %}
                                <video controls style="max-width: 200px;" src="{{ message.cite.content }}"></video>
                            {% else %}
                                <a href="/api/files/download/{{ message.content.id }}" download class="document-link" title="{{ message.content.filename }}">
                                {{ message.content.filename }}
                                </a>
                            {% endif %}
                        </div>
                    </div>
                {% endif %}
                
                {% if message.type == 'text' %}
                    <div class="markdown-content" id="message-{{ message.id }}">{{ message.content }}</div>
                {% elif message.type.startswith('image') %}
                    <img src="/api/files/download/{{ message.content.id }}" style="max-width: 300px;">
                {% elif message.type.startswith('audio') %}
                    <audio controls src="/api/files/download/{{ message.content }}"></audio>
                {% elif message.type.startswith('video') %}
                    <video controls style="max-width: 300px;" src="{{ message.content }}"></video>
                {% else %}
                    <a href="/api/files/download/{{ message.content.id }}" download class="document-link" title="{{ message.content.filename }}">
                    {{ message.content.filename }}
                    </a>

                {% endif %}
            </div>
        </div>
        {% endfor %}
    </div>

    <script>
        marked.setOptions({
            breaks: true,
            gfm: true,
            highlight: function(code, lang) {
                if (lang && hljs.getLanguage(lang)) {
                    try {
                        return hljs.highlight(code, { language: lang }).value;
                    } catch (__) {}
                }
                return code;
            }
        });

        document.addEventListener('DOMContentLoaded', function() {
            document.querySelectorAll('.markdown-content').forEach(function(element) {
                const originalText = element.textContent;
                const htmlContent = marked.parse(originalText);
                element.innerHTML = htmlContent;
                
                element.querySelectorAll('pre code').forEach((block) => {
                    hljs.highlightBlock(block);
                });
            });
            
            // 渲染数学公式
            renderMathInElement(document.body, {
                delimiters: [
                    {left: '$$', right: '$$', display: true},
                    {left: '$', right: '$', display: false}
                ]
            });
        });
    </script>
</body>
</html> 