<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>上传文档 - {{ database_name }}</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <style>
        .upload-container {
            max-width: 800px;
            margin: 50px auto;
            padding: 30px;
            background: #f8f9fa;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .drag-drop-area {
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            padding: 2rem;
            text-align: center;
            margin: 20px 0;
            background: white;
            cursor: pointer;
        }
        .file-list {
            margin-top: 20px;
        }
        .btn-custom {
            background-color: #1890ff;
            color: white;
            padding: 10px 25px;
            border-radius: 4px;
            border: none;
        }
        .btn-custom:hover {
            background-color: #096dd9;
        }
    </style>
</head>
<body>
    <div class="upload-container">
        <h2 class="mb-4">上传文档到知识库</h2>
        <h4 class="text-muted mb-4">知识库名称：{{ database_name }}</h4>
        
        <div class="drag-drop-area" id="dropZone">
            <!-- 新增选择文件按钮 -->
            <div class="d-grid gap-2 mb-3">
                <button type="button" class="btn btn-custom" onclick="$('#fileInput').click()">
                    选择文件
                </button>
            </div>
            或
            <p>拖放文件至此区域</p>
            <input type="file" id="fileInput" class="d-none">
        </div>
        
        <div class="file-list" id="fileList"></div>
        
        <div class="d-grid gap-2">
            <button class="btn btn-custom" onclick="uploadFile()">开始上传</button>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        // 初始化拖放功能
        const dropZone = $('#dropZone');
        const fileInput = $('#fileInput');



        // 拖放事件处理
        dropZone.on('dragover', function(e) {
            e.preventDefault();
            dropZone.css('border-color', '#1890ff');
        });

        dropZone.on('dragleave', function(e) {
            e.preventDefault();
            dropZone.css('border-color', '#dee2e6');
        });

        dropZone.on('drop', function(e) {
            e.preventDefault();
            dropZone.css('border-color', '#dee2e6');
            const files = e.originalEvent.dataTransfer.files;
            if (files.length > 0) {
                fileInput[0].files = files;
                updateFileList(files[0]);
            }
        });

        // 文件选择处理
        fileInput.on('change', function(e) {
            if (this.files && this.files[0]) {
                updateFileList(this.files[0]);
            }
        });

        // 更新文件列表显示
        function updateFileList(file) {
            $('#fileList').html(`
                <div class="alert alert-info">
                    <i class="bi bi-file-earmark"></i> 
                    ${file.name} (${(file.size/1024/1024).toFixed(2)}MB)
                </div>
            `);
        }

        // 文件上传逻辑
        function uploadFile() {
            const file = fileInput[0].files[0];
            if (!file) {
                alert('请先选择文件');
                return;
            }

            // 新增加载状态
            $('#fileList').html(`
                <div class="alert alert-info d-flex align-items-center">
                    <div class="spinner-border me-2" role="status"></div>
                    上传中，请稍候...
                </div>
            `);
            $('button').prop('disabled', true); // 禁用按钮

            const formData = new FormData();
            formData.append('file', file);
            formData.append('database_id', '{{ database_id }}'); // 添加数据库ID参数

            $.ajax({
                url: '/api/files/database/upload',
                type: 'POST',
                data: formData,
                contentType: false,
                processData: false,
                headers: {
                    'Authorization': 'Bearer {{ token }}'
                },
                success: function(response) {
                    if (response.code === 200) {  // 修改为数字类型比较
                        $('#fileList').html(`
                            <div class="alert alert-success">
                                ${response.message} 3秒后自动跳转...
                            </div>
                        `);
                        setTimeout(() => {
                            window.location.href = '/api/files/database/upload_to/'+'{{ database_id }}/'+'{{token}}';
                        }, 3000);
                    } else {  // 添加非200状态处理
                        $('#fileList').html(`
                            <div class="alert alert-warning">
                                ${response.message}
                            </div>
                        `);
                    }
                },
                error: function(xhr) {
                    $('button').prop('disabled', false); // 恢复按钮
                    const response = xhr.responseJSON || {};
                    const errorMsg = response.message || '上传失败，请检查网络连接';
                    $('#fileList').html(`
                        <div class="alert alert-danger">
                            ${errorMsg}${response.code ? ` (错误码: ${response.code})` : ''}
                        </div>
                    `);
                }
            });
        }
    </script>
</body>
</html>