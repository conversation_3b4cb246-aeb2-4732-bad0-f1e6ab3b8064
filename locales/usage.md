# 目录说明

> 本目录是国际化语言包目录，通过Babel提取项目中的中文到语言包中进行翻译。

- 步骤
1. 在代码中需要翻译的地方调用get_translator函数
2. 在项目顶级目录执行以下命令
```shell
# 提取翻译字符串
pybabel extract -F babel.cfg -o messages.pot .
```
```shell
# 初始化翻译语言(示例：英语)
pybabel init -i messages.pot -d locales -l en(参照翻译语言标准按需修改，如英语为en)
```
>如果已经初始化过翻译语言，则执行更新
pybabel update -i messages.pot -d locales -l en

3. 编辑 locales/相应语言文件夹/LC_MESSAGES/messages.po 添加翻译
4. 编译为 .mo 文件
```shell
pybabel compile -d locales
```
