# Chinese translations for PROJECT.
# Copyright (C) 2025 ORGANIZATION
# This file is distributed under the same license as the PROJECT project.
# <AUTHOR> <EMAIL>, 2025.
#
msgid ""
msgstr ""
"Project-Id-Version: PROJECT VERSION\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-07-15 18:27+0800\n"
"PO-Revision-Date: 2025-07-15 18:27+0800\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language: zh\n"
"Language-Team: zh <<EMAIL>>\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

#: app/utils/llm.py:63 app/utils/llm.py:1590 app/utils/llm.py:1656
msgid "<PERSON><PERSON><PERSON> (Paid)"
msgstr "通义千问（收费）"


#: app/utils/llm.py:67 app/utils/llm.py:1243 app/utils/llm.py:1669
#: app/utils/llm.py:2056

msgid "Tongyi Qianwen - Vision (Paid)"
msgstr "通义千问-视觉（收费）"

#: app/utils/llm.py:71 app/utils/llm.py:1548 app/utils/llm.py:1678
#: app/utils/llm.py:1856
msgid "Tongyi Qianwen - Long Text (Paid)"
msgstr "通义千问-长文本（收费）"


#: app/utils/llm.py:75
msgid "Tongyi Qianwen - Text-to-Image (Paid)"
msgstr "通义千问-文生图（收费）"


#: app/utils/llm.py:79
msgid "Tongyi Qianwen - Text-to-Video (Paid)"
msgstr "通义千问-文生视频（收费）"


#: app/utils/llm.py:83 app/utils/llm.py:1724
msgid "DeepSeek (Paid)"
msgstr "DeepSeek（收费）"


#: app/utils/llm.py:87 app/utils/llm.py:910 app/utils/llm.py:1708
msgid "Tongyi Qianwen-32B (Free)"
msgstr "通义千问-32B（免费）"

msgid "DeepTalk (Recommend)"
msgstr "DeepTalk（推荐）"