# Translations template for PROJECT.
# Copyright (C) 2025 ORGANIZATION
# This file is distributed under the same license as the PROJECT project.
# <AUTHOR> <EMAIL>, 2025.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PROJECT VERSION\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-07-17 12:44+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

#: app/routes/admin_route.py:22 app/routes/admin_route.py:34
msgid "姓名"
msgstr ""

#: app/routes/admin_route.py:22 app/routes/admin_route.py:36
msgid "邮箱"
msgstr ""

#: app/routes/admin_route.py:22 app/routes/admin_route.py:35
msgid "手机号"
msgstr ""

#: app/routes/admin_route.py:22 app/routes/admin_route.py:37
msgid "密码"
msgstr ""

#: app/routes/admin_route.py:54
#, python-brace-format
msgid "成功创建{count}用户并建立好友关系"
msgstr ""

#: app/routes/admin_route.py:56
#, python-brace-format
msgid "处理文件时出错: {error}"
msgstr ""

#: app/routes/auth.py:44 app/routes/auth.py:46
msgid "通讯成功"
msgstr ""

#: app/routes/auth.py:57
msgid "邮箱已注册"
msgstr ""

#: app/routes/auth.py:76 app/routes/auth.py:112 app/routes/auth.py:348
msgid "验证码已发送"
msgstr ""

#: app/routes/auth.py:79 app/routes/auth.py:115 app/routes/auth.py:215
#: app/routes/auth.py:298 app/routes/auth.py:321 app/routes/auth.py:351
#: app/routes/auth.py:388 app/routes/collection_route.py:100
#: app/routes/collection_route.py:140 app/routes/collection_route.py:197
#: app/routes/collection_route.py:239 app/routes/collection_route.py:292
#: app/routes/database_route.py:156 app/routes/database_route.py:242
#: app/routes/database_route.py:309 app/routes/database_route.py:369
#: app/routes/database_route.py:435 app/routes/database_route.py:510
#: app/routes/database_route.py:551 app/routes/database_route.py:626
#: app/routes/database_route.py:688 app/routes/database_route.py:848
#: app/routes/database_route.py:885 app/routes/file_route.py:89
#: app/routes/file_route.py:263 app/routes/file_route.py:344
#: app/routes/file_route.py:398 app/routes/organization_route.py:43
#: app/routes/organization_route.py:78 app/routes/organization_route.py:95
#: app/routes/organization_route.py:116 app/routes/organization_route.py:166
#: app/routes/organization_route.py:206 app/routes/organization_route.py:248
#: app/routes/user_route.py:71 app/routes/user_route.py:107
#: app/routes/user_route.py:153 app/routes/user_route.py:192
#: app/routes/user_route.py:261 app/routes/user_route.py:347
#: app/routes/user_route.py:453 app/routes/user_route.py:515
#: app/routes/user_route.py:533 app/routes/user_route.py:559
#: app/routes/user_route.py:591
#, python-brace-format
msgid "服务端遇到未知异常：{}"
msgstr ""

#: app/routes/auth.py:140 app/routes/auth.py:200 app/routes/auth.py:246
msgid "验证码错误"
msgstr ""

#: app/routes/auth.py:228 app/routes/user_route.py:134
#: app/routes/user_route.py:137
msgid "用户名已被使用"
msgstr ""

#: app/routes/auth.py:235
msgid "手机号已注册"
msgstr ""

#: app/routes/auth.py:295
msgid "注册成功"
msgstr ""

#: app/routes/auth.py:307 app/routes/auth.py:331 app/routes/auth.py:359
#: app/routes/user_route.py:576
msgid "用户不存在"
msgstr ""

#: app/routes/auth.py:309
msgid "邮箱或密码错误"
msgstr ""

#: app/routes/auth.py:312
msgid "账号未激活"
msgstr ""

#: app/routes/auth.py:371
msgid "验证码错误或已过期"
msgstr ""

#: app/routes/auth.py:385
msgid "密码重置成功"
msgstr ""

#: app/routes/chat.py:69 app/routes/collection_route.py:45
#: app/routes/file_route.py:178 app/routes/file_route.py:291
#: app/routes/file_route.py:654 app/routes/group_route.py:554
#: app/routes/group_route.py:590 app/routes/group_route.py:630
#: app/routes/group_route.py:736 app/routes/group_route.py:831
#: app/routes/group_route.py:874 app/routes/group_route.py:914
#: app/routes/group_route.py:1381
msgid "群组不存在"
msgstr ""

#: app/routes/chat.py:75 app/routes/group_route.py:633
#: app/routes/group_route.py:917
msgid "群组已解散"
msgstr ""

#: app/routes/chat.py:79 app/routes/group_route.py:635
#: app/routes/group_route.py:919
msgid "对方已删除聊天"
msgstr ""

#: app/routes/chat.py:88
msgid "你不在该群组中"
msgstr ""

#: app/routes/chat.py:157
msgid "撤回了一条消息"
msgstr ""

#: app/routes/chat.py:171
msgid "消息已撤回"
msgstr ""

#: app/routes/chat.py:193 app/routes/chat.py:206
msgid "已删除"
msgstr ""

#: app/routes/chat.py:220
msgid "以下是新话题"
msgstr ""

#: app/routes/chat.py:256 app/routes/group_route.py:1208
msgid "消息不存在"
msgstr ""

#: app/routes/chat.py:258
msgid "无权限停止该消息输出"
msgstr ""

#: app/routes/chat.py:263
msgid "已停止输出"
msgstr ""

#: app/routes/chat.py:268
msgid "停止消息输出失败"
msgstr ""

#: app/routes/collection_route.py:39
msgid "收藏内容不能为空"
msgstr ""

#: app/routes/collection_route.py:50
msgid "只有群主才能添加对话记录到收藏"
msgstr ""

#: app/routes/collection_route.py:62
#, python-brace-format
msgid "未找到v_name为{}的数据库"
msgstr ""

#: app/routes/collection_route.py:70
msgid "非文字收藏"
msgstr ""

#: app/routes/collection_route.py:97
msgid "收藏成功"
msgstr ""

#: app/routes/collection_route.py:119 app/routes/collection_route.py:216
msgid "未找到对应的收藏记录"
msgstr ""

#: app/routes/collection_route.py:125
msgid "您没有权限删除此收藏记录"
msgstr ""

#: app/routes/collection_route.py:137 app/routes/database_route.py:685
#: app/routes/user_route.py:188
msgid "删除成功"
msgstr ""

#: app/routes/collection_route.py:152 app/routes/collection_route.py:251
#: app/routes/database_route.py:169 app/routes/database_route.py:254
#: app/routes/database_route.py:322 app/routes/database_route.py:382
#: app/routes/database_route.py:448 app/routes/database_route.py:810
#: app/routes/database_route.py:861 app/routes/group_route.py:932
#: app/routes/group_route.py:1237 app/routes/group_route.py:1305
#: app/routes/user_route.py:274 app/routes/user_route.py:310
msgid "每页数量不能超过50"
msgstr ""

#: app/routes/collection_route.py:162 app/routes/group_route.py:207
#: app/routes/group_route.py:245 app/routes/group_route.py:297
#: app/routes/group_route.py:339 app/routes/stt_route.py:59
#: app/routes/tts_route.py:43
msgid "群组不存在或已解散"
msgstr ""

#: app/routes/database_route.py:63 app/routes/database_route.py:69
#, python-brace-format
msgid "{}群文档知识库"
msgstr ""

#: app/routes/database_route.py:84 app/routes/database_route.py:90
#, python-brace-format
msgid "{}群收藏知识库"
msgstr ""

#: app/routes/database_route.py:104 app/routes/database_route.py:110
#, python-brace-format
msgid "{}群对话记录库"
msgstr ""

#: app/routes/database_route.py:122
msgid "没有查询到绑定群组"
msgstr ""

#: app/routes/database_route.py:152 app/routes/group_route.py:140
msgid "创建成功"
msgstr ""

#: app/routes/database_route.py:455 app/routes/database_route.py:531
#: app/routes/database_route.py:570 app/routes/database_route.py:705
#: app/routes/database_route.py:757
msgid "知识库不存在"
msgstr ""

#: app/routes/database_route.py:464
msgid "权限不足"
msgstr ""

#: app/routes/database_route.py:580
msgid "无权修改此知识库"
msgstr ""

#: app/routes/database_route.py:622 app/routes/user_route.py:150
msgid "更新成功"
msgstr ""

#: app/routes/database_route.py:641 app/routes/file_route.py:56
msgid "数据库不存在"
msgstr ""

#: app/routes/database_route.py:645
msgid "您无权限删除此共享知识库"
msgstr ""

#: app/routes/database_route.py:734 app/routes/group_route.py:848
msgid "绑定成功"
msgstr ""

#: app/routes/database_route.py:784 app/routes/group_route.py:891
msgid "解绑成功"
msgstr ""

#: app/routes/database_route.py:790
msgid "解绑失败"
msgstr ""

#: app/routes/file_route.py:62
msgid "您没有权限访问该数据库"
msgstr ""

#: app/routes/file_route.py:81
msgid "后台上传中"
msgstr ""

#: app/routes/file_route.py:181 app/routes/file_route.py:294
#: app/routes/file_route.py:657
msgid "您没有权限访问该群聊"
msgstr ""

#: app/routes/file_route.py:257 app/routes/file_route.py:338
#: app/routes/file_route.py:391
msgid "上传成功"
msgstr ""

#: app/routes/file_route.py:413 app/routes/file_route.py:495
msgid "文件不存在"
msgstr ""

#: app/routes/file_route.py:500
msgid "文件未关联到知识库"
msgstr ""

#: app/routes/file_route.py:510
msgid "关联的数据库不存在"
msgstr ""

#: app/routes/file_route.py:518
msgid "无操作权限"
msgstr ""

#: app/routes/file_route.py:528
msgid "文件删除成功"
msgstr ""

#: app/routes/file_route.py:553
msgid "原始文件不存在"
msgstr ""

#: app/routes/file_route.py:560
msgid "无群组访问权限"
msgstr ""

#: app/routes/file_route.py:571
msgid "无数据库访问权限"
msgstr ""

#: app/routes/file_route.py:603
msgid "文件已加入上传队列"
msgstr ""

#: app/routes/file_route.py:724
msgid "批量上传成功"
msgstr ""

#: app/routes/group_route.py:88
msgid "不能创建与默认群聊同名群聊"
msgstr ""

#: app/routes/group_route.py:145
msgid "创建群组出错"
msgstr ""

#: app/routes/group_route.py:201
#, python-brace-format
msgid "用户{}已在群聊中，不能重复添加"
msgstr ""

#: app/routes/group_route.py:204
#, python-brace-format
msgid "用户{}不存在或已注销"
msgstr ""

#: app/routes/group_route.py:211
msgid "非群主不能添加用户到群聊"
msgstr ""

#: app/routes/group_route.py:215 app/routes/group_route.py:259
#: app/routes/group_route.py:305 app/routes/group_route.py:557
#: app/routes/group_route.py:593 app/routes/group_route.py:638
#: app/routes/stt_route.py:62 app/routes/tts_route.py:45
msgid "您不在该群组中"
msgstr ""

#: app/routes/group_route.py:223
msgid "添加成功"
msgstr ""

#: app/routes/group_route.py:228
msgid "添加用户到群组出错"
msgstr ""

#: app/routes/group_route.py:252
msgid "非群主不能将用户移出群聊"
msgstr ""

#: app/routes/group_route.py:262
msgid "不能将群主移出群聊"
msgstr ""

#: app/routes/group_route.py:270
msgid "移除成功"
msgstr ""

#: app/routes/group_route.py:276
msgid "将用户移除出群组出错"
msgstr ""

#: app/routes/group_route.py:301
msgid "群主不能退出群组"
msgstr ""

#: app/routes/group_route.py:313
msgid "退出群组成功"
msgstr ""

#: app/routes/group_route.py:318
msgid "退出群组出错"
msgstr ""

#: app/routes/group_route.py:342
msgid "不允许解散默认群聊"
msgstr ""

#: app/routes/group_route.py:347
msgid "你没有权限解散该群组"
msgstr ""

#: app/routes/group_route.py:362
msgid "该聊天已删除"
msgstr ""

#: app/routes/group_route.py:364
msgid "解散群组成功"
msgstr ""

#: app/routes/group_route.py:369
msgid "解散群组出错"
msgstr ""

#: app/routes/group_route.py:446 app/utils/llm.py:1661
msgid "name"
msgstr ""

#: app/routes/group_route.py:534
msgid "获取群组列表出错"
msgstr ""

#: app/routes/group_route.py:565 app/routes/group_route.py:606
msgid "记录成功"
msgstr ""

#: app/routes/group_route.py:570
msgid "一键已读出错"
msgstr ""

#: app/routes/group_route.py:604
msgid "消息已标记为已读或不存在"
msgstr ""

#: app/routes/group_route.py:612
msgid "记录已读消息成员出错"
msgstr ""

#: app/routes/group_route.py:705 app/utils/llm.py:933
msgid "agent_settingmodel"
msgstr ""

#: app/routes/group_route.py:712
msgid "获取群组出错"
msgstr ""

#: app/routes/group_route.py:727
msgid "群组名称不能为空"
msgstr ""

#: app/routes/group_route.py:740 app/routes/group_route.py:837
#: app/routes/group_route.py:880
msgid "只有群主能修改群信息"
msgstr ""

#: app/routes/group_route.py:747
msgid "不允许修改默认群聊名称"
msgstr ""

#: app/routes/group_route.py:754
msgid "不允许修改为默认群聊名称"
msgstr ""

#: app/routes/group_route.py:760
msgid "默认群聊不允许添加其他用户"
msgstr ""

#: app/routes/group_route.py:766
msgid "默认群聊不允许移交"
msgstr ""

#: app/routes/group_route.py:769
msgid "群聊成员不能为空"
msgstr ""

#: app/routes/group_route.py:805
msgid "修改成功"
msgstr ""

#: app/routes/group_route.py:812
msgid "修改群信息出错"
msgstr ""

#: app/routes/group_route.py:855
msgid "绑定群组知识库出错"
msgstr ""

#: app/routes/group_route.py:898
msgid "解绑群知识库出错"
msgstr ""

#: app/routes/group_route.py:992 app/routes/group_route.py:1104
msgid "获取对话记录失败"
msgstr ""

#: app/routes/group_route.py:1007
msgid "请选择要转存的对话记录"
msgstr ""

#: app/routes/group_route.py:1021
msgid "转存对话记录出错"
msgstr ""

#: app/routes/group_route.py:1035 app/routes/group_route.py:1070
msgid "分享记录不存在"
msgstr ""

#: app/routes/group_route.py:1051
msgid "获取转存对话记录出错"
msgstr ""

#: app/routes/group_route.py:1216
msgid "评估记录成功"
msgstr ""

#: app/routes/group_route.py:1222
msgid "评估记录出错"
msgstr ""

#: app/routes/group_route.py:1291 app/routes/group_route.py:1331
#: app/routes/group_route.py:1361 app/routes/group_route.py:1407
msgid "搜索群组失败"
msgstr ""

#: app/routes/llm_route.py:50 app/routes/llm_route.py:89
msgid "模型别名已存在，请使用其他别名"
msgstr ""

#: app/routes/llm_route.py:53 app/routes/llm_route.py:93
msgid "模型别名不能与系统默认模型名称相同"
msgstr ""

#: app/routes/llm_route.py:68 app/routes/llm_route.py:112
msgid "模型配置保存成功"
msgstr ""

#: app/routes/llm_route.py:72 app/routes/llm_route.py:116
msgid "保存模型配置失败，请检查参数格式"
msgstr ""

#: app/routes/llm_route.py:110
msgid "模型不存在"
msgstr ""

#: app/routes/llm_route.py:134
msgid "模型不存在或已删除"
msgstr ""

#: app/routes/llm_route.py:136
msgid "模型删除成功"
msgstr ""

#: app/routes/llm_route.py:141
msgid "删除模型失败"
msgstr ""

#: app/routes/llm_route.py:157
msgid "获取模型列表失败"
msgstr ""

#: app/routes/organization_route.py:28
msgid "只有管理员可以创建组织"
msgstr ""

#: app/routes/organization_route.py:33
msgid "组织代码已存在"
msgstr ""

#: app/routes/organization_route.py:91 app/routes/organization_route.py:110
#: app/routes/organization_route.py:134 app/routes/organization_route.py:180
msgid "组织不存在"
msgstr ""

#: app/routes/organization_route.py:107
msgid "只有管理员可以删除组织"
msgstr ""

#: app/routes/organization_route.py:113
msgid "组织已删除"
msgstr ""

#: app/routes/organization_route.py:129
msgid "只有管理员可以创建部门"
msgstr ""

#: app/routes/organization_route.py:140
msgid "父部门不存在"
msgstr ""

#: app/routes/organization_route.py:218
msgid "只有管理员可以删除部门"
msgstr ""

#: app/routes/organization_route.py:221
msgid "部门不存在"
msgstr ""

#: app/routes/organization_route.py:226
msgid "请先删除子部门"
msgstr ""

#: app/routes/organization_route.py:231
msgid "请先移除部门下的用户"
msgstr ""

#: app/routes/organization_route.py:245
msgid "部门已删除"
msgstr ""

#: app/routes/stt_route.py:33
msgid "未能识别任何内容，请检查音频文件。"
msgstr ""

#: app/routes/stt_route.py:35
msgid "语音识别完成"
msgstr ""

#: app/routes/stt_route.py:38
#, python-brace-format
msgid "识别过程中发生错误: {err}"
msgstr ""

#: app/routes/stt_route.py:70 app/routes/tts_route.py:53
msgid "未找到对应的消息记录"
msgstr ""

#: app/routes/stt_route.py:87 app/routes/stt_route.py:168
msgid "识别内容获取成功"
msgstr ""

#: app/routes/stt_route.py:102
msgid "重新识别中..."
msgstr ""

#: app/routes/stt_route.py:102 app/routes/stt_route.py:118
msgid "识别中..."
msgstr ""

#: app/routes/stt_route.py:125 app/routes/stt_route.py:153
msgid "识别异常，后台管理员排查中"
msgstr ""

#: app/routes/stt_route.py:146
#, python-brace-format
msgid "已识别成功,请查看识别结果文档{filename}"
msgstr ""

#: app/routes/stt_route.py:149
msgid "已识别成功"
msgstr ""

#: app/routes/stt_route.py:172
#, python-brace-format
msgid "获取识别内容时发生错误: {err}"
msgstr ""

#: app/routes/tts_route.py:36
msgid "用户不存在或未激活"
msgstr ""

#: app/routes/tts_route.py:57
msgid "语音文件已存在"
msgstr ""

#: app/routes/tts_route.py:80
msgid "语音文件生成成功"
msgstr ""

#: app/routes/tts_route.py:84
msgid "生成语音文件失败"
msgstr ""

#: app/routes/user_route.py:54 app/routes/user_route.py:60
#: app/routes/user_route.py:127
msgid "未查询到用户信息"
msgstr ""

#: app/routes/user_route.py:124
msgid "无权修改信息"
msgstr ""

#: app/routes/user_route.py:145
msgid "手机号已被使用"
msgstr ""

#: app/routes/user_route.py:166
msgid "不能删除自己"
msgstr ""

#: app/routes/user_route.py:175 app/routes/user_route.py:209
msgid "目标用户不存在"
msgstr ""

#: app/routes/user_route.py:179
msgid "该用户不是您的好友"
msgstr ""

#: app/routes/user_route.py:204
msgid "不能申请添加自己"
msgstr ""

#: app/routes/user_route.py:213
msgid "该用户已经是您的好友"
msgstr ""

#: app/routes/user_route.py:222
msgid "您已经发送过好友请求"
msgstr ""

#: app/routes/user_route.py:257
msgid "好友请求已发送"
msgstr ""

#: app/routes/user_route.py:360
msgid "无效的操作类型"
msgstr ""

#: app/routes/user_route.py:370
msgid "未找到该好友请求"
msgstr ""

#: app/routes/user_route.py:410
msgid "已接受好友请求"
msgstr ""

#: app/routes/user_route.py:446
msgid "已拒绝好友请求"
msgstr ""

#: app/routes/user_route.py:526
msgid "用户不存在或已注销"
msgstr ""

#: app/routes/user_route.py:530
msgid "注销成功"
msgstr ""

#: app/routes/user_route.py:556
msgid "token已失效"
msgstr ""

#: app/utils/file_handler.py:244 app/utils/file_handler.py:275
#, python-brace-format
msgid "文档《{file_name}》内容：{content}"
msgstr ""

#: app/utils/llm.py:63 app/utils/llm.py:1606 app/utils/llm.py:1672
msgid "通义千问（收费）"
msgstr ""

#: app/utils/llm.py:67 app/utils/llm.py:1260 app/utils/llm.py:1685
#: app/utils/llm.py:2072
msgid "通义千问-视觉（收费）"
msgstr ""

#: app/utils/llm.py:71 app/utils/llm.py:1565 app/utils/llm.py:1694
#: app/utils/llm.py:1872
msgid "通义千问-长文本（收费）"
msgstr ""

#: app/utils/llm.py:75
msgid "通义千问-文生图（收费）"
msgstr ""

#: app/utils/llm.py:79
msgid "通义千问-文生视频（收费）"
msgstr ""

#: app/utils/llm.py:83 app/utils/llm.py:1740
msgid "DeepSeek（收费）"
msgstr ""

#: app/utils/llm.py:87 app/utils/llm.py:924 app/utils/llm.py:1724
msgid "通义千问-32B（免费）"
msgstr ""

#: app/utils/llm.py:209
msgid "你是一个智能助手,你需要根据用户的问题,整理上下文的内容输出回复,如果上下文不足以回复用户的问题,请你自行补充。另外这是在多人群聊里,会有很多消息。每条消息都以【时间】【用户名】为前缀,请根据【】中内容识别时间和发言者"
msgstr ""

#: app/utils/llm.py:213 app/utils/llm.py:285
#, python-brace-format
msgid ""
"{}\n"
"                            {}\n"
"                            注意：                            "
"1.除非用户在问题中有明确要求,否则query及prompt只作为参考内容内容,;                            "
"2.若因上下文不完整,相关性不足,或有矛盾而影响解答,你可自行解答,或补充后解答;                            "
"3.遇到指代不明时可合理推断;                            "
"4.由于是多人参与的对话,所以用户问题如果与之前的对话内容无关,请忽略之前的对话内容;                            {}"
"\n"
"                            {}"
msgstr ""

#: app/utils/llm.py:222 app/utils/llm.py:294
#, python-brace-format
msgid "当前查询时间:{}"
msgstr ""

#: app/utils/llm.py:223 app/utils/llm.py:295
#, python-brace-format
msgid "`用户的问题`:{}"
msgstr ""

#: app/utils/llm.py:224 app/utils/llm.py:296
msgid "**只回答结论**"
msgstr ""

#: app/utils/llm.py:224 app/utils/llm.py:296
msgid "**回答完整内容**"
msgstr ""

#: app/utils/llm.py:225 app/utils/llm.py:297
msgid "**如果你接收到的问题存在影响回答质量的干扰因素,请提示用户。情况严重时可不必强行输出答案。**"
msgstr ""

#: app/utils/llm.py:279
msgid "我是一个基于本地RAG的多人参与的对话Agent,因此,我发送给你的内容中会包括`上下文context结构`,请你根据`用户的问题`,整理`上下文context结构`的内容输出回复,如果`上下文context结构`不足以回复`用户的问题`,请你自行补充"
msgstr ""

#: app/utils/llm.py:281
msgid "你是一个智能助手,你需要根据用户的问题,整理上下文的内容输出回复,如果上下文不足以回复用户的问题,请你自行补充。"
msgstr ""

#: app/utils/llm.py:305
#, python-brace-format
msgid ""
"{}\n"
"上下文context结构:{}\n"
msgstr ""

#: app/utils/llm.py:315
msgid "你好"
msgstr ""

#: app/utils/llm.py:415 app/utils/llm.py:2053
msgid "你是图片识别模型,请根据图片内容和问题,输出回答。"
msgstr ""

#: app/utils/llm.py:872
#, python-brace-format
msgid ""
"你是一个多function的Agent系统的路由中枢,你需要根据如下要求解答用户问题\n"
"                        (1)对于常识性或公共信息的问题,你可直接回答,无需调用其他function;\n"
"                        (2)对于典型的用户私域问题,调用本地信息混合检索函数function A;\n"
"                        (3)对于时效性问题(例如新闻,天气等),调用联网查询function B;\n"
"                        (4)如果你无法判断应该调用function A或B,可以同时调用这两个function;\n"
"                        注意:\n"
"                        (1)你作为路由中枢,不能自造function,也不能自行修改function A和B函数名;\n"
"                        "
"(2)你调用function时必须严格遵循格式:{'name':'','arguments':''}输出\n"
"                        "
msgstr ""

#: app/utils/llm.py:935 app/utils/llm.py:1241
msgid "当前群聊未设置默认AI服务,请联系群主设置后再试。"
msgstr ""

#: app/utils/llm.py:944
#, python-brace-format
msgid ""
"# query - 引用：{}\n"
"- 问题：{} \n"
msgstr ""

#: app/utils/llm.py:1078
msgid ""
"此消息来自一个多人(含AI)多群组,具备RAG能力的 Chat Agent;role为tool的内容为function "
"calls返回的内容,包括：\n"
"                            1.\"search_group_chat_history\"：是群组对话历史查询记录;\n"
"                            2.\"web_search\":是来自互联网查询的结果;\n"
"                            3.\"search_graph\":知识图谱搜索结果;\n"
"                            注意：\n"
"                            1.以上function calls的返回内容,不分优先级;\n"
"                            2.function "
"calls的返回内容,如果可以完整且准确的回答用户问题,你可以直接基于这些内容,组织语言回答;\n"
"                            3.function "
"calls的返回内容,如果无效,或为空,或不充分,或有问题,你需要根据自己的知识进行完善并回答;\n"
"                            4.由于是多人参与的Chat "
"Agent,所以,user会指代多人,而真实用户名称,以user的content里面【】中的用户名为准;且不同用户之间的问题可能不相关;\n"
"                            5.回答时间相关的问题时,请你根据用户消息里所携带的绝对时间,进行换算。\n"
"                            "
msgstr ""

#: app/utils/llm.py:1096
msgid "由于上下文过长,已截断部分上下文。\n"
msgstr ""

#: app/utils/llm.py:1118 app/utils/llm.py:1412 app/utils/llm.py:1961
#: app/utils/llm.py:2092
#, python-brace-format
msgid "{error},请重新选择模型"
msgstr ""

#: app/utils/llm.py:1124 app/utils/llm.py:1417 app/utils/llm.py:1966
msgid "公有云账号余额不足，请联系管理员充值。"
msgstr ""

#: app/utils/llm.py:1126 app/utils/llm.py:1419 app/utils/llm.py:1968
msgid "上下文过长，模型暂无法处理，正在优化中。"
msgstr ""

#: app/utils/llm.py:1128 app/utils/llm.py:1421 app/utils/llm.py:1970
#: app/utils/llm.py:2101
msgid "获取大模型回复异常，请稍后再试。"
msgstr ""

#: app/utils/llm.py:1362
msgid "通义千问-32B(免费)"
msgstr ""

#: app/utils/llm.py:1372 app/utils/llm.py:1941
msgid "由于上下文过长,已截断部分上下文。"
msgstr ""

#: app/utils/llm.py:1379
#, python-brace-format
msgid "我理解你的问题是:{question}。"
msgstr ""

#: app/utils/llm.py:1566
#, python-brace-format
msgid ""
"\n"
"                        你是一个文档分析专家,请根据文档内容,提取出文档的结构化内容,并输出结构化内容;\n"
"                        一、结构化内容要求:\n"
"                        1.你需要仔细阅读并理解文档内容,按照文档知识结构提取知识要点;\n"
"                        2.每个要点的标题,必须是文章原始内容;\n"
"                        二、输出要求\n"
"                        [\n"
"                            {\n"
"                                \"first_class_title\":str #一级要点标题\n"
"                            },\n"
"                            {\n"
"                                \"first_class_title\":str #一级要点标题\n"
"                            },\n"
"                            ....# 数量不限\n"
"                        ]\n"
"                        "
msgstr ""

#: app/utils/llm.py:1607
#, python-brace-format
msgid ""
"\n"
"                    你需要按照如下要求，对文档内容进行重构和输出；\n"
"                    一、重构要求\n"
"                    "
"1.你需要首先通读并理解文档,提取文档每个主要章节的标题,再以每个章节里的最小段落为基准,从每个段落里提取全部原子化的知识要点；\n"
"                    2.要求每个最小化段落至少一个知识要点,且每个知识要点至少提出一个问题,不能有遗漏！都需要输出；\n"
"                    3.针对每个问题的标准答案，不能引入任何此文档外的知识或数据，仅能来自于此文档；\n"
"                    4.需确保以FAQ形式解析,输出文档的全部内容,严禁自行删减或忽略任何内容;\n"
"                    5.对于表格内容，需完整输出，以便于后期检索；\n"
"                    "
"6.如果文档内容中,有代码行、或Linux命令、或表格,请无遗漏的提出相应问题,并在答案中包含这些内容,即使可能有相似或重复。\n"
"                    二、输出要求\n"
"                    ```json\n"
"                    [\n"
"                        {\n"
"                            \"first_class_title\": str # 知识要点\n"
"                            \"qa_set\" : [    # 覆盖知识要点所有内容的问题集\n"
"                                            {\n"
"                                                \"question\": str , # "
"针对知识要点的问题\n"
"                                                \"answer\": str , # "
"针对该问题的标准答案\n"
"                                            },\n"
"                                            {\n"
"                                                \"question\": str , # "
"针对知识要点的问题\n"
"                                                \"answer\": str , # "
"针对该问题的标准答案\n"
"                                            },\n"
"                                            ...# 数量不限\n"
"                                        ],\n"
"                        },\n"
"                    ]\n"
"                    ```\n"
"                    "
msgstr ""

#: app/utils/llm.py:1754
#, python-brace-format
msgid "未找到模型配置: {model_name}"
msgstr ""

#: app/utils/llm.py:1908
#, python-brace-format
msgid "【语音内容】{content}"
msgstr ""

#: app/utils/llm.py:1920
msgid "你是多模态分析模型,请根据提供的文档进行分析回答。"
msgstr ""

#: app/utils/llm.py:2047
#, python-brace-format
msgid "（系统提示：第{images}张图片因超过10MB限制已跳过）"
msgstr ""

#: app/utils/llm.py:2097
msgid "公有云账号余额不足,请联系管理员充值。"
msgstr ""

#: app/utils/llm.py:2099
msgid "上下文过长,模型暂无法处理,正在优化中。"
msgstr ""

#: app/utils/tools.py:40
msgid "当你想知道通过搜索引擎查询信息时非常有用。"
msgstr ""

#: app/utils/tools.py:47
msgid "搜索的信息"
msgstr ""

#: app/utils/tools.py:448
msgid "未知工具"
msgstr ""

#: app/utils/tools.py:929
msgid "音频文件不存在"
msgstr ""

#: app/utils/tools.py:964
msgid "检测到上下文或引用消息中含有未识别音频文件,识别后模型回复更精细哦！"
msgstr ""

