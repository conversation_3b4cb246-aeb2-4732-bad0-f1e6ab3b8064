#!/bin/bash

# 无限循环，每天凌晨 1:30 执行任务
while true; do
    # 获取当前时间
    CURRENT_TIME=$(date +"%H:%M")
    
    # 如果当前时间是 01:30，则执行任务
    if [ "$CURRENT_TIME" == "01:30" ]; then
        # 获取当前时间并格式化为文件名
        TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
        OUTPUT_FILE="/data/clean_vector/${TIMESTAMP}.log"
        
        # 执行命令并将输出保存到文件
        python3 app/utils/clean_vector.py > "$OUTPUT_FILE" 2>&1
        
        # 等待 60 秒，避免在 1:30 重复执行
        sleep 60
    else
        # 每分钟检查一次时间
        sleep 60
    fi
done

